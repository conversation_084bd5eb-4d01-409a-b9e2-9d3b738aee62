import requests
import matplotlib.pyplot as plt
from datetime import date, timedelta
import json

def get_historical_rates(start_date, end_date, from_curr, to_curr):
    url = f"https://api.frankfurter.app/{start_date}..{end_date}?from={from_curr}&to={to_curr}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        
        rates_history = data.get('rates', {})
        if not rates_history:
            return None, None
            
        dates = sorted(rates_history.keys())
        rates = [rates_history[d][to_curr] for d in dates]
        return dates, rates
        
    except requests.exceptions.RequestException as e:
        print(f"获取历史汇率失败: {e}")
        return None, None

def plot_currency_trend(dates, rates, from_curr, to_curr):
    plt.figure(figsize=(12, 6))
    plt.plot(dates, rates, linewidth=2, color='blue', marker='o', markersize=3)
    plt.title(f'{from_curr} 兑 {to_curr} 汇率趋势图', fontsize=16)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel(f'汇率 ({from_curr} -> {to_curr})', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    
    step = max(1, len(dates) // 10)
    plt.xticks(range(0, len(dates), step), [dates[i] for i in range(0, len(dates), step)])
    
    plt.tight_layout()
    plt.show()

def find_historical_low(rates, dates):
    min_rate = min(rates)
    min_index = rates.index(min_rate)
    min_date = dates[min_index]
    return min_rate, min_date

def find_trading_opportunities(rates, dates, days=30):
    opportunities = []
    
    for i in range(len(rates) - days):
        buy_rate = rates[i]
        buy_date = dates[i]
        
        future_rates = rates[i+1:i+days+1]
        if future_rates:
            max_future_rate = max(future_rates)
            max_index = future_rates.index(max_future_rate) + i + 1
            sell_date = dates[max_index]
            
            profit_percent = ((max_future_rate - buy_rate) / buy_rate) * 100
            
            if profit_percent > 1:
                opportunities.append({
                    'buy_date': buy_date,
                    'buy_rate': buy_rate,
                    'sell_date': sell_date,
                    'sell_rate': max_future_rate,
                    'profit_percent': profit_percent
                })
    
    return sorted(opportunities, key=lambda x: x['profit_percent'], reverse=True)

def currency_arbitrage_analysis(base_curr='CNY'):
    currencies = ['USD', 'JPY', 'EUR']
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    arbitrage_opportunities = []
    
    for curr1 in currencies:
        for curr2 in currencies:
            if curr1 != curr2:
                dates1, rates1 = get_historical_rates(start_date, end_date, base_curr, curr1)
                dates2, rates2 = get_historical_rates(start_date, end_date, curr1, curr2)
                dates3, rates3 = get_historical_rates(start_date, end_date, curr2, base_curr)
                
                direct_dates, direct_rates = get_historical_rates(start_date, end_date, base_curr, curr2)
                
                if all([dates1, rates1, dates2, rates2, dates3, rates3, direct_dates, direct_rates]):
                    if len(rates1) > 0 and len(rates2) > 0 and len(rates3) > 0 and len(direct_rates) > 0:
                        indirect_rate = rates1[-1] * rates2[-1] * rates3[-1]
                        direct_rate = direct_rates[-1]
                        
                        arbitrage_profit = ((1 / direct_rate) - (1 / indirect_rate)) / (1 / indirect_rate) * 100
                        
                        if abs(arbitrage_profit) > 0.1:
                            arbitrage_opportunities.append({
                                'path': f'{base_curr} -> {curr1} -> {curr2} -> {base_curr}',
                                'direct_path': f'{base_curr} -> {curr2}',
                                'indirect_rate': indirect_rate,
                                'direct_rate': direct_rate,
                                'arbitrage_profit': arbitrage_profit
                            })
    
    return arbitrage_opportunities

def analyze_volatility(rates):
    if len(rates) < 2:
        return 0
    
    changes = []
    for i in range(1, len(rates)):
        change = abs((rates[i] - rates[i-1]) / rates[i-1]) * 100
        changes.append(change)
    
    return sum(changes) / len(changes)

def find_support_resistance(rates, dates, window=5):
    support_levels = []
    resistance_levels = []
    
    for i in range(window, len(rates) - window):
        current_rate = rates[i]
        
        is_support = all(current_rate <= rates[j] for j in range(i-window, i+window+1))
        is_resistance = all(current_rate >= rates[j] for j in range(i-window, i+window+1))
        
        if is_support:
            support_levels.append({'date': dates[i], 'rate': current_rate})
        elif is_resistance:
            resistance_levels.append({'date': dates[i], 'rate': current_rate})
    
    return support_levels, resistance_levels

def main():
    print("=== 汇率分析系统 ===\n")
    
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    dates, rates = get_historical_rates(start_date, end_date, 'CNY', 'JPY')
    
    if dates and rates:
        print(f"获取到 {len(dates)} 天的汇率数据")
        
        plot_currency_trend(dates, rates, 'CNY', 'JPY')
        
        min_rate, min_date = find_historical_low(rates, dates)
        print(f"历史最低汇率: {min_rate:.4f} ({min_date})")
        
        opportunities = find_trading_opportunities(rates, dates, 30)
        print(f"发现 {len(opportunities)} 个交易机会")
        
        if opportunities:
            best_opp = opportunities[0]
            print(f"最佳机会: {best_opp['buy_date']} 买入 -> {best_opp['sell_date']} 卖出")
            print(f"盈利: {best_opp['profit_percent']:.2f}%")
        
        volatility = analyze_volatility(rates)
        print(f"平均波动率: {volatility:.2f}%")
        
        support, resistance = find_support_resistance(rates, dates)
        print(f"支撑位数量: {len(support)}, 阻力位数量: {len(resistance)}")
        
        arbitrage_ops = currency_arbitrage_analysis()
        if arbitrage_ops:
            print(f"套利机会: {len(arbitrage_ops)} 个")
        else:
            print("暂无套利机会")
    
    else:
        print("无法获取数据")

if __name__ == "__main__":
    main()
