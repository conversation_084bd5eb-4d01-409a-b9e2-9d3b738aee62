# Python数据可视化与算法：汇率趋势大揭秘

基于L2.ipynb课程内容为小学生设计的Python编程教学材料，涵盖API使用、数据可视化和算法思维。

## 文件结构

```
02/
├── teacher_demo/           # 老师演示代码
│   └── currency_analysis_demo.py
├── answer/                 # 标准答案
│   └── currency_analysis_answer.py
├── template/               # 学生模板
│   ├── simple/            # 简单难度（填空）
│   │   └── currency_analysis_simple.py
│   ├── medium/            # 中等难度（半框架）
│   │   └── currency_analysis_medium.py
│   └── hard/              # 困难难度（纯框架）
│       └── currency_analysis_hard.py
├── auto_grader.py         # 自动评分工具
└── README.md              # 说明文档
```

## 课程内容

### 核心知识点
1. **API文档阅读**：学习如何理解和使用Frankfurter汇率API
2. **HTTP请求**：使用requests库获取网络数据
3. **数据可视化**：matplotlib绘制汇率趋势图
4. **算法思维**：实现暴力搜索和贪心算法
5. **数据分析**：汇率波动性分析和交易机会发现

### 功能模块
- 历史汇率数据获取
- 汇率趋势图绘制
- 历史极值点查找
- 交易机会分析
- 波动率计算
- 多货币比较
- 套利机会分析（高级）

## 使用说明

### 老师演示代码
`teacher_demo/currency_analysis_demo.py`

完整的功能演示，包含详细注释和输出说明。老师可以运行此代码展示所有功能的实现效果。

```bash
cd teacher_demo
python currency_analysis_demo.py
```

### 学生模板

#### 简单难度
`template/simple/currency_analysis_simple.py`

- 适合初学者
- 代码几乎完整，只在关键位置挖空
- 学生需要填入正确的参数和变量名
- 文件末尾提供填空提示

#### 中等难度
`template/medium/currency_analysis_medium.py`

- 适合有一定基础的学生
- 提供函数框架和复杂算法逻辑
- 学生需要实现基本的数据处理和API调用
- 包含详细的TODO注释指导

#### 困难难度
`template/hard/currency_analysis_hard.py`

- 适合编程能力较强的学生
- 只提供类结构和函数签名
- 学生需要实现所有功能
- 包含可选的高级功能作为加分项

### 自动评分工具

`auto_grader.py`

专门用于评估困难难度学生代码的自动化测试系统。

#### 使用方法
```bash
python auto_grader.py <学生代码文件路径>
```

#### 评分标准
**基础功能（70分）**
- API数据获取：15分
- 趋势图绘制：15分
- 极值点查找：10分
- 交易机会分析：15分
- 波动率计算：10分
- 代码质量：5分

**可选高级功能（30分）**
- 套利分析：8分
- 技术分析：8分
- 预测功能：6分
- 风险评估：5分
- 数据导出：3分

#### 等级划分
- 90分以上：优秀
- 80-89分：良好
- 70-79分：中等
- 60-69分：及格
- 60分以下：不及格

## 环境要求

### Python版本
- Python 3.7+

### 依赖库
```bash
pip install requests matplotlib
```

### API说明
使用Frankfurter免费汇率API：
- 基础URL：https://api.frankfurter.app/
- 无需API密钥
- 支持历史汇率查询
- 数据更新及时

## 教学建议

### 课前准备
1. 确保学生环境已安装Python和必要库
2. 简单介绍API概念和HTTP请求
3. 复习matplotlib基础用法

### 教学流程
1. **演示阶段**：运行teacher_demo代码，展示完整功能
2. **理论讲解**：结合L2.ipynb内容讲解API文档阅读方法
3. **实践练习**：根据学生水平选择对应难度模板
4. **代码评估**：使用自动评分工具快速评估学生作业

### 分层教学
- **基础班**：使用简单难度模板，重点理解API调用和数据处理
- **提高班**：使用中等难度模板，培养独立编程能力
- **竞赛班**：使用困难难度模板，挑战高级算法实现

## 扩展功能

### 可选实现
1. **实时汇率监控**：定时获取最新汇率数据
2. **汇率预警系统**：设置阈值进行价格提醒
3. **多图表展示**：K线图、柱状图等多种可视化
4. **数据持久化**：将分析结果保存到文件
5. **Web界面**：使用Flask创建网页版本

### 进阶挑战
1. **机器学习预测**：使用简单的线性回归预测汇率
2. **情感分析**：结合新闻数据分析对汇率的影响
3. **量化交易策略**：实现更复杂的交易算法
4. **风险管理**：计算VaR、最大回撤等风险指标

## 注意事项

1. **网络连接**：确保能够访问外网API
2. **数据限制**：API有请求频率限制，避免过于频繁调用
3. **错误处理**：教导学生处理网络异常和数据异常
4. **代码规范**：强调良好的编程习惯和代码注释

## 联系支持

如有问题或建议，请联系课程负责老师。

---

*本教学材料基于真实的金融数据API设计，旨在让小学生在实践中学习编程，培养数据分析思维和算法解决问题的能力。*
