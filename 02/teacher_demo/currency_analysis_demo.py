import requests
import matplotlib.pyplot as plt
from datetime import date, timedelta, datetime
import json

def get_historical_rates(start_date, end_date, from_curr, to_curr):
    """获取历史汇率数据"""
    url = f"https://api.frankfurter.app/{start_date}..{end_date}?from={from_curr}&to={to_curr}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        
        rates_history = data.get('rates', {})
        if not rates_history:
            return None, None
            
        dates = sorted(rates_history.keys())
        rates = [rates_history[d][to_curr] for d in dates]
        return dates, rates
        
    except requests.exceptions.RequestException as e:
        print(f"获取历史汇率失败: {e}")
        return None, None

def plot_currency_trend(dates, rates, from_curr, to_curr):
    """绘制汇率趋势图"""
    plt.figure(figsize=(12, 6))
    plt.plot(dates, rates, linewidth=2, color='blue', marker='o', markersize=3)
    plt.title(f'{from_curr} 兑 {to_curr} 汇率趋势图', fontsize=16)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel(f'汇率 ({from_curr} -> {to_curr})', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    
    # 每隔一定间隔显示日期标签
    step = max(1, len(dates) // 10)
    plt.xticks(range(0, len(dates), step), [dates[i] for i in range(0, len(dates), step)])
    
    plt.tight_layout()
    plt.show()

def find_historical_low(rates, dates):
    """找到历史最低点"""
    min_rate = min(rates)
    min_index = rates.index(min_rate)
    min_date = dates[min_index]
    return min_rate, min_date

def find_trading_opportunities(rates, dates, days=30):
    """寻找交易机会（买低卖高）"""
    opportunities = []
    
    for i in range(len(rates) - days):
        buy_rate = rates[i]
        buy_date = dates[i]
        
        # 在接下来的days天内寻找最高点
        future_rates = rates[i+1:i+days+1]
        if future_rates:
            max_future_rate = max(future_rates)
            max_index = future_rates.index(max_future_rate) + i + 1
            sell_date = dates[max_index]
            
            profit_percent = ((max_future_rate - buy_rate) / buy_rate) * 100
            
            if profit_percent > 1:  # 只记录盈利超过1%的机会
                opportunities.append({
                    'buy_date': buy_date,
                    'buy_rate': buy_rate,
                    'sell_date': sell_date,
                    'sell_rate': max_future_rate,
                    'profit_percent': profit_percent
                })
    
    return sorted(opportunities, key=lambda x: x['profit_percent'], reverse=True)

def currency_arbitrage_analysis(base_curr='CNY'):
    """多货币套利分析"""
    currencies = ['USD', 'JPY', 'EUR']
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    arbitrage_opportunities = []
    
    for curr1 in currencies:
        for curr2 in currencies:
            if curr1 != curr2:
                # 直接兑换路径
                dates1, rates1 = get_historical_rates(start_date, end_date, base_curr, curr1)
                dates2, rates2 = get_historical_rates(start_date, end_date, curr1, curr2)
                dates3, rates3 = get_historical_rates(start_date, end_date, curr2, base_curr)
                
                # 间接兑换路径
                direct_dates, direct_rates = get_historical_rates(start_date, end_date, base_curr, curr2)
                
                if all([dates1, rates1, dates2, rates2, dates3, rates3, direct_dates, direct_rates]):
                    # 计算最近的套利机会
                    if len(rates1) > 0 and len(rates2) > 0 and len(rates3) > 0 and len(direct_rates) > 0:
                        indirect_rate = rates1[-1] * rates2[-1] * rates3[-1]
                        direct_rate = direct_rates[-1]
                        
                        arbitrage_profit = ((1 / direct_rate) - (1 / indirect_rate)) / (1 / indirect_rate) * 100
                        
                        if abs(arbitrage_profit) > 0.1:  # 套利机会超过0.1%
                            arbitrage_opportunities.append({
                                'path': f'{base_curr} -> {curr1} -> {curr2} -> {base_curr}',
                                'direct_path': f'{base_curr} -> {curr2}',
                                'indirect_rate': indirect_rate,
                                'direct_rate': direct_rate,
                                'arbitrage_profit': arbitrage_profit
                            })
    
    return arbitrage_opportunities

def main():
    """主函数演示所有功能"""
    print("=== Python数据可视化与算法: 汇率趋势大揭秘 ===\n")
    
    # 1. 获取并绘制汇率趋势
    print("1. 获取CNY兑JPY历史汇率数据...")
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    dates, rates = get_historical_rates(start_date, end_date, 'CNY', 'JPY')
    
    if dates and rates:
        print(f"成功获取 {len(dates)} 天的汇率数据")
        print(f"数据范围: {dates[0]} 到 {dates[-1]}")
        print(f"汇率范围: {min(rates):.4f} 到 {max(rates):.4f}")
        
        # 绘制趋势图
        plot_currency_trend(dates, rates, 'CNY', 'JPY')
        
        # 2. 寻找历史最低点
        print("\n2. 寻找历史最低点...")
        min_rate, min_date = find_historical_low(rates, dates)
        print(f"历史最低汇率: {min_rate:.4f} (日期: {min_date})")
        
        # 3. 寻找交易机会
        print("\n3. 寻找30天交易机会...")
        opportunities = find_trading_opportunities(rates, dates, 30)
        print(f"发现 {len(opportunities)} 个盈利机会")
        
        if opportunities:
            print("前5个最佳交易机会:")
            for i, opp in enumerate(opportunities[:5], 1):
                print(f"{i}. 买入日期: {opp['buy_date']}, 汇率: {opp['buy_rate']:.4f}")
                print(f"   卖出日期: {opp['sell_date']}, 汇率: {opp['sell_rate']:.4f}")
                print(f"   盈利: {opp['profit_percent']:.2f}%\n")
        
        # 4. 多货币套利分析
        print("4. 多货币套利分析...")
        arbitrage_ops = currency_arbitrage_analysis()
        
        if arbitrage_ops:
            print(f"发现 {len(arbitrage_ops)} 个套利机会:")
            for i, arb in enumerate(arbitrage_ops[:3], 1):
                print(f"{i}. 路径: {arb['path']}")
                print(f"   间接汇率: {arb['indirect_rate']:.6f}")
                print(f"   直接汇率: {arb['direct_rate']:.6f}")
                print(f"   套利收益: {arb['arbitrage_profit']:.3f}%\n")
        else:
            print("当前没有发现明显的套利机会")
    
    else:
        print("无法获取汇率数据，请检查网络连接")

if __name__ == "__main__":
    main()
