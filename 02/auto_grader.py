"""
汇率分析系统自动评分工具
用于评估学生提交的困难难度代码

评分标准：
基础功能（70分）：
- API数据获取：15分
- 趋势图绘制：15分
- 极值点查找：10分
- 交易机会分析：15分
- 波动率计算：10分
- 代码质量：5分

可选高级功能（30分）：
- 套利分析：8分
- 技术分析：8分
- 预测功能：6分
- 风险评估：5分
- 数据导出：3分
"""

import importlib.util
import sys
import os
import inspect
import traceback
from datetime import date, timedelta
import requests

class CurrencyAnalysisGrader:
    def __init__(self):
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        
    def load_student_module(self, file_path):
        """加载学生提交的代码模块"""
        try:
            spec = importlib.util.spec_from_file_location("student_code", file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return module, True
        except Exception as e:
            self.add_result("代码加载", 0, 5, f"代码无法加载: {str(e)}")
            return None, False
    
    def add_result(self, test_name, score, max_score, comment=""):
        """添加测试结果"""
        self.test_results.append({
            'test': test_name,
            'score': score,
            'max_score': max_score,
            'comment': comment
        })
        self.total_score += score
    
    def test_api_data_retrieval(self, module):
        """测试API数据获取功能（15分）"""
        score = 0
        max_score = 15
        
        try:
            # 检查是否有CurrencyAnalyzer类
            if hasattr(module, 'CurrencyAnalyzer'):
                analyzer = module.CurrencyAnalyzer()
                score += 3
                
                # 检查get_historical_rates方法
                if hasattr(analyzer, 'get_historical_rates'):
                    score += 3
                    
                    # 测试实际API调用
                    try:
                        end_date = date.today().strftime('%Y-%m-%d')
                        start_date = (date.today() - timedelta(days=7)).strftime('%Y-%m-%d')
                        dates, rates = analyzer.get_historical_rates(start_date, end_date, 'CNY', 'JPY')
                        
                        if dates and rates and len(dates) > 0 and len(rates) > 0:
                            score += 6
                            if len(dates) == len(rates):
                                score += 3
                        else:
                            self.add_result("API数据获取", score, max_score, "API调用成功但返回数据为空")
                            return
                            
                    except Exception as e:
                        self.add_result("API数据获取", score, max_score, f"API调用失败: {str(e)}")
                        return
                else:
                    self.add_result("API数据获取", score, max_score, "缺少get_historical_rates方法")
                    return
            else:
                # 检查是否有独立函数
                if hasattr(module, 'get_historical_rates'):
                    score += 6
                    try:
                        end_date = date.today().strftime('%Y-%m-%d')
                        start_date = (date.today() - timedelta(days=7)).strftime('%Y-%m-%d')
                        dates, rates = module.get_historical_rates(start_date, end_date, 'CNY', 'JPY')
                        
                        if dates and rates and len(dates) > 0 and len(rates) > 0:
                            score += 6
                            if len(dates) == len(rates):
                                score += 3
                    except Exception as e:
                        self.add_result("API数据获取", score, max_score, f"函数调用失败: {str(e)}")
                        return
                else:
                    self.add_result("API数据获取", score, max_score, "缺少数据获取功能")
                    return
        
        except Exception as e:
            self.add_result("API数据获取", 0, max_score, f"测试过程出错: {str(e)}")
            return
        
        self.add_result("API数据获取", score, max_score, "API数据获取功能正常")
    
    def test_plotting_function(self, module):
        """测试趋势图绘制功能（15分）"""
        score = 0
        max_score = 15
        
        try:
            plot_function = None
            
            # 查找绘图函数
            if hasattr(module, 'CurrencyAnalyzer'):
                analyzer = module.CurrencyAnalyzer()
                if hasattr(analyzer, 'plot_currency_trend'):
                    plot_function = analyzer.plot_currency_trend
                    score += 5
            elif hasattr(module, 'plot_currency_trend'):
                plot_function = module.plot_currency_trend
                score += 5
            
            if plot_function:
                # 检查函数参数
                sig = inspect.signature(plot_function)
                params = list(sig.parameters.keys())
                
                if len(params) >= 4:  # dates, rates, from_curr, to_curr
                    score += 5
                    
                    # 测试函数调用（不实际显示图表）
                    try:
                        test_dates = ['2024-01-01', '2024-01-02', '2024-01-03']
                        test_rates = [20.5, 20.6, 20.4]
                        
                        # 重定向matplotlib显示
                        import matplotlib
                        matplotlib.use('Agg')  # 使用非交互式后端
                        
                        if hasattr(module, 'CurrencyAnalyzer'):
                            analyzer = module.CurrencyAnalyzer()
                            analyzer.plot_currency_trend(test_dates, test_rates, 'CNY', 'JPY')
                        else:
                            module.plot_currency_trend(test_dates, test_rates, 'CNY', 'JPY')
                        
                        score += 5
                        
                    except Exception as e:
                        self.add_result("趋势图绘制", score, max_score, f"绘图函数调用失败: {str(e)}")
                        return
                else:
                    self.add_result("趋势图绘制", score, max_score, "绘图函数参数不正确")
                    return
            else:
                self.add_result("趋势图绘制", score, max_score, "缺少绘图函数")
                return
        
        except Exception as e:
            self.add_result("趋势图绘制", 0, max_score, f"测试过程出错: {str(e)}")
            return
        
        self.add_result("趋势图绘制", score, max_score, "趋势图绘制功能正常")
    
    def test_extremes_finding(self, module):
        """测试极值点查找功能（10分）"""
        score = 0
        max_score = 10
        
        try:
            test_rates = [20.5, 20.3, 20.1, 20.6, 20.8, 20.2]
            test_dates = ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05', '2024-01-06']
            
            # 查找极值函数
            if hasattr(module, 'CurrencyAnalyzer'):
                analyzer = module.CurrencyAnalyzer()
                if hasattr(analyzer, 'find_historical_extremes'):
                    result = analyzer.find_historical_extremes(test_rates, test_dates)
                    score += 5
                elif hasattr(analyzer, 'find_historical_low'):
                    result = analyzer.find_historical_low(test_rates, test_dates)
                    score += 3
            elif hasattr(module, 'find_historical_low'):
                result = module.find_historical_low(test_rates, test_dates)
                score += 3
            elif hasattr(module, 'find_historical_extremes'):
                result = module.find_historical_extremes(test_rates, test_dates)
                score += 5
            else:
                self.add_result("极值点查找", 0, max_score, "缺少极值查找函数")
                return
            
            # 验证结果
            if result:
                if isinstance(result, tuple) and len(result) >= 2:
                    min_rate = result[0]
                    if min_rate == 20.1:  # 正确的最小值
                        score += 5
                    else:
                        score += 2
                else:
                    score += 2
        
        except Exception as e:
            self.add_result("极值点查找", score, max_score, f"函数调用失败: {str(e)}")
            return
        
        self.add_result("极值点查找", score, max_score, "极值点查找功能正常")
    
    def test_trading_opportunities(self, module):
        """测试交易机会分析功能（15分）"""
        score = 0
        max_score = 15
        
        try:
            test_rates = [20.5, 20.3, 20.1, 20.6, 20.8, 20.2, 20.9, 20.4]
            test_dates = ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', 
                         '2024-01-05', '2024-01-06', '2024-01-07', '2024-01-08']
            
            # 查找交易机会函数
            if hasattr(module, 'CurrencyAnalyzer'):
                analyzer = module.CurrencyAnalyzer()
                if hasattr(analyzer, 'find_trading_opportunities'):
                    opportunities = analyzer.find_trading_opportunities(test_rates, test_dates, 5)
                    score += 8
                else:
                    self.add_result("交易机会分析", 0, max_score, "缺少交易机会分析方法")
                    return
            elif hasattr(module, 'find_trading_opportunities'):
                opportunities = module.find_trading_opportunities(test_rates, test_dates, 5)
                score += 8
            else:
                self.add_result("交易机会分析", 0, max_score, "缺少交易机会分析函数")
                return
            
            # 验证结果
            if opportunities and isinstance(opportunities, list):
                score += 4
                if len(opportunities) > 0 and isinstance(opportunities[0], dict):
                    required_keys = ['buy_date', 'sell_date', 'profit_percent']
                    if all(key in opportunities[0] for key in required_keys):
                        score += 3
        
        except Exception as e:
            self.add_result("交易机会分析", score, max_score, f"函数调用失败: {str(e)}")
            return
        
        self.add_result("交易机会分析", score, max_score, "交易机会分析功能正常")
    
    def test_volatility_analysis(self, module):
        """测试波动率计算功能（10分）"""
        score = 0
        max_score = 10
        
        try:
            test_rates = [20.0, 20.2, 19.8, 20.1, 20.3]
            
            # 查找波动率函数
            if hasattr(module, 'CurrencyAnalyzer'):
                analyzer = module.CurrencyAnalyzer()
                if hasattr(analyzer, 'analyze_volatility'):
                    volatility = analyzer.analyze_volatility(test_rates)
                    score += 5
                else:
                    self.add_result("波动率计算", 0, max_score, "缺少波动率分析方法")
                    return
            elif hasattr(module, 'analyze_volatility'):
                volatility = module.analyze_volatility(test_rates)
                score += 5
            else:
                self.add_result("波动率计算", 0, max_score, "缺少波动率分析函数")
                return
            
            # 验证结果
            if isinstance(volatility, (int, float)) and volatility >= 0:
                score += 5
        
        except Exception as e:
            self.add_result("波动率计算", score, max_score, f"函数调用失败: {str(e)}")
            return
        
        self.add_result("波动率计算", score, max_score, "波动率计算功能正常")
    
    def test_advanced_features(self, module):
        """测试可选高级功能（30分）"""
        advanced_score = 0
        
        # 套利分析（8分）
        if hasattr(module, 'CurrencyAnalyzer'):
            analyzer = module.CurrencyAnalyzer()
            if hasattr(analyzer, 'arbitrage_analysis'):
                try:
                    result = analyzer.arbitrage_analysis()
                    advanced_score += 8
                    self.add_result("套利分析", 8, 8, "套利分析功能实现")
                except:
                    advanced_score += 4
                    self.add_result("套利分析", 4, 8, "套利分析功能部分实现")
        
        # 技术分析（8分）
        if hasattr(module, 'CurrencyAnalyzer'):
            analyzer = module.CurrencyAnalyzer()
            tech_score = 0
            if hasattr(analyzer, 'find_support_resistance'):
                tech_score += 4
            if hasattr(analyzer, 'calculate_moving_averages'):
                tech_score += 4
            if tech_score > 0:
                advanced_score += tech_score
                self.add_result("技术分析", tech_score, 8, f"技术分析功能部分实现")
        
        # 预测功能（6分）
        if hasattr(module, 'CurrencyAnalyzer'):
            analyzer = module.CurrencyAnalyzer()
            if hasattr(analyzer, 'simple_prediction'):
                try:
                    test_rates = [20.0, 20.1, 20.2, 20.0, 19.9]
                    result = analyzer.simple_prediction(test_rates, 3)
                    advanced_score += 6
                    self.add_result("预测功能", 6, 6, "预测功能实现")
                except:
                    advanced_score += 3
                    self.add_result("预测功能", 3, 6, "预测功能部分实现")
        
        # 风险评估（5分）
        if hasattr(module, 'CurrencyAnalyzer'):
            analyzer = module.CurrencyAnalyzer()
            if hasattr(analyzer, 'risk_assessment'):
                try:
                    test_rates = [20.0, 20.1, 19.8, 20.2, 19.9]
                    result = analyzer.risk_assessment(test_rates)
                    advanced_score += 5
                    self.add_result("风险评估", 5, 5, "风险评估功能实现")
                except:
                    advanced_score += 2
                    self.add_result("风险评估", 2, 5, "风险评估功能部分实现")
        
        # 数据导出（3分）
        if hasattr(module, 'CurrencyAnalyzer'):
            analyzer = module.CurrencyAnalyzer()
            if hasattr(analyzer, 'export_data'):
                advanced_score += 3
                self.add_result("数据导出", 3, 3, "数据导出功能实现")
        
        if advanced_score == 0:
            self.add_result("高级功能", 0, 30, "未实现任何高级功能")
    
    def test_code_quality(self, module):
        """测试代码质量（5分）"""
        score = 0
        max_score = 5
        
        # 检查是否使用面向对象
        if hasattr(module, 'CurrencyAnalyzer'):
            score += 2
        
        # 检查函数文档
        functions = [obj for name, obj in inspect.getmembers(module) 
                    if inspect.isfunction(obj) or inspect.isclass(obj)]
        
        documented_functions = 0
        for func in functions:
            if func.__doc__ and len(func.__doc__.strip()) > 10:
                documented_functions += 1
        
        if documented_functions >= 3:
            score += 2
        elif documented_functions >= 1:
            score += 1
        
        # 检查错误处理
        source = inspect.getsource(module)
        if 'try:' in source and 'except' in source:
            score += 1
        
        self.add_result("代码质量", score, max_score, f"文档化函数: {documented_functions}")
    
    def grade_submission(self, file_path):
        """对学生提交的代码进行评分"""
        print(f"正在评分文件: {file_path}")
        print("=" * 50)
        
        # 加载学生代码
        module, loaded = self.load_student_module(file_path)
        if not loaded:
            return self.generate_report()
        
        # 执行各项测试
        self.test_api_data_retrieval(module)
        self.test_plotting_function(module)
        self.test_extremes_finding(module)
        self.test_trading_opportunities(module)
        self.test_volatility_analysis(module)
        self.test_code_quality(module)
        self.test_advanced_features(module)
        
        return self.generate_report()
    
    def generate_report(self):
        """生成评分报告"""
        report = f"\n{'='*50}\n"
        report += f"汇率分析系统 - 自动评分报告\n"
        report += f"{'='*50}\n\n"
        
        for result in self.test_results:
            report += f"{result['test']}: {result['score']}/{result['max_score']} 分"
            if result['comment']:
                report += f" - {result['comment']}"
            report += "\n"
        
        report += f"\n{'='*50}\n"
        report += f"总分: {self.total_score}/{self.max_score} 分\n"
        
        # 等级评定
        percentage = (self.total_score / self.max_score) * 100
        if percentage >= 90:
            grade = "优秀"
        elif percentage >= 80:
            grade = "良好"
        elif percentage >= 70:
            grade = "中等"
        elif percentage >= 60:
            grade = "及格"
        else:
            grade = "不及格"
        
        report += f"得分率: {percentage:.1f}%\n"
        report += f"等级: {grade}\n"
        report += f"{'='*50}\n"
        
        return report

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python auto_grader.py <学生代码文件路径>")
        return
    
    file_path = sys.argv[1]
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    grader = CurrencyAnalysisGrader()
    report = grader.grade_submission(file_path)
    print(report)
    
    # 保存报告到文件
    report_file = file_path.replace('.py', '_grade_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"评分报告已保存到: {report_file}")

if __name__ == "__main__":
    main()
