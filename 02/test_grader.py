"""
测试自动评分工具的示例脚本
演示如何使用auto_grader.py对学生代码进行评分
"""

import subprocess
import sys
import os

def test_grader_with_answer():
    """使用标准答案测试评分工具"""
    print("=== 测试自动评分工具 ===\n")
    
    answer_file = "answer/currency_analysis_answer.py"
    
    if not os.path.exists(answer_file):
        print(f"答案文件不存在: {answer_file}")
        return
    
    print(f"正在使用标准答案测试评分工具...")
    print(f"测试文件: {answer_file}\n")
    
    try:
        # 运行评分工具
        result = subprocess.run([
            sys.executable, "auto_grader.py", answer_file
        ], capture_output=True, text=True, encoding='utf-8')
        
        print("评分结果:")
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"运行评分工具时出错: {e}")

def create_sample_student_code():
    """创建一个示例学生代码用于测试"""
    sample_code = '''
import requests
import matplotlib.pyplot as plt
from datetime import date, timedelta

class CurrencyAnalyzer:
    def __init__(self):
        pass
    
    def get_historical_rates(self, start_date, end_date, from_curr, to_curr):
        url = f"https://api.frankfurter.app/{start_date}..{end_date}?from={from_curr}&to={to_curr}"
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
            
            rates_history = data.get('rates', {})
            if not rates_history:
                return None, None
                
            dates = sorted(rates_history.keys())
            rates = [rates_history[d][to_curr] for d in dates]
            return dates, rates
            
        except requests.exceptions.RequestException as e:
            print(f"获取历史汇率失败: {e}")
            return None, None
    
    def plot_currency_trend(self, dates, rates, from_curr, to_curr):
        plt.figure(figsize=(12, 6))
        plt.plot(dates, rates, linewidth=2, color='blue')
        plt.title(f'{from_curr} 兑 {to_curr} 汇率趋势图')
        plt.xlabel('日期')
        plt.ylabel('汇率')
        plt.show()
    
    def find_historical_low(self, rates, dates):
        min_rate = min(rates)
        min_index = rates.index(min_rate)
        min_date = dates[min_index]
        return min_rate, min_date
    
    def find_trading_opportunities(self, rates, dates, days=30):
        opportunities = []
        
        for i in range(len(rates) - days):
            buy_rate = rates[i]
            buy_date = dates[i]
            
            future_rates = rates[i+1:i+days+1]
            if future_rates:
                max_future_rate = max(future_rates)
                max_index = future_rates.index(max_future_rate) + i + 1
                sell_date = dates[max_index]
                
                profit_percent = ((max_future_rate - buy_rate) / buy_rate) * 100
                
                if profit_percent > 1:
                    opportunities.append({
                        'buy_date': buy_date,
                        'buy_rate': buy_rate,
                        'sell_date': sell_date,
                        'sell_rate': max_future_rate,
                        'profit_percent': profit_percent
                    })
        
        return sorted(opportunities, key=lambda x: x['profit_percent'], reverse=True)
    
    def analyze_volatility(self, rates):
        if len(rates) < 2:
            return 0
        
        changes = []
        for i in range(1, len(rates)):
            change = abs((rates[i] - rates[i-1]) / rates[i-1]) * 100
            changes.append(change)
        
        return sum(changes) / len(changes)

def main():
    print("=== 学生示例代码 ===")
    
    analyzer = CurrencyAnalyzer()
    
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    dates, rates = analyzer.get_historical_rates(start_date, end_date, 'CNY', 'JPY')
    
    if dates and rates:
        print(f"获取到 {len(dates)} 天的数据")
        
        min_rate, min_date = analyzer.find_historical_low(rates, dates)
        print(f"最低汇率: {min_rate:.4f} ({min_date})")
        
        opportunities = analyzer.find_trading_opportunities(rates, dates, 15)
        print(f"交易机会: {len(opportunities)} 个")
        
        volatility = analyzer.analyze_volatility(rates)
        print(f"波动率: {volatility:.2f}%")
    
    else:
        print("无法获取数据")

if __name__ == "__main__":
    main()
'''
    
    # 保存示例代码
    with open("sample_student_code.py", "w", encoding="utf-8") as f:
        f.write(sample_code)
    
    print("已创建示例学生代码: sample_student_code.py")
    return "sample_student_code.py"

def test_grader_with_sample():
    """使用示例学生代码测试评分工具"""
    sample_file = create_sample_student_code()
    
    print(f"\n正在使用示例学生代码测试评分工具...")
    print(f"测试文件: {sample_file}\n")
    
    try:
        # 运行评分工具
        result = subprocess.run([
            sys.executable, "auto_grader.py", sample_file
        ], capture_output=True, text=True, encoding='utf-8')
        
        print("评分结果:")
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"运行评分工具时出错: {e}")
    
    # 清理示例文件
    try:
        os.remove(sample_file)
        os.remove(sample_file.replace('.py', '_grade_report.txt'))
        print(f"\n已清理临时文件")
    except:
        pass

def main():
    """主函数"""
    print("汇率分析系统 - 评分工具测试\n")
    
    # 测试1: 使用标准答案
    test_grader_with_answer()
    
    print("\n" + "="*60 + "\n")
    
    # 测试2: 使用示例学生代码
    test_grader_with_sample()

if __name__ == "__main__":
    main()
