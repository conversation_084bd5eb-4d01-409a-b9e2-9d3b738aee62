"""
汇率分析系统 - 困难难度
学生需要实现所有功能，包括基础功能和可选的高级功能

基础功能要求：
1. 获取历史汇率数据
2. 绘制汇率趋势图
3. 寻找历史最低点
4. 寻找交易机会
5. 分析汇率波动性

可选高级功能（加分项）：
1. 多货币套利分析
2. 支撑位和阻力位分析
3. 移动平均线分析
4. 汇率预测模型
5. 风险评估系统
6. 交互式图表
7. 数据导出功能
"""

import requests
import matplotlib.pyplot as plt
from datetime import date, timedelta

class CurrencyAnalyzer:
    """汇率分析器类"""
    
    def __init__(self):
        """初始化分析器"""
        # TODO: 初始化必要的属性
        pass
    
    def get_historical_rates(self, start_date, end_date, from_curr, to_curr):
        """
        获取历史汇率数据
        
        参数：
        - start_date: 开始日期
        - end_date: 结束日期  
        - from_curr: 源货币
        - to_curr: 目标货币
        
        返回：
        - dates: 日期列表
        - rates: 汇率列表
        """
        # TODO: 实现API调用获取历史汇率数据
        pass
    
    def plot_currency_trend(self, dates, rates, from_curr, to_curr):
        """
        绘制汇率趋势图
        
        要求：
        - 使用matplotlib绘制折线图
        - 添加适当的标题、标签、网格
        - 处理日期显示格式
        """
        # TODO: 实现汇率趋势图绘制
        pass
    
    def find_historical_extremes(self, rates, dates):
        """
        寻找历史极值点（最高点和最低点）
        
        返回：
        - min_rate, min_date: 最低汇率和日期
        - max_rate, max_date: 最高汇率和日期
        """
        # TODO: 实现历史极值点查找
        pass
    
    def find_trading_opportunities(self, rates, dates, days=30):
        """
        寻找交易机会
        
        参数：
        - rates: 汇率列表
        - dates: 日期列表
        - days: 交易时间窗口
        
        返回：
        - opportunities: 交易机会列表
        """
        # TODO: 实现交易机会分析算法
        pass
    
    def analyze_volatility(self, rates):
        """
        分析汇率波动性
        
        返回：
        - volatility: 波动率百分比
        """
        # TODO: 实现波动性分析
        pass
    
    def currency_comparison(self, base_curr, target_currencies, days=30):
        """
        多货币表现比较
        
        参数：
        - base_curr: 基础货币
        - target_currencies: 目标货币列表
        - days: 分析天数
        
        返回：
        - comparison_results: 比较结果列表
        """
        # TODO: 实现多货币比较分析
        pass
    
    # 可选高级功能 1: 套利分析
    def arbitrage_analysis(self, base_curr='CNY'):
        """
        多货币套利机会分析
        
        分析通过不同货币路径是否存在套利机会
        例如：CNY -> USD -> EUR -> CNY 与 CNY -> EUR 的差异
        """
        # TODO: 实现套利分析（可选）
        pass
    
    # 可选高级功能 2: 技术分析
    def find_support_resistance(self, rates, dates, window=5):
        """
        寻找支撑位和阻力位
        
        参数：
        - rates: 汇率列表
        - dates: 日期列表
        - window: 分析窗口大小
        
        返回：
        - support_levels: 支撑位列表
        - resistance_levels: 阻力位列表
        """
        # TODO: 实现支撑位和阻力位分析（可选）
        pass
    
    # 可选高级功能 3: 移动平均线
    def calculate_moving_averages(self, rates, short_window=5, long_window=20):
        """
        计算移动平均线
        
        参数：
        - rates: 汇率列表
        - short_window: 短期移动平均窗口
        - long_window: 长期移动平均窗口
        
        返回：
        - short_ma: 短期移动平均线
        - long_ma: 长期移动平均线
        """
        # TODO: 实现移动平均线计算（可选）
        pass
    
    # 可选高级功能 4: 简单预测
    def simple_prediction(self, rates, prediction_days=7):
        """
        基于历史数据的简单汇率预测
        
        参数：
        - rates: 历史汇率列表
        - prediction_days: 预测天数
        
        返回：
        - predicted_rates: 预测汇率列表
        """
        # TODO: 实现简单预测模型（可选）
        pass
    
    # 可选高级功能 5: 风险评估
    def risk_assessment(self, rates):
        """
        风险评估分析
        
        计算各种风险指标：
        - 最大回撤
        - 夏普比率（简化版）
        - VaR（风险价值）
        
        返回：
        - risk_metrics: 风险指标字典
        """
        # TODO: 实现风险评估（可选）
        pass
    
    # 可选高级功能 6: 数据导出
    def export_data(self, data, filename, format='csv'):
        """
        导出分析数据到文件
        
        参数：
        - data: 要导出的数据
        - filename: 文件名
        - format: 导出格式 ('csv', 'json')
        """
        # TODO: 实现数据导出功能（可选）
        pass

def main():
    """
    主函数
    
    要求实现：
    1. 创建CurrencyAnalyzer实例
    2. 获取CNY兑JPY的90天历史数据
    3. 绘制趋势图
    4. 分析历史极值
    5. 寻找交易机会
    6. 计算波动率
    7. 进行多货币比较
    
    可选实现：
    - 套利分析
    - 技术分析指标
    - 预测功能
    - 风险评估
    - 数据导出
    """
    print("=== 汇率分析系统 - 高级版 ===\n")
    
    # TODO: 创建分析器实例
    analyzer = CurrencyAnalyzer()
    
    # TODO: 设置分析参数
    # 分析货币对：CNY -> JPY
    # 分析时间：最近90天
    
    # TODO: 实现基础功能
    # 1. 获取历史数据
    # 2. 绘制趋势图
    # 3. 分析极值点
    # 4. 寻找交易机会
    # 5. 计算波动率
    # 6. 多货币比较
    
    # TODO: 实现可选高级功能（加分项）
    # 1. 套利分析
    # 2. 技术分析
    # 3. 预测模型
    # 4. 风险评估
    # 5. 数据导出
    
    print("分析完成！")

if __name__ == "__main__":
    main()

"""
评分标准：
基础功能（70分）：
- 正确获取API数据：15分
- 绘制趋势图：15分
- 寻找极值点：10分
- 交易机会分析：15分
- 波动率计算：10分
- 代码结构和注释：5分

可选高级功能（30分）：
- 套利分析：8分
- 技术分析：8分
- 预测功能：6分
- 风险评估：5分
- 数据导出：3分

代码质量要求：
- 使用面向对象编程
- 适当的错误处理
- 清晰的函数文档
- 合理的变量命名
"""
