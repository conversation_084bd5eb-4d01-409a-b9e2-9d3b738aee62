import requests
import matplotlib.pyplot as plt
from datetime import date, timedelta

def get_historical_rates(start_date, end_date, from_curr, to_curr):
    """
    获取历史汇率数据
    参数：
    - start_date: 开始日期 (格式: 'YYYY-MM-DD')
    - end_date: 结束日期 (格式: 'YYYY-MM-DD')
    - from_curr: 源货币代码 (如: 'CNY')
    - to_curr: 目标货币代码 (如: 'JPY')
    
    返回：
    - dates: 日期列表
    - rates: 汇率列表
    """
    # TODO: 构建API请求URL
    # 提示：使用f-string格式化字符串，API地址为 https://api.frankfurter.app/
    url = # 你的代码
    
    try:
        # TODO: 发送HTTP请求并获取JSON数据
        response = # 你的代码
        data = # 你的代码
        
        # 复杂逻辑已提供：提取和处理汇率数据
        rates_history = data.get('rates', {})
        if not rates_history:
            return None, None
            
        dates = sorted(rates_history.keys())
        rates = [rates_history[d][to_curr] for d in dates]
        return dates, rates
        
    except requests.exceptions.RequestException as e:
        print(f"获取历史汇率失败: {e}")
        return None, None

def plot_currency_trend(dates, rates, from_curr, to_curr):
    """绘制汇率趋势图"""
    # TODO: 创建图表并设置大小
    # 你的代码
    
    # TODO: 绘制折线图，设置颜色、线宽、标记点
    # 你的代码
    
    # TODO: 设置图表标题、x轴标签、y轴标签
    # 你的代码
    
    # 复杂逻辑已提供：网格线和日期标签处理
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    step = max(1, len(dates) // 10)
    plt.xticks(range(0, len(dates), step), [dates[i] for i in range(0, len(dates), step)])
    plt.tight_layout()
    plt.show()

def find_historical_low(rates, dates):
    """寻找历史最低汇率"""
    # TODO: 找到最小汇率值和对应的日期
    # 你的代码
    
    return min_rate, min_date

def find_trading_opportunities(rates, dates, days=30):
    """
    寻找交易机会（买低卖高策略）
    复杂的算法逻辑已提供，你需要补充基本的数据处理部分
    """
    opportunities = []
    
    for i in range(len(rates) - days):
        # TODO: 获取当前的买入汇率和日期
        buy_rate = # 你的代码
        buy_date = # 你的代码
        
        # 复杂逻辑已提供：在未来时间窗口内寻找最佳卖出点
        future_rates = rates[i+1:i+days+1]
        if future_rates:
            max_future_rate = max(future_rates)
            max_index = future_rates.index(max_future_rate) + i + 1
            sell_date = dates[max_index]
            
            # TODO: 计算盈利百分比
            profit_percent = # 你的代码，公式：((卖出价-买入价)/买入价) * 100
            
            if profit_percent > 1:
                opportunities.append({
                    'buy_date': buy_date,
                    'buy_rate': buy_rate,
                    'sell_date': sell_date,
                    'sell_rate': max_future_rate,
                    'profit_percent': profit_percent
                })
    
    # 复杂逻辑已提供：按盈利率排序
    return sorted(opportunities, key=lambda x: x['profit_percent'], reverse=True)

def analyze_volatility(rates):
    """
    分析汇率波动性
    计算相邻两天汇率变化的平均百分比
    """
    if len(rates) < 2:
        return 0
    
    changes = []
    # TODO: 计算每天的汇率变化百分比
    for i in range(1, len(rates)):
        # 你的代码：计算变化百分比并添加到changes列表
        pass
    
    # TODO: 返回平均波动率
    return # 你的代码

def currency_comparison(base_curr, target_currencies, days=30):
    """
    比较多个货币对的表现
    参数：
    - base_curr: 基础货币
    - target_currencies: 目标货币列表
    - days: 分析天数
    """
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    comparison_results = []
    
    for curr in target_currencies:
        # TODO: 获取每个货币对的汇率数据
        dates, rates = # 你的代码
        
        if dates and rates and len(rates) >= 2:
            # TODO: 计算期间涨跌幅
            start_rate = # 你的代码
            end_rate = # 你的代码
            change_percent = # 你的代码
            
            # TODO: 计算波动率
            volatility = # 你的代码
            
            comparison_results.append({
                'currency': curr,
                'start_rate': start_rate,
                'end_rate': end_rate,
                'change_percent': change_percent,
                'volatility': volatility
            })
    
    return comparison_results

def main():
    print("=== 汇率分析系统 ===\n")
    
    # TODO: 设置分析的日期范围
    end_date = # 你的代码
    start_date = # 你的代码
    
    print("正在获取CNY兑JPY的历史汇率数据...")
    
    # TODO: 获取汇率数据
    dates, rates = # 你的代码
    
    if dates and rates:
        print(f"成功获取 {len(dates)} 天的汇率数据")
        
        # TODO: 绘制趋势图
        # 你的代码
        
        # TODO: 寻找历史最低点
        min_rate, min_date = # 你的代码
        print(f"历史最低汇率: {min_rate:.4f} ({min_date})")
        
        # TODO: 寻找交易机会
        opportunities = # 你的代码
        print(f"发现 {len(opportunities)} 个交易机会")
        
        if opportunities:
            best_opp = opportunities[0]
            print(f"最佳交易机会:")
            print(f"买入: {best_opp['buy_date']} - {best_opp['buy_rate']:.4f}")
            print(f"卖出: {best_opp['sell_date']} - {best_opp['sell_rate']:.4f}")
            print(f"盈利: {best_opp['profit_percent']:.2f}%")
        
        # TODO: 分析波动性
        volatility = # 你的代码
        print(f"平均波动率: {volatility:.2f}%")
        
        # TODO: 多货币比较
        target_currencies = ['USD', 'EUR', 'GBP']
        comparison = # 你的代码
        
        print("\n多货币表现比较:")
        for result in comparison:
            print(f"{result['currency']}: 涨跌幅 {result['change_percent']:.2f}%, 波动率 {result['volatility']:.2f}%")
    
    else:
        print("无法获取汇率数据")

if __name__ == "__main__":
    main()
