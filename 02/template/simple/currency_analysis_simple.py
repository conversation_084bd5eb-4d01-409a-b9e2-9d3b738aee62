import requests
import matplotlib.pyplot as plt
from datetime import date, timedelta

def get_historical_rates(start_date, end_date, from_curr, to_curr):
    # 构建API请求的URL地址
    url = f"https://api.frankfurter.app/{start_date}..{end_date}?from={from_curr}&to={to_curr}"
    
    try:
        # 发送HTTP请求获取数据
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        
        # 从返回的数据中提取汇率历史记录
        rates_history = data.get('rates', {})
        if not rates_history:
            return None, None
            
        # 对日期进行排序，确保按时间顺序
        dates = sorted(rates_history.keys())
        # 提取每个日期对应的汇率值
        rates = [rates_history[d][to_curr] for d in dates]
        return dates, rates
        
    except requests.exceptions.RequestException as e:
        print(f"获取历史汇率失败: {e}")
        return None, None

def plot_currency_trend(dates, rates, from_curr, to_curr):
    # 创建图表，设置大小为12x6
    plt.figure(figsize=(12, 6))
    
    # 绘制折线图，设置线条颜色为蓝色，添加圆点标记
    plt.plot(dates, rates, linewidth=2, color='____', marker='o', markersize=3)
    
    # 设置图表标题
    plt.title(f'{from_curr} 兑 {to_curr} 汇率趋势图', fontsize=16)
    
    # 设置x轴和y轴标签
    plt.xlabel('____', fontsize=12)
    plt.ylabel(f'汇率 ({from_curr} -> {to_curr})', fontsize=12)
    
    # 添加网格线，设置透明度为0.3
    plt.grid(True, alpha=0.3)
    
    # 旋转x轴标签45度，避免重叠
    plt.xticks(rotation=45)
    
    # 每隔一定间隔显示日期标签
    step = max(1, len(dates) // 10)
    plt.xticks(range(0, len(dates), step), [dates[i] for i in range(0, len(dates), step)])
    
    # 自动调整布局
    plt.tight_layout()
    plt.show()

def find_historical_low(rates, dates):
    # 找到汇率列表中的最小值
    min_rate = min(____)
    
    # 找到最小值在列表中的位置
    min_index = rates.index(min_rate)
    
    # 根据位置找到对应的日期
    min_date = dates[____]
    
    return min_rate, min_date

def find_trading_opportunities(rates, dates, days=30):
    opportunities = []
    
    # 遍历汇率数据，寻找交易机会
    for i in range(len(rates) - days):
        buy_rate = rates[i]
        buy_date = dates[i]
        
        # 在接下来的days天内寻找最高点
        future_rates = rates[i+1:i+days+1]
        if future_rates:
            max_future_rate = max(____)
            max_index = future_rates.index(max_future_rate) + i + 1
            sell_date = dates[max_index]
            
            # 计算盈利百分比
            profit_percent = ((max_future_rate - buy_rate) / ____) * 100
            
            # 只记录盈利超过1%的机会
            if profit_percent > 1:
                opportunities.append({
                    'buy_date': buy_date,
                    'buy_rate': buy_rate,
                    'sell_date': sell_date,
                    'sell_rate': max_future_rate,
                    'profit_percent': profit_percent
                })
    
    # 按盈利百分比从高到低排序
    return sorted(opportunities, key=lambda x: x['profit_percent'], reverse=True)

def main():
    print("=== 汇率分析系统 ===\n")
    
    # 设置日期范围：从90天前到今天
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=____)).strftime('%Y-%m-%d')
    
    print("正在获取CNY兑JPY的历史汇率数据...")
    
    # 获取历史汇率数据
    dates, rates = get_historical_rates(start_date, end_date, '____', '____')
    
    if dates and rates:
        print(f"成功获取 {len(dates)} 天的汇率数据")
        print(f"数据范围: {dates[0]} 到 {dates[-1]}")
        print(f"汇率范围: {min(rates):.4f} 到 {max(rates):.4f}")
        
        # 绘制汇率趋势图
        plot_currency_trend(dates, rates, 'CNY', 'JPY')
        
        # 寻找历史最低点
        print("\n正在寻找历史最低点...")
        min_rate, min_date = find_historical_low(rates, dates)
        print(f"历史最低汇率: {min_rate:.4f} (日期: {min_date})")
        
        # 寻找30天交易机会
        print("\n正在寻找30天交易机会...")
        opportunities = find_trading_opportunities(rates, dates, 30)
        print(f"发现 {len(opportunities)} 个盈利机会")
        
        if opportunities:
            print("\n前3个最佳交易机会:")
            for i, opp in enumerate(opportunities[:3], 1):
                print(f"{i}. 买入日期: {opp['buy_date']}, 汇率: {opp['buy_rate']:.4f}")
                print(f"   卖出日期: {opp['sell_date']}, 汇率: {opp['sell_rate']:.4f}")
                print(f"   盈利: {opp['profit_percent']:.2f}%")
        else:
            print("在指定时间范围内没有发现明显的交易机会")
    
    else:
        print("无法获取汇率数据，请检查网络连接")

if __name__ == "__main__":
    main()

# 填空提示：
# 1. 'blue' - 设置折线图颜色
# 2. '日期' - x轴标签
# 3. rates - 汇率列表
# 4. min_index - 最小值的索引位置
# 5. future_rates - 未来汇率列表
# 6. buy_rate - 买入汇率，用于计算盈利百分比
# 7. 90 - 获取90天的历史数据
# 8. 'CNY' - 人民币货币代码
# 9. 'JPY' - 日元货币代码
