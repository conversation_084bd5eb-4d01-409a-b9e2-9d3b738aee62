import requests

def get_iss_location():
    """获取国际空间站当前位置"""
    iss_url = "https://api.wheretheiss.at/v1/satellites/25544"
    
    try:
        response = requests.get(iss_url)
        response.raise_for_status()
        
        iss_data = response.json()
        latitude = iss_data.get('latitude')
        longitude = iss_data.get('longitude')
        altitude = iss_data.get('altitude')
        velocity = iss_data.get('velocity')
        
        if latitude and longitude:
            print(f"第一棒完成！成功获取ISS坐标: 纬度 {latitude:.4f}, 经度 {longitude:.4f}")
            print(f"高度: {altitude:.2f} 公里, 速度: {velocity:.2f} 公里/小时")
            return latitude, longitude
        else:
            print("未能从API响应中获取有效的坐标")
            return None, None
            
    except requests.exceptions.RequestException as e:
        print(f"请求ISS位置失败: {e}")
        return None, None

def get_location_name(latitude, longitude):
    """将坐标转换为地名"""
    if not latitude or not longitude:
        return "未知区域"
    
    try:
        print("\n开始第二棒... 正在将坐标发送给地理位置API...")
        geo_url = "https://nominatim.openstreetmap.org/reverse"
        params = {
            'format': 'jsonv2',
            'lat': latitude,
            'lon': longitude,
            'accept-language': 'zh-CN'
        }
        headers = {
            'User-Agent': 'HelloWorld/1.0'
        }
        
        geo_response = requests.get(geo_url, params=params, headers=headers)
        geo_response.raise_for_status()
        geo_data = geo_response.json()
        
        location_name = geo_data.get('display_name', '海洋或偏远地区上空')
        print("第二棒完成！成功将坐标转换为地名！")
        return location_name
        
    except requests.exceptions.RequestException as e:
        print(f"请求地理位置失败: {e}")
        return "海洋或偏远地区上空"

def get_astronauts_info():
    """获取太空中宇航员信息"""
    astronauts_url = "http://api.open-notify.org/astros.json"
    
    try:
        response = requests.get(astronauts_url)
        response.raise_for_status()
        astronauts_data = response.json()
        
        print(f"\n太空中总共有 {astronauts_data['number']} 名宇航员！")
        print(f"API状态: {astronauts_data['message']}")
        
        return astronauts_data
        
    except requests.exceptions.RequestException as e:
        print(f"获取宇航员信息失败: {e}")
        backup_data = {
            "people": [
                {"craft": "ISS", "name": "Oleg Kononenko"},
                {"craft": "ISS", "name": "Nikolai Chub"},
                {"craft": "ISS", "name": "Tracy Caldwell Dyson"},
                {"craft": "ISS", "name": "Matthew Dominick"},
                {"craft": "ISS", "name": "Michael Barratt"},
                {"craft": "ISS", "name": "Jeanette Epps"},
                {"craft": "ISS", "name": "Alexander Grebenkin"},
                {"craft": "ISS", "name": "Butch Wilmore"},
                {"craft": "ISS", "name": "Sunita Williams"},
                {"craft": "Tiangong", "name": "Li Guangsu"},
                {"craft": "Tiangong", "name": "Li Cong"},
                {"craft": "Tiangong", "name": "Ye Guangfu"}
            ],
            "number": 12,
            "message": "success"
        }
        print(f"使用备用数据: 太空中总共有 {backup_data['number']} 名宇航员！")
        return backup_data

def analyze_astronauts(astronauts_data):
    """分析宇航员分布情况"""
    craft_count = {}
    
    for astronaut in astronauts_data['people']:
        craft = astronaut['craft']
        name = astronaut['name']
        
        if craft in craft_count:
            craft_count[craft] += 1
        else:
            craft_count[craft] = 1
    
    print("\n各飞行器宇航员分布：")
    for craft, count in craft_count.items():
        print(f"{craft}: {count} 人")
    
    iss_count = craft_count.get('ISS', 0)
    tiangong_count = craft_count.get('Tiangong', 0)
    
    print(f"\n国际空间站(ISS): {iss_count} 人")
    print(f"中国天宫空间站: {tiangong_count} 人")
    
    if tiangong_count > 0:
        print("\n太棒了！中国天宫空间站也有宇航员在工作！")
        print("这说明人类太空探索正在蓬勃发展！")
    
    return craft_count

def main():
    """主程序"""
    print("=== Python 和 API 的奇妙探险之旅 ===\n")
    
    # 第一棒：获取ISS位置
    latitude, longitude = get_iss_location()
    
    # 第二棒：获取地名
    location_name = get_location_name(latitude, longitude)
    
    # 显示最终结果
    if latitude and longitude:
        print("\n--- 国际空间站实时追踪报告 ---")
        print(f"当前坐标: 纬度 {latitude:.4f}, 经度 {longitude:.4f}")
        print(f"当前位置: {location_name}")
    else:
        print("\n无法生成追踪报告，因为初始位置获取失败。")
    
    # 练习：分析宇航员信息
    print("\n" + "="*50)
    print("练习：探索太空中的宇航员！")
    print("="*50)
    
    astronauts_data = get_astronauts_info()
    analyze_astronauts(astronauts_data)

if __name__ == "__main__":
    main()
