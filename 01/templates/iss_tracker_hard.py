"""
ISS追踪器 - 困难难度

基础任务：
1. 获取国际空间站当前位置
2. 将坐标转换为地名
3. 获取太空中宇航员信息
4. 统计各飞行器宇航员分布

可选挑战功能：
1. 添加ISS轨道预测功能
2. 实现多次位置查询并显示移动轨迹
3. 添加最近经过的国家统计
4. 实现宇航员信息的详细分析（按国籍分组等）
5. 添加数据可视化功能
6. 实现定时自动更新功能

API信息：
- ISS位置: https://api.wheretheiss.at/v1/satellites/25544
- 地理编码: https://nominatim.openstreetmap.org/reverse
- 宇航员信息: http://api.open-notify.org/astros.json

提示：
- 使用requests库进行HTTP请求
- 注意异常处理
- 合理设置User-Agent
- 考虑API调用频率限制
"""

# 在这里实现你的代码
