import requests

def get_iss_location():
    iss_url = "https://api.wheretheiss.at/v1/satellites/25544"
    
    # 发送GET请求获取ISS位置数据
    # 处理可能的异常
    # 解析JSON响应
    # 提取latitude, longitude, altitude, velocity
    # 返回latitude, longitude
    pass

def get_location_name(latitude, longitude):
    # 检查坐标是否有效
    
    # 设置Nominatim API的URL和参数
    geo_url = "https://nominatim.openstreetmap.org/reverse"
    params = {
        # 填写必要的参数
    }
    headers = {
        # 设置User-Agent
    }
    
    # 发送请求并处理响应
    # 提取display_name字段
    # 返回地名
    pass

def get_astronauts_info():
    astronauts_url = "http://api.open-notify.org/astros.json"
    
    try:
        # 发送请求获取宇航员数据
        response = requests.get(astronauts_url)
        response.raise_for_status()
        astronauts_data = response.json()
        
        # 打印宇航员总数
        
        return astronauts_data
        
    except requests.exceptions.RequestException as e:
        print(f"获取宇航员信息失败: {e}")
        # 返回备用数据
        backup_data = {
            "people": [
                {"craft": "ISS", "name": "Astronaut 1"},
                {"craft": "ISS", "name": "Astronaut 2"},
                {"craft": "ISS", "name": "Astronaut 3"},
                {"craft": "Tiangong", "name": "Astronaut 4"},
                {"craft": "Tiangong", "name": "Astronaut 5"}
            ],
            "number": 5,
            "message": "success"
        }
        return backup_data

def analyze_astronauts(astronauts_data):
    # 创建字典统计各飞行器的宇航员数量
    craft_count = {}
    
    # 遍历所有宇航员
    for astronaut in astronauts_data['people']:
        # 获取飞行器名称
        # 统计数量
        pass
    
    # 打印各飞行器宇航员分布
    
    # 获取ISS和天宫空间站的宇航员数量
    
    # 打印特定信息
    
    return craft_count

def main():
    print("=== ISS 追踪器 ===")
    
    # 获取ISS位置
    
    # 获取地名
    
    # 显示结果
    
    print("\n=== 宇航员信息 ===")
    # 获取并分析宇航员信息

if __name__ == "__main__":
    main()
