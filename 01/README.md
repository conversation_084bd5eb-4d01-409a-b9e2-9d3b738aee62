# Python API 编程教学材料

基于 Notebook/L1.ipynb 的 Python API 编程教学材料，适合小学生学习。

## 教学内容

本课程通过"API接力赛"的方式教授：

1. **API基础概念** - 用"点餐"比喻理解API工作原理
2. **HTTP请求响应** - 状态码、JSON格式等
3. **实践项目**：
   - 第一棒：调用ISS API获取国际空间站位置
   - 第二棒：调用地理编码API将坐标转换为地名
4. **练习项目** - 分析太空中宇航员信息

## 文件夹结构

### teacher_demo/
老师课堂演示用的完整代码
- `iss_tracker_demo.py` - 完整的ISS追踪器演示程序

### answers/
标准答案文件
- `iss_tracker_complete.py` - 完整的参考答案

### templates/
学生练习模板（三种难度）
- `iss_tracker_easy.py` - 简单难度（填空题）
- `iss_tracker_medium.py` - 中等难度（提供骨架）
- `iss_tracker_hard.py` - 困难难度（仅框架）

### 辅助工具
- `auto_grader_iss.py` - 自动测试打分工具
- `api_test_helper.py` - API连接测试工具

## 难度说明

### 简单难度（iss_tracker_easy.py）
- 几乎完整的程序代码
- 在关键位置设置填空
- 适合初学者理解API调用流程

### 中等难度（iss_tracker_medium.py）
- 提供函数框架和复杂逻辑
- 需要学生补充简单的实现代码
- 适合有一定基础的学生

### 困难难度（iss_tracker_hard.py）
- 仅提供任务描述和API信息
- 需要学生独立完成所有代码
- 包含可选的挑战功能
- 适合有经验的学生

## 使用说明

### 学生使用
1. 根据自己水平选择对应难度的模板文件
2. 完成代码编写
3. 运行程序查看结果
4. 使用自动打分工具检验成果

### 老师使用
1. 使用 `teacher_demo/` 中的代码进行课堂演示
2. 参考 `answers/` 中的标准答案
3. 使用 `auto_grader_iss.py` 对学生作业进行自动评分

### 测试工具
运行 `api_test_helper.py` 可以：
- 测试所有API的连接状态
- 查看API返回数据格式
- 验证完整的调用流程

## 评分标准

自动打分工具评分项目：
- 导入库 (5分)
- ISS位置获取函数 (20分)
- 地理编码函数 (20分)
- 宇航员信息获取函数 (15分)
- 数据分析函数 (20分)
- 主函数 (10分)
- 代码质量和奖励功能 (10分)
