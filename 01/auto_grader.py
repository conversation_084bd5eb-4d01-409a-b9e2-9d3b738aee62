import importlib.util
import sys
import io
import contextlib
import traceback
import requests
from unittest.mock import patch, MagicMock

class ISSTrackerGrader:
    def __init__(self, student_file_path):
        self.student_file_path = student_file_path
        self.student_module = None
        self.score = 0
        self.max_score = 100
        self.feedback = []
        
    def load_student_code(self):
        try:
            spec = importlib.util.spec_from_file_location("student_code", self.student_file_path)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.feedback.append(f"代码加载失败: {str(e)}")
            return False
    
    def test_imports(self):
        try:
            if hasattr(self.student_module, 'requests') or 'requests' in str(self.student_module.__dict__):
                self.score += 5
                self.feedback.append("✓ 正确导入requests库 (+5分)")
            else:
                self.feedback.append("✗ 未正确导入requests库")
        except Exception as e:
            self.feedback.append(f"✗ 导入测试失败: {str(e)}")
    
    def test_get_iss_location(self):
        if not hasattr(self.student_module, 'get_iss_location'):
            self.feedback.append("✗ 缺少get_iss_location函数")
            return
        
        try:
            # 模拟API响应
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'latitude': 25.0,
                'longitude': 120.0,
                'altitude': 400.0,
                'velocity': 27000.0
            }
            mock_response.raise_for_status.return_value = None
            
            with patch('requests.get', return_value=mock_response):
                result = self.student_module.get_iss_location()
                
                if isinstance(result, tuple) and len(result) == 2:
                    lat, lon = result
                    if lat == 25.0 and lon == 120.0:
                        self.score += 20
                        self.feedback.append("✓ get_iss_location函数正确实现 (+20分)")
                    else:
                        self.score += 10
                        self.feedback.append("✓ get_iss_location函数基本正确，但返回值有误 (+10分)")
                else:
                    self.feedback.append("✗ get_iss_location函数返回格式错误")
                    
        except Exception as e:
            self.feedback.append(f"✗ get_iss_location函数测试失败: {str(e)}")
    
    def test_get_location_name(self):
        if not hasattr(self.student_module, 'get_location_name'):
            self.feedback.append("✗ 缺少get_location_name函数")
            return
        
        try:
            # 模拟地理编码API响应
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'display_name': '台湾'
            }
            mock_response.raise_for_status.return_value = None
            
            with patch('requests.get', return_value=mock_response):
                result = self.student_module.get_location_name(25.0, 120.0)
                
                if isinstance(result, str) and result == '台湾':
                    self.score += 20
                    self.feedback.append("✓ get_location_name函数正确实现 (+20分)")
                elif isinstance(result, str):
                    self.score += 10
                    self.feedback.append("✓ get_location_name函数基本正确 (+10分)")
                else:
                    self.feedback.append("✗ get_location_name函数返回类型错误")
                    
        except Exception as e:
            self.feedback.append(f"✗ get_location_name函数测试失败: {str(e)}")
    
    def test_get_astronauts_info(self):
        if not hasattr(self.student_module, 'get_astronauts_info'):
            self.feedback.append("✗ 缺少get_astronauts_info函数")
            return
        
        try:
            # 模拟宇航员API响应
            mock_response = MagicMock()
            mock_response.json.return_value = {
                'number': 3,
                'people': [
                    {'craft': 'ISS', 'name': 'Test1'},
                    {'craft': 'ISS', 'name': 'Test2'},
                    {'craft': 'Tiangong', 'name': 'Test3'}
                ],
                'message': 'success'
            }
            mock_response.raise_for_status.return_value = None
            
            with patch('requests.get', return_value=mock_response):
                result = self.student_module.get_astronauts_info()
                
                if isinstance(result, dict) and 'number' in result and 'people' in result:
                    self.score += 15
                    self.feedback.append("✓ get_astronauts_info函数正确实现 (+15分)")
                else:
                    self.feedback.append("✗ get_astronauts_info函数返回格式错误")
                    
        except Exception as e:
            self.feedback.append(f"✗ get_astronauts_info函数测试失败: {str(e)}")
    
    def test_analyze_astronauts(self):
        if not hasattr(self.student_module, 'analyze_astronauts'):
            self.feedback.append("✗ 缺少analyze_astronauts函数")
            return
        
        try:
            test_data = {
                'number': 3,
                'people': [
                    {'craft': 'ISS', 'name': 'Test1'},
                    {'craft': 'ISS', 'name': 'Test2'},
                    {'craft': 'Tiangong', 'name': 'Test3'}
                ]
            }
            
            # 捕获输出
            captured_output = io.StringIO()
            with contextlib.redirect_stdout(captured_output):
                result = self.student_module.analyze_astronauts(test_data)
            
            output = captured_output.getvalue()
            
            if isinstance(result, dict) and 'ISS' in result and 'Tiangong' in result:
                if result['ISS'] == 2 and result['Tiangong'] == 1:
                    self.score += 20
                    self.feedback.append("✓ analyze_astronauts函数正确实现 (+20分)")
                else:
                    self.score += 10
                    self.feedback.append("✓ analyze_astronauts函数基本正确 (+10分)")
            else:
                self.feedback.append("✗ analyze_astronauts函数返回格式错误")
                
        except Exception as e:
            self.feedback.append(f"✗ analyze_astronauts函数测试失败: {str(e)}")
    
    def test_main_function(self):
        if not hasattr(self.student_module, 'main'):
            self.feedback.append("✗ 缺少main函数")
            return
        
        try:
            # 模拟所有API调用
            mock_iss_response = MagicMock()
            mock_iss_response.json.return_value = {
                'latitude': 25.0, 'longitude': 120.0, 'altitude': 400.0, 'velocity': 27000.0
            }
            mock_iss_response.raise_for_status.return_value = None
            
            mock_geo_response = MagicMock()
            mock_geo_response.json.return_value = {'display_name': '台湾'}
            mock_geo_response.raise_for_status.return_value = None
            
            mock_astro_response = MagicMock()
            mock_astro_response.json.return_value = {
                'number': 2, 'people': [{'craft': 'ISS', 'name': 'Test1'}, {'craft': 'ISS', 'name': 'Test2'}]
            }
            mock_astro_response.raise_for_status.return_value = None
            
            def mock_get(url, **kwargs):
                if 'wheretheiss' in url:
                    return mock_iss_response
                elif 'nominatim' in url:
                    return mock_geo_response
                elif 'astros' in url:
                    return mock_astro_response
                return MagicMock()
            
            captured_output = io.StringIO()
            with patch('requests.get', side_effect=mock_get):
                with contextlib.redirect_stdout(captured_output):
                    self.student_module.main()
            
            output = captured_output.getvalue()
            if output and len(output) > 50:
                self.score += 10
                self.feedback.append("✓ main函数能够正常运行并产生输出 (+10分)")
            else:
                self.feedback.append("✗ main函数输出不足或无输出")
                
        except Exception as e:
            self.feedback.append(f"✗ main函数测试失败: {str(e)}")
    
    def test_bonus_features(self):
        bonus_score = 0
        
        # 检查异常处理
        source_code = open(self.student_file_path, 'r', encoding='utf-8').read()
        if 'try:' in source_code and 'except' in source_code:
            bonus_score += 5
            self.feedback.append("✓ 实现了异常处理 (+5分)")
        
        # 检查User-Agent设置
        if 'User-Agent' in source_code:
            bonus_score += 3
            self.feedback.append("✓ 设置了User-Agent (+3分)")
        
        # 检查可选功能
        if any(keyword in source_code for keyword in ['轨道', 'orbit', '预测', 'predict']):
            bonus_score += 5
            self.feedback.append("✓ 实现了轨道预测功能 (+5分)")
        
        if any(keyword in source_code for keyword in ['可视化', 'plot', 'matplotlib']):
            bonus_score += 5
            self.feedback.append("✓ 实现了数据可视化功能 (+5分)")
        
        if any(keyword in source_code for keyword in ['定时', 'time.sleep', 'schedule']):
            bonus_score += 3
            self.feedback.append("✓ 实现了定时更新功能 (+3分)")
        
        self.score += min(bonus_score, 20)
        if bonus_score > 0:
            self.feedback.append(f"总计获得奖励分: {min(bonus_score, 20)}分")
    
    def grade(self):
        if not self.load_student_code():
            return self.generate_report()
        
        self.test_imports()
        self.test_get_iss_location()
        self.test_get_location_name()
        self.test_get_astronauts_info()
        self.test_analyze_astronauts()
        self.test_main_function()
        self.test_bonus_features()
        
        return self.generate_report()
    
    def generate_report(self):
        percentage = (self.score / self.max_score) * 100
        
        if percentage >= 90:
            grade = "优秀"
        elif percentage >= 80:
            grade = "良好"
        elif percentage >= 70:
            grade = "中等"
        elif percentage >= 60:
            grade = "及格"
        else:
            grade = "不及格"
        
        report = f"""
=== ISS追踪器自动评分报告 ===

总分: {self.score}/{self.max_score} ({percentage:.1f}%)
等级: {grade}

详细反馈:
"""
        for feedback in self.feedback:
            report += f"  {feedback}\n"
        
        report += f"""
评分标准:
- 导入库 (5分)
- get_iss_location函数 (20分)
- get_location_name函数 (20分)
- get_astronauts_info函数 (15分)
- analyze_astronauts函数 (20分)
- main函数 (10分)
- 代码质量和奖励功能 (10分)

建议:
"""
        if percentage < 60:
            report += "- 建议重新学习API调用基础知识\n- 注意函数的返回值格式\n"
        elif percentage < 80:
            report += "- 继续完善异常处理\n- 尝试实现一些可选功能\n"
        else:
            report += "- 代码质量很好！可以尝试更多挑战功能\n"
        
        return report

def main():
    import sys
    if len(sys.argv) != 2:
        print("使用方法: python auto_grader.py <学生代码文件路径>")
        return
    
    grader = ISSTrackerGrader(sys.argv[1])
    report = grader.grade()
    print(report)

if __name__ == "__main__":
    main()
