import requests
import json
import time

def test_iss_api():
    """测试ISS位置API"""
    print("=== 测试ISS位置API ===")
    url = "https://api.wheretheiss.at/v1/satellites/25544"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        
        print("✓ API连接成功")
        print(f"状态码: {response.status_code}")
        print("返回数据示例:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ API连接失败: {e}")
        return False

def test_geo_api():
    """测试地理编码API"""
    print("\n=== 测试地理编码API ===")
    url = "https://nominatim.openstreetmap.org/reverse"
    params = {
        'format': 'jsonv2',
        'lat': 25.0,
        'lon': 121.0,
        'accept-language': 'zh-CN'
    }
    headers = {
        'User-Agent': 'TestApp/1.0'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        data = response.json()
        
        print("✓ API连接成功")
        print(f"状态码: {response.status_code}")
        print("返回数据示例:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ API连接失败: {e}")
        return False

def test_astronauts_api():
    """测试宇航员API"""
    print("\n=== 测试宇航员API ===")
    url = "http://api.open-notify.org/astros.json"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        
        print("✓ API连接成功")
        print(f"状态码: {response.status_code}")
        print("返回数据示例:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ API连接失败: {e}")
        return False

def test_complete_workflow():
    """测试完整的API调用流程"""
    print("\n=== 测试完整流程 ===")
    
    # 获取ISS位置
    iss_url = "https://api.wheretheiss.at/v1/satellites/25544"
    try:
        iss_response = requests.get(iss_url)
        iss_response.raise_for_status()
        iss_data = iss_response.json()
        
        latitude = iss_data.get('latitude')
        longitude = iss_data.get('longitude')
        
        print(f"✓ 获取ISS位置: 纬度 {latitude:.4f}, 经度 {longitude:.4f}")
        
        # 获取地名
        geo_url = "https://nominatim.openstreetmap.org/reverse"
        params = {
            'format': 'jsonv2',
            'lat': latitude,
            'lon': longitude,
            'accept-language': 'zh-CN'
        }
        headers = {
            'User-Agent': 'TestApp/1.0'
        }
        
        time.sleep(1)  # 避免请求过快
        
        geo_response = requests.get(geo_url, params=params, headers=headers)
        geo_response.raise_for_status()
        geo_data = geo_response.json()
        
        location_name = geo_data.get('display_name', '未知位置')
        print(f"✓ 获取地名: {location_name}")
        
        # 获取宇航员信息
        astro_url = "http://api.open-notify.org/astros.json"
        astro_response = requests.get(astro_url)
        astro_response.raise_for_status()
        astro_data = astro_response.json()
        
        print(f"✓ 获取宇航员信息: 太空中有 {astro_data['number']} 名宇航员")
        
        print("\n✓ 完整流程测试成功！")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 完整流程测试失败: {e}")
        return False

def show_api_documentation():
    """显示API文档"""
    print("\n=== API使用文档 ===")
    
    print("\n1. ISS位置API:")
    print("   URL: https://api.wheretheiss.at/v1/satellites/25544")
    print("   方法: GET")
    print("   返回字段: latitude, longitude, altitude, velocity")
    
    print("\n2. 地理编码API:")
    print("   URL: https://nominatim.openstreetmap.org/reverse")
    print("   方法: GET")
    print("   参数: format=jsonv2, lat=纬度, lon=经度, accept-language=zh-CN")
    print("   请求头: User-Agent必须设置")
    print("   返回字段: display_name")
    
    print("\n3. 宇航员API:")
    print("   URL: http://api.open-notify.org/astros.json")
    print("   方法: GET")
    print("   返回字段: number, people, message")
    
    print("\n注意事项:")
    print("- 所有API都需要网络连接")
    print("- 地理编码API需要设置User-Agent")
    print("- 建议在请求间添加适当延时")
    print("- 注意处理异常情况")

def main():
    print("API连接测试工具")
    print("="*40)
    
    # 测试各个API
    iss_ok = test_iss_api()
    geo_ok = test_geo_api()
    astro_ok = test_astronauts_api()
    
    if iss_ok and geo_ok and astro_ok:
        test_complete_workflow()
    else:
        print("\n部分API连接失败，请检查网络连接")
    
    show_api_documentation()

if __name__ == "__main__":
    main()
