import requests

def get_iss_location():
    iss_url = "https://api.wheretheiss.at/v1/satellites/25544"
    
    try:
        response = requests.get(iss_url)
        response.raise_for_status()
        
        iss_data = response.json()
        latitude = iss_data.get('latitude')
        longitude = iss_data.get('longitude')
        altitude = iss_data.get('altitude')
        velocity = iss_data.get('velocity')
        
        if latitude and longitude:
            print(f"ISS坐标: 纬度 {latitude:.4f}, 经度 {longitude:.4f}")
            print(f"高度: {altitude:.2f} 公里, 速度: {velocity:.2f} 公里/小时")
            return latitude, longitude
        else:
            return None, None
            
    except requests.exceptions.RequestException as e:
        print(f"获取ISS位置失败: {e}")
        return None, None

def get_location_name(latitude, longitude):
    if not latitude or not longitude:
        return "未知区域"
    
    try:
        geo_url = "https://nominatim.openstreetmap.org/reverse"
        params = {
            'format': 'jsonv2',
            'lat': latitude,
            'lon': longitude,
            'accept-language': 'zh-CN'
        }
        headers = {
            'User-Agent': 'HelloWorld/1.0'
        }
        
        geo_response = requests.get(geo_url, params=params, headers=headers)
        geo_response.raise_for_status()
        geo_data = geo_response.json()
        
        location_name = geo_data.get('display_name', '海洋或偏远地区上空')
        return location_name
        
    except requests.exceptions.RequestException as e:
        print(f"获取地理位置失败: {e}")
        return "海洋或偏远地区上空"

def get_astronauts_info():
    astronauts_url = "http://api.open-notify.org/astros.json"
    
    try:
        response = requests.get(astronauts_url)
        response.raise_for_status()
        astronauts_data = response.json()
        
        print(f"太空中总共有 {astronauts_data['number']} 名宇航员")
        return astronauts_data
        
    except requests.exceptions.RequestException as e:
        print(f"获取宇航员信息失败: {e}")
        backup_data = {
            "people": [
                {"craft": "ISS", "name": "Oleg Kononenko"},
                {"craft": "ISS", "name": "Nikolai Chub"},
                {"craft": "ISS", "name": "Tracy Caldwell Dyson"},
                {"craft": "ISS", "name": "Matthew Dominick"},
                {"craft": "ISS", "name": "Michael Barratt"},
                {"craft": "ISS", "name": "Jeanette Epps"},
                {"craft": "ISS", "name": "Alexander Grebenkin"},
                {"craft": "ISS", "name": "Butch Wilmore"},
                {"craft": "ISS", "name": "Sunita Williams"},
                {"craft": "Tiangong", "name": "Li Guangsu"},
                {"craft": "Tiangong", "name": "Li Cong"},
                {"craft": "Tiangong", "name": "Ye Guangfu"}
            ],
            "number": 12,
            "message": "success"
        }
        return backup_data

def analyze_astronauts(astronauts_data):
    craft_count = {}
    
    for astronaut in astronauts_data['people']:
        craft = astronaut['craft']
        
        if craft in craft_count:
            craft_count[craft] += 1
        else:
            craft_count[craft] = 1
    
    print("各飞行器宇航员分布：")
    for craft, count in craft_count.items():
        print(f"{craft}: {count} 人")
    
    iss_count = craft_count.get('ISS', 0)
    tiangong_count = craft_count.get('Tiangong', 0)
    
    print(f"国际空间站(ISS): {iss_count} 人")
    print(f"中国天宫空间站: {tiangong_count} 人")
    
    return craft_count

def main():
    print("=== ISS 追踪器 ===")
    
    latitude, longitude = get_iss_location()
    location_name = get_location_name(latitude, longitude)
    
    if latitude and longitude:
        print(f"当前位置: {location_name}")
    
    print("\n=== 宇航员信息 ===")
    astronauts_data = get_astronauts_info()
    analyze_astronauts(astronauts_data)

if __name__ == "__main__":
    main()
