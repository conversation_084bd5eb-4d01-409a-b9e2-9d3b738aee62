import requests
import json
import time
import hashlib
import os
from playsound import playsound

def lookup_word(word):
    # 构建词典API的URL
    url = f"https://api.dictionaryapi.dev/api/v2/entries/en/{word}"
    
    # 发送GET请求
    response = requests.get(url)
    
    # 检查响应状态码并处理JSON数据
    if response.status_code == 200:
        data = response.json()
        
        # 解析JSON数据结构，提取单词信息
        # data是一个列表，需要获取第一个元素
        first_result = data[0]
        
        print(f"单词: {first_result.get('word', 'N/A')}")
        
        # 遍历meanings列表，提取词性和定义
        if 'meanings' in first_result:
            for meaning in first_result['meanings']:
                # 提取词性
                part_of_speech = meaning.get('partOfSpeech', 'N/A')
                print(f"\n词性: {part_of_speech}")
                
                # 遍历定义列表
                for i, definition in enumerate(meaning.get('definitions', [])):
                    # 提取具体定义
                    def_text = definition.get('definition', 'N/A')
                    print(f"  {i+1}. {def_text}")
        
        return True
    else:
        print(f"查询失败: {response.status_code}")
        return False

def translate_text(text, from_lang="en", to_lang="zh"):
    # 配置API信息
    APP_ID = "WML1752072320905"
    APP_KEY = "571ed8d31b353b8069d40fb1ab96cee7"
    
    url = "https://api.niutrans.com/v2/text/translate"
    
    # 生成时间戳
    salt = str(int(time.time()))
    
    # 构建签名字符串
    sign_raw = f"apikey={APP_KEY}&appId={APP_ID}&from={from_lang}&srcText={text}&timestamp={salt}&to={to_lang}"
    
    # 计算MD5签名
    sign = hashlib.md5(sign_raw.encode('utf-8')).hexdigest()
    
    # 构建请求参数字典
    params = {
        "from": from_lang, 
        "to": to_lang,
        "appId": APP_ID, 
        "timestamp": salt,
        "authStr": sign, 
        "srcText": text
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, data=params)
        
        if response.status_code == 200:
            result = response.json()
            # 提取翻译结果
            return result.get('tgtText', '翻译失败')
        return f"请求失败: {response.status_code}"
    except Exception as e:
        return f"异常: {e}"

def text_to_speech(text):
    API_URL = "http://127.0.0.1:5000/synthesize"
    
    # 构建请求载荷
    payload = {"text": text}
    
    try:
        # 发送POST请求
        response = requests.post(API_URL, json=payload, timeout=30)
        response.raise_for_status()
        
        # 检查响应类型是否为音频
        if 'audio/wav' in response.headers.get('Content-Type', '').lower():
            # 将音频数据写入临时文件
            with open("tts.wav", "wb") as tmp_file:
                # 写入二进制数据
                tmp_file.write(response.content)
                tmp_file_path = tmp_file.name
            
            # 播放音频文件
            playsound(tmp_file_path)
            
            # 删除临时文件
            os.remove(tmp_file_path)
            
            return True
        return False
    except Exception as e:
        print(f"TTS失败: {e}")
        return False

def extract_key_info(chinese_text):
    # 从翻译后的中文文本中提取关键信息
    # 寻找包含"成分"或"剂量"的句子
    key_info_list = []
    
    # 分割句子并查找关键词
    for sentence in chinese_text.replace('。', '.').split('.'):
        # 检查句子是否包含关键词
        if '成分' in sentence or '剂量' in sentence:
            key_info_list.append(sentence.strip())
    
    # 将关键信息拼接成字符串
    return "。".join(key_info_list)

def medicine_reader():
    spanish_text = "Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido."
    
    print("原始文本:", spanish_text)
    
    # 调用翻译函数，从西班牙语翻译到中文
    chinese_text = translate_text(spanish_text, from_lang="es", to_lang="zh")
    print(f"翻译结果: {chinese_text}")
    
    # 检查翻译是否成功
    if isinstance(chinese_text, str) and "失败" not in chinese_text:
        # 提取关键信息
        key_info = extract_key_info(chinese_text)
        print(f"关键信息: {key_info}")
        
        # 朗读关键信息
        if key_info:
            text_to_speech(key_info)

def main():
    print("=== 词典查询测试 ===")
    # 测试词典查询功能
    lookup_word("apple")
    
    print("\n=== 翻译测试 ===")
    # 测试翻译功能
    result = translate_text("apple")
    print(f"翻译结果: {result}")
    
    print("\n=== 语音测试 ===")
    # 测试语音合成功能
    text_to_speech("测试音频")
    
    print("\n=== 药品说明解读器 ===")
    # 运行综合应用
    medicine_reader()

if __name__ == "__main__":
    main()
