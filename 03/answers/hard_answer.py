import requests
import json
import time
import hashlib
import os
from playsound import playsound

class DictionaryAPI:
    def __init__(self):
        self.base_url = "https://api.dictionaryapi.dev/api/v2/entries/en"
    
    def lookup_word(self, word):
        url = f"{self.base_url}/{word}"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            return self._parse_word_data(data[0])
        return None
    
    def _parse_word_data(self, word_data):
        result = {
            'word': word_data.get('word', 'N/A'),
            'phonetic': word_data.get('phonetic', 'N/A'),
            'meanings': []
        }
        
        for meaning in word_data.get('meanings', []):
            meaning_info = {
                'partOfSpeech': meaning.get('partOfSpeech', 'N/A'),
                'definitions': [d.get('definition', 'N/A') for d in meaning.get('definitions', [])]
            }
            result['meanings'].append(meaning_info)
        
        return result
    
    def get_pronunciation_url(self, word_data):
        if 'phonetics' in word_data:
            for phonetic in word_data['phonetics']:
                if 'audio' in phonetic and phonetic['audio']:
                    return phonetic['audio']
        return None

class TranslationAPI:
    def __init__(self, app_id, app_key):
        self.app_id = app_id
        self.app_key = app_key
        self.base_url = "https://api.niutrans.com/v2/text/translate"
    
    def translate(self, text, from_lang="en", to_lang="zh"):
        salt = str(int(time.time()))
        sign_raw = f"apikey={self.app_key}&appId={self.app_id}&from={from_lang}&srcText={text}&timestamp={salt}&to={to_lang}"
        sign = hashlib.md5(sign_raw.encode('utf-8')).hexdigest()
        
        params = {
            "from": from_lang, "to": to_lang,
            "appId": self.app_id, "timestamp": salt,
            "authStr": sign, "srcText": text
        }
        
        try:
            response = requests.post(self.base_url, data=params)
            if response.status_code == 200:
                result = response.json()
                return result.get('tgtText', '翻译失败')
            return f"请求失败: {response.status_code}"
        except Exception as e:
            return f"异常: {e}"
    
    def batch_translate(self, texts, from_lang="en", to_lang="zh"):
        results = []
        for text in texts:
            result = self.translate(text, from_lang, to_lang)
            results.append(result)
            time.sleep(0.1)  # 避免请求过快
        return results

class TTSService:
    def __init__(self, server_url="http://127.0.0.1:5000"):
        self.server_url = server_url
    
    def synthesize(self, text):
        url = f"{self.server_url}/synthesize"
        payload = {"text": text}
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            if 'audio/wav' in response.headers.get('Content-Type', '').lower():
                with open("tts.wav", "wb") as tmp_file:
                    tmp_file.write(response.content)
                    tmp_file_path = tmp_file.name
                
                playsound(tmp_file_path)
                os.remove(tmp_file_path)
                return True
            return False
        except Exception as e:
            print(f"TTS失败: {e}")
            return False
    
    def save_audio(self, text, filename):
        url = f"{self.server_url}/synthesize"
        payload = {"text": text}
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            if 'audio/wav' in response.headers.get('Content-Type', '').lower():
                with open(filename, "wb") as f:
                    f.write(response.content)
                return True
            return False
        except Exception as e:
            print(f"保存音频失败: {e}")
            return False

class TextProcessor:
    def __init__(self):
        pass
    
    def extract_key_info(self, text, keywords):
        key_info_list = []
        sentences = text.replace('。', '.').split('.')
        
        for sentence in sentences:
            sentence = sentence.strip()
            if any(keyword in sentence for keyword in keywords):
                key_info_list.append(sentence)
        
        return "。".join(key_info_list)
    
    def clean_text(self, text):
        # 移除多余的空格和特殊字符
        import re
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        return text

class MedicineReader:
    def __init__(self, dict_api, trans_api, tts_service, processor):
        self.dict_api = dict_api
        self.trans_api = trans_api
        self.tts_service = tts_service
        self.processor = processor
    
    def process_medicine_info(self, text, source_lang="es"):
        print(f"原始文本 ({source_lang}): {text}")
        
        # 翻译
        translated = self.trans_api.translate(text, from_lang=source_lang, to_lang="zh")
        print(f"翻译结果: {translated}")
        
        if isinstance(translated, str) and "失败" not in translated:
            # 提取关键信息
            keywords = ['成分', '剂量', '用法', '用量']
            key_info = self.processor.extract_key_info(translated, keywords)
            print(f"关键信息: {key_info}")
            
            # 语音播放
            if key_info:
                print("正在朗读关键信息...")
                self.tts_service.synthesize(key_info)
            
            return {
                'original': text,
                'translated': translated,
                'key_info': key_info
            }
        
        return None
    
    def interactive_mode(self):
        print("进入交互式模式，输入'quit'退出")
        while True:
            text = input("请输入要处理的文本: ").strip()
            if text.lower() == 'quit':
                break
            
            lang = input("请输入源语言代码 (如: es, en, fr): ").strip()
            if not lang:
                lang = "es"
            
            result = self.process_medicine_info(text, lang)
            if result:
                save = input("是否保存处理结果? (y/n): ").strip().lower()
                if save == 'y':
                    filename = f"medicine_report_{int(time.time())}.txt"
                    self.save_report(result['original'], result['translated'], 
                                   result['key_info'], filename)
                    print(f"结果已保存到: {filename}")
    
    def save_report(self, original_text, translated_text, key_info, filename):
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("药品说明书处理报告\n")
                f.write("=" * 30 + "\n\n")
                f.write(f"原始文本:\n{original_text}\n\n")
                f.write(f"翻译结果:\n{translated_text}\n\n")
                f.write(f"关键信息:\n{key_info}\n\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            return True
        except Exception as e:
            print(f"保存报告失败: {e}")
            return False

def main():
    # 配置API
    APP_ID = "WML1752072320905"
    APP_KEY = "571ed8d31b353b8069d40fb1ab96cee7"
    
    # 初始化各个服务
    dict_api = DictionaryAPI()
    trans_api = TranslationAPI(APP_ID, APP_KEY)
    tts_service = TTSService()
    processor = TextProcessor()
    
    # 创建药品说明解读器
    reader = MedicineReader(dict_api, trans_api, tts_service, processor)
    
    # 测试数据
    spanish_medicine_text = "Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido."
    
    print("=== 基础功能测试 ===")
    # 测试词典查询
    word_info = dict_api.lookup_word("vitamin")
    if word_info:
        print(f"单词: {word_info['word']}")
        print(f"音标: {word_info['phonetic']}")
    
    # 测试翻译
    translation = trans_api.translate("vitamin")
    print(f"翻译: {translation}")
    
    # 测试语音合成
    tts_service.synthesize("测试音频")
    
    print("\n=== 综合应用测试 ===")
    # 测试药品说明解读器
    reader.process_medicine_info(spanish_medicine_text)
    
    print("\n=== 可选功能测试 ===")
    # 测试批量翻译
    texts = ["apple", "banana", "orange"]
    batch_results = trans_api.batch_translate(texts)
    print(f"批量翻译结果: {batch_results}")
    
    # 测试交互式模式（注释掉避免阻塞）
    # reader.interactive_mode()

if __name__ == "__main__":
    main()
