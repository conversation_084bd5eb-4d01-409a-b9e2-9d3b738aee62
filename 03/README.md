# Python文本处理API教学项目

本项目基于L3.ipynb课件内容，为小学生提供Python文本处理API学习模板。

## 文件夹结构

### teacher/
老师专用文件夹，包含：
- 完整的示例代码和运行结果
- TTS服务器代码
- 辅助工具和配置文件

### answers/
标准答案文件夹，包含：
- 三种难度的完整解答
- 详细的代码注释和说明

### templates/
学生模板文件夹，包含：
- simple.py - 简单难度（填空题）
- medium.py - 中等难度（骨架代码）
- hard.py - 困难难度（框架代码）
- grader.py - 自动测试打分工具

## 学习目标

1. 理解GET和POST请求的区别
2. 掌握复杂JSON数据解析
3. 学习API密钥和签名认证
4. 处理多媒体流数据
5. 整合多个API构建应用

## 使用说明

1. 老师先运行teacher/tts_server.py启动TTS服务
2. 学生根据自己水平选择对应难度的模板
3. 完成代码后使用grader.py进行自动测试
4. 困难难度包含可选的扩展功能

## 注意事项

- 需要安装requests和playsound库
- 需要注册小牛翻译API获取密钥
- TTS服务需要在本地网络环境运行
