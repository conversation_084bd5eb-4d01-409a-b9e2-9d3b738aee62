import importlib.util
import sys
import io
import contextlib
import traceback
import requests
import json
import time
import hashlib

class AutoGrader:
    def __init__(self):
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
    
    def load_student_code(self, file_path):
        try:
            spec = importlib.util.spec_from_file_location("student_code", file_path)
            student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(student_module)
            return student_module
        except Exception as e:
            print(f"加载学生代码失败: {e}")
            return None
    
    def capture_output(self, func, *args, **kwargs):
        old_stdout = sys.stdout
        sys.stdout = captured_output = io.StringIO()
        
        try:
            result = func(*args, **kwargs)
            output = captured_output.getvalue()
            return result, output, None
        except Exception as e:
            output = captured_output.getvalue()
            return None, output, str(e)
        finally:
            sys.stdout = old_stdout
    
    def test_dictionary_api(self, student_module):
        print("测试词典API功能...")
        score = 0
        
        if hasattr(student_module, 'lookup_word'):
            try:
                result, output, error = self.capture_output(student_module.lookup_word, "test")
                
                if error is None:
                    score += 10
                    print("✓ 词典API调用成功 (+10分)")
                    
                    if "单词:" in output and "词性:" in output:
                        score += 10
                        print("✓ 正确解析JSON数据 (+10分)")
                    else:
                        print("✗ JSON数据解析不完整")
                else:
                    print(f"✗ 词典API调用失败: {error}")
            except Exception as e:
                print(f"✗ 词典API测试异常: {e}")
        else:
            print("✗ 未找到lookup_word函数")
        
        return score
    
    def test_translation_api(self, student_module):
        print("测试翻译API功能...")
        score = 0
        
        if hasattr(student_module, 'translate_text'):
            try:
                # 检查是否配置了API密钥
                result, output, error = self.capture_output(student_module.translate_text, "hello")
                
                if error is None:
                    if "你的APP_ID" not in str(result) and "你的APP_KEY" not in str(result):
                        score += 15
                        print("✓ API密钥配置正确 (+15分)")
                        
                        if isinstance(result, str) and len(result) > 0:
                            score += 10
                            print("✓ 翻译功能正常 (+10分)")
                        else:
                            print("✗ 翻译结果格式错误")
                    else:
                        print("✗ 请配置正确的API密钥")
                        score += 5
                        print("✓ 函数结构正确 (+5分)")
                else:
                    print(f"✗ 翻译API调用失败: {error}")
            except Exception as e:
                print(f"✗ 翻译API测试异常: {e}")
        else:
            print("✗ 未找到translate_text函数")
        
        return score
    
    def test_tts_functionality(self, student_module):
        print("测试TTS功能...")
        score = 0
        
        if hasattr(student_module, 'text_to_speech'):
            try:
                result, output, error = self.capture_output(student_module.text_to_speech, "测试")
                
                if error is None:
                    score += 10
                    print("✓ TTS函数调用成功 (+10分)")
                    
                    if "tts.wav" in output or result is True:
                        score += 10
                        print("✓ 音频处理逻辑正确 (+10分)")
                else:
                    if "连接" in str(error) or "timeout" in str(error).lower():
                        score += 5
                        print("✓ TTS函数结构正确，服务器未运行 (+5分)")
                    else:
                        print(f"✗ TTS功能错误: {error}")
            except Exception as e:
                print(f"✗ TTS测试异常: {e}")
        else:
            print("✗ 未找到text_to_speech函数")
        
        return score
    
    def test_integration(self, student_module):
        print("测试综合应用...")
        score = 0
        
        if hasattr(student_module, 'medicine_reader'):
            try:
                result, output, error = self.capture_output(student_module.medicine_reader)
                
                if error is None:
                    score += 10
                    print("✓ 综合应用运行成功 (+10分)")
                    
                    if "原始" in output and "翻译" in output:
                        score += 10
                        print("✓ 完整处理流程 (+10分)")
                    
                    if "关键信息" in output or "成分" in output:
                        score += 10
                        print("✓ 关键信息提取 (+10分)")
                else:
                    print(f"✗ 综合应用失败: {error}")
            except Exception as e:
                print(f"✗ 综合应用测试异常: {e}")
        else:
            print("✗ 未找到medicine_reader函数")
        
        return score
    
    def test_code_quality(self, file_path):
        print("测试代码质量...")
        score = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # 检查是否有基本的错误处理
            if "try:" in code and "except" in code:
                score += 5
                print("✓ 包含错误处理 (+5分)")
            
            # 检查是否有适当的函数分解
            function_count = code.count("def ")
            if function_count >= 3:
                score += 5
                print("✓ 良好的函数结构 (+5分)")
            
            # 检查是否移除了占位符
            if "你的APP_ID" not in code or "你的APP_KEY" not in code:
                score += 5
                print("✓ 正确配置API信息 (+5分)")
            
        except Exception as e:
            print(f"代码质量检查失败: {e}")
        
        return score
    
    def grade_student_work(self, file_path):
        print(f"开始评分学生作业: {file_path}")
        print("=" * 50)
        
        student_module = self.load_student_code(file_path)
        if not student_module:
            print("无法加载学生代码，评分结束")
            return 0
        
        # 各项测试
        dict_score = self.test_dictionary_api(student_module)
        trans_score = self.test_translation_api(student_module)
        tts_score = self.test_tts_functionality(student_module)
        integration_score = self.test_integration(student_module)
        quality_score = self.test_code_quality(file_path)
        
        total_score = dict_score + trans_score + tts_score + integration_score + quality_score
        
        print("\n" + "=" * 50)
        print("评分结果:")
        print(f"词典API: {dict_score}/20分")
        print(f"翻译API: {trans_score}/25分")
        print(f"TTS功能: {tts_score}/20分")
        print(f"综合应用: {integration_score}/30分")
        print(f"代码质量: {quality_score}/15分")
        print(f"总分: {total_score}/110分")
        
        # 转换为百分制
        percentage = min(100, (total_score / 110) * 100)
        print(f"百分制得分: {percentage:.1f}/100分")
        
        if percentage >= 90:
            print("评级: 优秀")
        elif percentage >= 80:
            print("评级: 良好")
        elif percentage >= 70:
            print("评级: 中等")
        elif percentage >= 60:
            print("评级: 及格")
        else:
            print("评级: 需要改进")
        
        return percentage

def main():
    grader = AutoGrader()
    
    print("自动评分系统")
    print("支持的文件: simple.py, medium.py, hard.py")
    
    file_path = input("请输入要评分的Python文件路径: ").strip()
    
    if not file_path.endswith('.py'):
        file_path += '.py'
    
    try:
        score = grader.grade_student_work(file_path)
        print(f"\n最终得分: {score:.1f}分")
    except FileNotFoundError:
        print("文件未找到，请检查路径是否正确")
    except Exception as e:
        print(f"评分过程中出现错误: {e}")

if __name__ == "__main__":
    main()
