# 学生使用指南

## 开始之前

1. 确保已安装Python 3.7+
2. 安装必需的库：
   ```bash
   pip install requests playsound==1.2.2
   ```

## 选择难度

### 简单难度 (simple.py)
- 适合初学者
- 代码几乎完整，只需填空
- 重点学习API调用和JSON解析

### 中等难度 (medium.py)
- 适合有一定基础的学生
- 提供代码骨架，需要补充核心逻辑
- 重点学习函数设计和错误处理

### 困难难度 (hard.py)
- 适合有经验的学生
- 只提供类框架，需要实现所有功能
- 包含可选的扩展功能

## 配置步骤

1. 获取翻译API密钥：
   - 访问 https://niutrans.com 注册
   - 获取APP_ID和APP_KEY
   - 在代码中替换占位符

2. 确认TTS服务器运行：
   - 老师会启动TTS服务器
   - 确认能访问 http://127.0.0.1:5000

## 测试你的代码

完成编码后，使用自动评分工具：
```bash
python grader.py
```

输入你的Python文件名进行评分。

## 学习重点

1. **HTTP方法差异**：
   - GET：获取数据，参数在URL中
   - POST：提交数据，参数在请求体中

2. **JSON数据解析**：
   - 理解嵌套结构
   - 使用索引和键访问数据

3. **API认证**：
   - 理解签名机制
   - 学习MD5哈希计算

4. **二进制数据处理**：
   - 音频文件的接收和保存
   - 文件操作模式

## 常见问题

### 翻译API调用失败
- 检查APP_ID和APP_KEY是否正确
- 确认网络连接正常
- 注意不要频繁调用API

### 音频播放问题
- 确认playsound版本为1.2.2
- 检查TTS服务器是否运行
- 确认系统音量设置

### JSON解析错误
- 仔细检查数据结构
- 使用try-except处理异常
- 打印中间结果进行调试

## 扩展挑战

完成基础功能后，可以尝试：
1. 添加更多语言支持
2. 实现批量处理功能
3. 创建图形用户界面
4. 添加语音识别功能
