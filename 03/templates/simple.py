import requests
import json
import time
import hashlib
import os
from playsound import playsound

def lookup_word(word):
    url = f"https://api.dictionaryapi.dev/api/v2/entries/en/{word}"
    response = requests.get(url)
    
    if response.status_code == ____:  # 填空1: HTTP成功状态码
        data = response.____()  # 填空2: 获取JSON数据的方法
        first_result = data[____]  # 填空3: 获取列表第一个元素的索引
        
        print(f"单词: {first_result.get('word', 'N/A')}")
        
        if 'meanings' in first_result:
            for meaning in first_result['meanings']:
                part_of_speech = meaning.get('partOfSpeech', 'N/A')
                print(f"\n词性: {part_of_speech}")
                
                for i, definition in enumerate(meaning.get('definitions', [])):
                    print(f"  {i+1}. {definition.get('definition', 'N/A')}")
        
        return True
    else:
        print(f"查询失败: {response.status_code}")
        return False

def translate_text(text, from_lang="en", to_lang="zh"):
    APP_ID = "你的APP_ID"  # 填空4: 替换为实际的APP_ID
    APP_KEY = "你的APP_KEY"  # 填空5: 替换为实际的APP_KEY
    
    url = "https://api.niutrans.com/v2/text/translate"
    salt = str(int(time.time()))
    
    sign_raw = f"apikey={APP_KEY}&appId={APP_ID}&from={from_lang}&srcText={text}&timestamp={salt}&to={to_lang}"
    sign = hashlib.____(____.encode('utf-8')).hexdigest()  # 填空6: MD5算法名称, 填空7: 要编码的变量
    
    params = {
        "from": from_lang, "to": to_lang,
        "appId": APP_ID, "timestamp": salt,
        "authStr": sign, "srcText": text
    }
    
    try:
        response = requests.____(____, data=params)  # 填空8: POST请求方法, 填空9: URL变量
        if response.status_code == 200:
            result = response.json()
            return result.get('tgtText', '翻译失败')
        return f"请求失败: {response.status_code}"
    except Exception as e:
        return f"异常: {e}"

def text_to_speech(text):
    API_URL = "http://127.0.0.1:5000/synthesize"
    payload = {"text": text}
    
    try:
        response = requests.post(API_URL, ____=payload, timeout=30)  # 填空10: JSON参数名
        response.raise_for_status()
        
        if 'audio/wav' in response.headers.get('Content-Type', '').lower():
            with open("tts.wav", "____") as tmp_file:  # 填空11: 二进制写入模式
                tmp_file.write(response.____)  # 填空12: 获取二进制内容的属性
                tmp_file_path = tmp_file.name
            
            playsound(tmp_file_path)
            os.remove(tmp_file_path)
            return True
        return False
    except Exception as e:
        print(f"TTS失败: {e}")
        return False

def medicine_reader():
    spanish_text = "Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido."
    
    print("原始文本:", spanish_text)
    
    chinese_text = translate_text(spanish_text, from_lang="____", to_lang="____")  # 填空13: 西班牙语代码, 填空14: 中文代码
    print(f"翻译结果: {chinese_text}")
    
    if isinstance(chinese_text, str) and "失败" not in chinese_text:
        key_info_list = []
        for sentence in chinese_text.replace('。', '.').split('.'):
            if '成分' in sentence or '剂量' in sentence:
                key_info_list.append(sentence.strip())
        
        key_info = "。".join(key_info_list)
        print(f"关键信息: {key_info}")
        
        if key_info:
            text_to_speech(key_info)

if __name__ == "__main__":
    print("=== 词典查询测试 ===")
    lookup_word("apple")
    
    print("\n=== 翻译测试 ===")
    result = translate_text("apple")
    print(f"翻译结果: {result}")
    
    print("\n=== 语音测试 ===")
    text_to_speech("测试音频")
    
    print("\n=== 药品说明解读器 ===")
    medicine_reader()
