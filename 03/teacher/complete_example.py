import requests
import json
import time
import hashlib
import tempfile
import os
from playsound import playsound

def lookup_word(word):
    url = f"https://api.dictionaryapi.dev/api/v2/entries/en/{word}"
    response = requests.get(url)
    
    if response.status_code == 200:
        data = response.json()
        first_result = data[0]
        word_info = {
            'word': first_result.get('word', 'N/A'),
            'phonetic': first_result.get('phonetic', 'N/A'),
            'meanings': []
        }
        
        if 'meanings' in first_result:
            for meaning in first_result['meanings']:
                part_of_speech = meaning.get('partOfSpeech', 'N/A')
                definitions = []
                for definition in meaning.get('definitions', []):
                    definitions.append(definition.get('definition', 'N/A'))
                word_info['meanings'].append({
                    'partOfSpeech': part_of_speech,
                    'definitions': definitions
                })
        
        return word_info
    else:
        return None

def translate_text(text, from_lang="en", to_lang="zh"):
    APP_ID = "WML1752072320905"
    APP_KEY = "571ed8d31b353b8069d40fb1ab96cee7"
    
    url = "https://api.niutrans.com/v2/text/translate"
    salt = str(int(time.time()))
    sign_raw = f"apikey={APP_KEY}&appId={APP_ID}&from={from_lang}&srcText={text}&timestamp={salt}&to={to_lang}"
    sign = hashlib.md5(sign_raw.encode('utf-8')).hexdigest()
    
    params = {
        "from": from_lang, "to": to_lang,
        "appId": APP_ID, "timestamp": salt,
        "authStr": sign, "srcText": text
    }
    
    try:
        response = requests.post(url, data=params)
        if response.status_code == 200:
            result = response.json()
            return result.get('tgtText', f"翻译结果解析失败: {result}")
        return f"翻译API请求失败, 状态码: {response.status_code}"
    except Exception as e:
        return f"调用翻译API时发生异常: {e}"

def text_to_speech(text):
    API_URL = "http://127.0.0.1:5000/synthesize"
    payload = {"text": text}
    
    try:
        response = requests.post(API_URL, json=payload, timeout=30)
        response.raise_for_status()
        
        if 'audio/wav' in response.headers.get('Content-Type', '').lower():
            with open("tts.wav", "wb") as tmp_file:
                tmp_file.write(response.content)
                tmp_file_path = tmp_file.name
            
            playsound(tmp_file_path)
            os.remove(tmp_file_path)
            return True
        else:
            print("API未返回有效的音频文件")
            return False
    except Exception as e:
        print(f"TTS调用失败: {e}")
        return False

def medicine_reader():
    spanish_text = "Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido."
    
    print("原始西班牙语文本:")
    print(spanish_text)
    
    chinese_text = translate_text(spanish_text, from_lang="es", to_lang="zh")
    print(f"\n翻译结果: {chinese_text}")
    
    if isinstance(chinese_text, str) and "失败" not in chinese_text:
        key_info_list = []
        for sentence in chinese_text.replace('。', '.').split('.'):
            if '成分' in sentence or '剂量' in sentence:
                key_info_list.append(sentence.strip())
        
        key_info = "。".join(key_info_list)
        print(f"\n提取的关键信息: {key_info}")
        
        if key_info:
            print("\n正在朗读关键信息...")
            text_to_speech(key_info)

if __name__ == "__main__":
    print("=== 词典API测试 ===")
    word_info = lookup_word("apple")
    if word_info:
        print(f"单词: {word_info['word']}")
        print(f"音标: {word_info['phonetic']}")
        for meaning in word_info['meanings']:
            print(f"\n词性: {meaning['partOfSpeech']}")
            for i, definition in enumerate(meaning['definitions']):
                print(f"  {i+1}. {definition}")
    
    print("\n=== 翻译API测试 ===")
    translation = translate_text("apple")
    print(f"'apple' 的翻译: {translation}")
    
    print("\n=== TTS API测试 ===")
    text_to_speech("你好，这是一个测试音频")
    
    print("\n=== 综合应用：药品说明解读器 ===")
    medicine_reader()
