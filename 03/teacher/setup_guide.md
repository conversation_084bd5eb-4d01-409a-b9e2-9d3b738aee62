# 教师设置指南

## 环境准备

1. 确保Python 3.7+已安装
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

## TTS服务器启动

1. 运行TTS服务器：
   ```bash
   python tts_server.py
   ```
2. 服务器将在 http://127.0.0.1:5000 启动
3. 可通过 http://127.0.0.1:5000/health 检查服务状态

## API配置

### 小牛翻译API
1. 访问 https://niutrans.com 注册账号
2. 获取APP_ID和APP_KEY
3. 在学生模板中替换相应的占位符

## 课堂使用流程

1. 课前准备：
   - 启动TTS服务器
   - 确认网络连接正常
   - 准备API密钥信息

2. 课堂演示：
   - 运行complete_example.py展示完整功能
   - 解释各个API的工作原理
   - 演示JSON数据解析过程

3. 学生练习：
   - 根据学生水平分发对应难度模板
   - 指导学生配置API密钥
   - 使用grader.py进行自动评分

## 故障排除

### TTS服务器问题
- 检查端口5000是否被占用
- 确认pyttsx3库正确安装
- 在macOS上可能需要额外的语音引擎配置

### API调用问题
- 检查网络连接
- 验证API密钥是否正确
- 注意API调用频率限制

### 音频播放问题
- 确认playsound版本为1.2.2
- 检查系统音频设备
- 在Linux系统可能需要额外的音频库
