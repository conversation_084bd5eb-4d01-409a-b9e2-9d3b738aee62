from flask import Flask, request, jsonify, send_file
import pyttsx3
import tempfile
import os
import threading
import time

app = Flask(__name__)

class TTSService:
    def __init__(self):
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', 150)
        self.engine.setProperty('volume', 0.9)
        
    def text_to_speech(self, text):
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        temp_file.close()
        
        self.engine.save_to_file(text, temp_file.name)
        self.engine.runAndWait()
        
        return temp_file.name

tts_service = TTSService()

@app.route('/synthesize', methods=['POST'])
def synthesize():
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': 'Missing text parameter'}), 400
        
        text = data['text']
        if not text.strip():
            return jsonify({'error': 'Empty text'}), 400
        
        audio_file = tts_service.text_to_speech(text)
        
        def cleanup_file():
            time.sleep(10)
            try:
                os.unlink(audio_file)
            except:
                pass
        
        threading.Thread(target=cleanup_file).start()
        
        return send_file(audio_file, mimetype='audio/wav', as_attachment=True)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    return jsonify({'status': 'ok'})

if __name__ == '__main__':
    print("TTS服务器启动中...")
    print("访问地址: http://127.0.0.1:5000")
    print("健康检查: http://127.0.0.1:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=False)
