#!/usr/bin/env python3
"""
Python网络编程：Socket通信与图形化聊天室
"""

import socket
import threading
import json
import os
import time
from typing import Dict, Any


class ChatServer:
    """多线程聊天服务器"""
    
    def __init__(self, host='localhost', port=12345):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients = []
        self.running = False
    
    def start_server(self):
        """启动服务器"""
        try:
            # 1. 创建服务器socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 2. 绑定地址和端口
            self.server_socket.bind((self.host, self.port))
            
            # 3. 开始监听
            self.server_socket.listen(5)
            self.running = True
            print(f"服务器正在监听 {self.host}:{self.port}...")
            
            while self.running:
                try:
                    # 4. 接受连接
                    client_socket, addr = self.server_socket.accept()
                    self.clients.append(client_socket)
                    
                    # 为每个新连接创建一个新的线程
                    thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, addr)
                    )
                    thread.daemon = True
                    thread.start()
                    
                    print(f"[新连接] 客户端 {addr} 已连接。")
                    print(f"[活跃连接] 当前连接总数: {len(self.clients)}")
                    
                except socket.error:
                    if self.running:
                        print("服务器接受连接时发生错误")
                    break
                    
        except Exception as e:
            print(f"启动服务器时发生错误: {e}")
        finally:
            self.stop_server()
    
    def handle_client(self, client_socket: socket.socket, addr):
        """处理单个客户端的所有通信"""
        try:
            while self.running:
                # 接收消息头部长度
                header_length_bytes = client_socket.recv(10)
                if not header_length_bytes:
                    break
                
                header_length = int(header_length_bytes.decode('utf-8').strip())
                
                # 接收消息头部
                header_bytes = client_socket.recv(header_length)
                if not header_bytes:
                    break
                
                header = json.loads(header_bytes.decode('utf-8'))
                
                # 根据消息类型处理
                if header.get('type') == 'message':
                    self.handle_text_message(client_socket, addr, header)
                elif header.get('type') == 'file':
                    self.handle_file_transfer(client_socket, addr, header)
                else:
                    print(f"未知消息类型: {header}")
                    
        except Exception as e:
            print(f"处理客户端 {addr} 时发生错误: {e}")
        finally:
            print(f"[连接关闭] 客户端 {addr} 已断开。")
            if client_socket in self.clients:
                self.clients.remove(client_socket)
            client_socket.close()
    
    def handle_text_message(self, client_socket: socket.socket, addr, header: Dict[str, Any]):
        """处理文本消息"""
        message = header.get('data', '')
        print(f"收到来自 {addr} 的消息: {message}")
        
        # 广播消息给所有客户端
        response_header = {
            'type': 'message',
            'data': f"[{addr}]: {message}",
            'timestamp': time.time()
        }
        self.broadcast_message(response_header, exclude=client_socket)
    
    def handle_file_transfer(self, client_socket: socket.socket, addr, header: Dict[str, Any]):
        """处理文件传输"""
        filename = header.get('filename', 'unknown_file')
        filesize = header.get('filesize', 0)
        
        print(f"开始接收来自 {addr} 的文件: {filename} ({filesize} 字节)")
        
        # 创建接收文件夹
        os.makedirs('received_files', exist_ok=True)
        received_filename = os.path.join('received_files', f"received_{filename}")
        
        try:
            with open(received_filename, 'wb') as f:
                received_bytes = 0
                while received_bytes < filesize:
                    remaining = filesize - received_bytes
                    chunk = client_socket.recv(min(4096, remaining))
                    if not chunk:
                        break
                    f.write(chunk)
                    received_bytes += len(chunk)
            
            print(f"文件 '{filename}' 接收完毕，保存为 '{received_filename}'")
            
            # 通知所有客户端有新文件
            response_header = {
                'type': 'message',
                'data': f"[系统]: {addr} 上传了文件 '{filename}'",
                'timestamp': time.time()
            }
            self.broadcast_message(response_header)
            
        except Exception as e:
            print(f"接收文件时发生错误: {e}")
    
    def broadcast_message(self, header: Dict[str, Any], exclude=None):
        """广播消息给所有客户端"""
        header_bytes = json.dumps(header).encode('utf-8')
        header_length_bytes = str(len(header_bytes)).encode('utf-8').ljust(10)
        
        disconnected_clients = []
        for client in self.clients:
            if client != exclude:
                try:
                    client.send(header_length_bytes)
                    client.send(header_bytes)
                except:
                    disconnected_clients.append(client)
        
        # 清理断开的连接
        for client in disconnected_clients:
            if client in self.clients:
                self.clients.remove(client)
    
    def stop_server(self):
        """停止服务器"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        for client in self.clients:
            client.close()
        self.clients.clear()
        print("服务器已停止")


class ChatClient:
    """聊天客户端"""
    
    def __init__(self, host='localhost', port=12345):
        self.host = host
        self.port = port
        self.client_socket = None
        self.connected = False
    
    def connect_to_server(self):
        """连接到服务器"""
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.connect((self.host, self.port))
            self.connected = True
            print(f"已连接到服务器 {self.host}:{self.port}")
            
            # 启动接收消息的线程
            receive_thread = threading.Thread(target=self.receive_messages)
            receive_thread.daemon = True
            receive_thread.start()
            
            return True
        except Exception as e:
            print(f"连接服务器失败: {e}")
            return False
    
    def send_message(self, message: str):
        """发送文本消息"""
        if not self.connected:
            print("未连接到服务器")
            return
        
        try:
            header = {
                'type': 'message',
                'data': message,
                'timestamp': time.time()
            }
            self.send_header(header)
        except Exception as e:
            print(f"发送消息失败: {e}")
    
    def send_file(self, file_path: str):
        """发送文件"""
        if not self.connected:
            print("未连接到服务器")
            return
        
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return
        
        try:
            filename = os.path.basename(file_path)
            filesize = os.path.getsize(file_path)
            
            # 发送文件头部信息
            header = {
                'type': 'file',
                'filename': filename,
                'filesize': filesize
            }
            self.send_header(header)
            
            # 发送文件数据
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    self.client_socket.sendall(chunk)
            
            print(f"文件 '{filename}' 发送完毕")
            
        except Exception as e:
            print(f"发送文件失败: {e}")
    
    def send_header(self, header: Dict[str, Any]):
        """发送消息头部"""
        header_bytes = json.dumps(header).encode('utf-8')
        header_length_bytes = str(len(header_bytes)).encode('utf-8').ljust(10)
        
        self.client_socket.send(header_length_bytes)
        self.client_socket.send(header_bytes)
    
    def receive_messages(self):
        """接收消息的线程函数"""
        while self.connected:
            try:
                # 接收消息头部长度
                header_length_bytes = self.client_socket.recv(10)
                if not header_length_bytes:
                    break
                
                header_length = int(header_length_bytes.decode('utf-8').strip())
                
                # 接收消息头部
                header_bytes = self.client_socket.recv(header_length)
                if not header_bytes:
                    break
                
                header = json.loads(header_bytes.decode('utf-8'))
                
                if header.get('type') == 'message':
                    print(f"\n{header.get('data', '')}")
                    
            except Exception as e:
                if self.connected:
                    print(f"接收消息时发生错误: {e}")
                break
    
    def disconnect(self):
        """断开连接"""
        self.connected = False
        if self.client_socket:
            self.client_socket.close()
        print("已断开连接")


def main():
    """主函数 - 演示程序使用"""
    print("Python Socket 聊天程序")
    print("1. 启动服务器")
    print("2. 启动客户端")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == '1':
        # 启动服务器
        server = ChatServer()
        try:
            server.start_server()
        except KeyboardInterrupt:
            print("\n正在关闭服务器...")
            server.stop_server()
    
    elif choice == '2':
        # 启动客户端
        client = ChatClient()
        if client.connect_to_server():
            try:
                while True:
                    user_input = input("\n输入消息 (或 'file:文件路径' 发送文件, 'quit' 退出): ").strip()
                    
                    if user_input.lower() == 'quit':
                        break
                    elif user_input.startswith('file:'):
                        file_path = user_input[5:].strip()
                        client.send_file(file_path)
                    else:
                        client.send_message(user_input)
                        
            except KeyboardInterrupt:
                print("\n正在断开连接...")
            finally:
                client.disconnect()
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
