{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# Python文本处理API入门：从查词到翻译"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["欢迎来到Python文本处理API课程！在这个课程中, 我们将探索如何利用Python调用词典API、翻译API以及文字转语音服务。\n", "\n", "这些工具就像魔法一样, 可以帮助我们轻松处理和转换文本。有了这些技能, 你可以创建自己的翻译软件、发音助手, 甚至是多语言学习工具！"]}, {"cell_type": "markdown", "id": "7b61c097", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 课程目标\n", "\n", "*   **深入HTTP方法**: 真正理解`GET`和`POST`请求的区别与应用场景。\n", "*   **驾驭复杂JSON**: 学习解析层层嵌套的JSON数据，就像寻宝一样找到信息。\n", "*   **使用专业API**: 掌握需要API密钥和复杂签名认证的`POST`请求方法。\n", "*   **处理多媒体流**: 学习如何接收和处理API返回的音频文件。\n", "*   **整合应用**: 将所有技能融会贯通，打造一个智能药品说明解读器。"]}, {"cell_type": "markdown", "id": "7440b808", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 概念回顾与深化：从`GET`到`POST`"]}, {"cell_type": "markdown", "id": "13710d5f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### `GET` 请求：大声说出你的要求\n", "- **用途**: 从服务器**获取（Get）**数据，比如查一个单词、看一条新闻。\n", "- **特点**: 参数直接附加在URL中，用`?`和`&`连接。这就像在图书馆大声问：“请给我关于**苹果**的书”。所有人都能看到你的要求。\n", "- **例子**: `https://api.example.com/books?topic=apple`\n", "\n", "我们之前查国际空间站位置、查汇率，用的都是`GET`请求。"]}, {"cell_type": "markdown", "id": "46f56785", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### `POST` 请求：把要求装进信封\n", "- **用途**: 向服务器**提交（Post）**数据，比如发一篇帖子、翻译一段长文。\n", "- **特点**: 参数被放在一个独立的、看不见的“信封”（请求体`body`）里发送。这样做更安全，而且可以传输更大量、更复杂的数据。\n", "- **应用**: 我们今天要学习的翻译API和文字转语音API，因为要发送大段文本，所以都使用`POST`方法。\n", "\n", "**记住：`GET`用于“拿”，`POST`用于“送”。**"]}, {"cell_type": "markdown", "id": "fe0659fb", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 实践一：免费词典API (`GET`请求)\n", "\n", "我们将使用一个免费的词典API来查询英语单词的定义、发音和例句。这个API非常简单，直接在URL里告诉它你要查什么单词即可。"]}, {"cell_type": "markdown", "id": "3cf37dbd", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 1. 发送API请求\n", "我们来查询单词 \"apple\" 的信息。"]}, {"cell_type": "code", "execution_count": 48, "id": "ee3528f1", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在查询URL: https://api.dictionaryapi.dev/api/v2/entries/en/apple\n", "成功获取单词 'apple' 的数据！\n"]}], "source": ["import requests\n", "import json # 导入json库，方便我们后面美化打印\n", "\n", "word = \"apple\"\n", "# f-string让我们能方便地把变量word嵌入到URL中\n", "url = f\"https://api.dictionaryapi.dev/api/v2/entries/en/{word}\"\n", "\n", "print(f\"正在查询URL: {url}\")\n", "response = requests.get(url)\n", "\n", "if response.status_code == 200:\n", "    # 将响应的JSON数据存入变量data，供后续使用\n", "    data = response.json()\n", "    print(f\"成功获取单词 '{word}' 的数据！\")\n", "else:\n", "    print(f\"错误: {response.status_code}, 内容: {response.text}\")"]}, {"cell_type": "markdown", "id": "c06ebf2a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "import requests\n", "import json\n", "\n", "word = \"apple\"\n", "url = f\"https://api.dictionaryapi.dev/api/v2/entries/en/{word}\"\n", "response = requests.get(url)\n", "\n", "if response.status_code == 200:\n", "    data = response.json()\n", "    print(\"成功获取单词 'apple' 的数据！\")\n", "else:\n", "    print(f\"错误: {response.status_code}\")\n", "```"]}, {"cell_type": "markdown", "id": "3c308397", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 2. 解析复杂的JSON数据：像剥洋葱一样\n", "这个API返回的数据结构比我们之前见过的要复杂。它就像一个“洋葱”或者“俄罗斯套娃”，我们需要一层一层地剥开，才能找到想要的信息。"]}, {"cell_type": "markdown", "id": "d14af3cc", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 步骤A：先看看“洋葱”长什么样\n", "\n", "在解析之前，我们先用`json.dumps`把它**格式化打印**出来，看看它的结构。`indent=2`能让它显示得更漂亮。\n", "\n", "由于JSON数据很长，我们编写一个分页显示函数，每次显示一部分内容。"]}, {"cell_type": "code", "execution_count": 49, "id": "902d1d4d-fd78-4395-bf3e-10870932822a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== JSON数据结构 (第1页) ===\n", "[\n", "  {\n", "    \"word\": \"apple\",\n", "    \"phonetic\": \"/ˈæp.əl/\",\n", "    \"phonetics\": [\n", "      {\n", "        \"text\": \"/ˈæp.əl/\",\n", "        \"audio\": \"https://api.dictionaryapi.dev/media/pronunciations/en/apple-uk.mp3\",\n", "        \"sourceUrl\": \"https://commons.wikimedia.org/w/index.php?curid=9014262\",\n", "        \"license\": {\n", "          \"name\": \"BY 3.0 US\",\n", "          \"url\": \"https://creativecommons.org/licenses/by/3.0/us\"\n", "        }\n", "      },\n", "      {\n", "        \"text\": \"/ˈæp.əl/\",\n", "        \"audio\": \"https://api.dictionaryapi.dev/media/pronunciations/en/apple-us.mp3\",\n", "        \"sourceUrl\": \"https://commons.wikimedia.org/w/index.php?curid=718877\",\n", "        \"license\": {\n", "          \"name\": \"BY-SA 3.0\",\n", "\n", "... (还有 89 行)\n", "\n", "观察：最外层是一个[](列表)，里面有一个巨大的{}(字典)\n"]}], "source": ["def display_json_paginated(data, lines_per_page=20, curr_page=1):\n", "    \"\"\"分页显示JSON数据\"\"\"\n", "    if response.status_code == 200:\n", "        pretty_json_str = json.dumps(data, indent=2, ensure_ascii=False)\n", "        lines = pretty_json_str.split('\\n')\n", "        \n", "        # 显示第一页\n", "        print(f\"=== JSON数据结构 (第{curr_page}页) ===\")\n", "        for line in lines[(curr_page - 1) * lines_per_page: curr_page * lines_per_page]:\n", "            print(line)\n", "        \n", "        if len(lines) > lines_per_page:\n", "            print(f\"\\n... (还有 {(len(lines) - lines_per_page * curr_page) if (len(lines) - lines_per_page * curr_page) > 0 else 0} 行)\")\n", "        \n", "        print(\"\\n观察：最外层是一个[](列表)，里面有一个巨大的{}(字典)\")\n", "\n", "# 调用分页显示函数\n", "if response.status_code == 200:\n", "    display_json_paginated(data)"]}, {"cell_type": "code", "execution_count": 50, "id": "ba32823f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== JSON数据结构 (第2页) ===\n", "          \"url\": \"https://creativecommons.org/licenses/by-sa/3.0\"\n", "        }\n", "      }\n", "    ],\n", "    \"meanings\": [\n", "      {\n", "        \"partOfSpeech\": \"noun\",\n", "        \"definitions\": [\n", "          {\n", "            \"definition\": \"A common, round fruit produced by the tree Malus domestica, cultivated in temperate climates.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"Any of various tree-borne fruits or vegetables especially considered as resembling an apple; also (with qualifying words) used to form the names of other specific fruits such as custard apple, rose apple, thorn apple etc.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"The fruit of the Tree of Knowledge, eaten by <PERSON> and <PERSON> according to post-Biblical Christian tradition; the forbidden fruit.\",\n", "\n", "... (还有 69 行)\n", "\n", "观察：最外层是一个[](列表)，里面有一个巨大的{}(字典)\n"]}], "source": ["display_json_paginated(data, curr_page=2)"]}, {"cell_type": "code", "execution_count": 51, "id": "2d03af75", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== JSON数据结构 (第3页) ===\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"A tree of the genus Malus, especially one cultivated for its edible fruit; the apple tree.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"The wood of the apple tree.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"(in the plural) Short for apples and pears, slang for stairs.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"The ball in baseball.\",\n", "\n", "... (还有 49 行)\n", "\n", "观察：最外层是一个[](列表)，里面有一个巨大的{}(字典)\n"]}], "source": ["display_json_paginated(data, curr_page=3)"]}, {"cell_type": "code", "execution_count": 52, "id": "7d0513f5", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== JSON数据结构 (第4页) ===\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"When smiling, the round, fleshy part of the cheeks between the eyes and the corners of the mouth.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"A Native American or red-skinned person who acts and/or thinks like a white (Caucasian) person.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"(ice hockey slang) An assist.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          }\n", "        ],\n", "        \"synonyms\": [],\n", "\n", "... (还有 29 行)\n", "\n", "观察：最外层是一个[](列表)，里面有一个巨大的{}(字典)\n"]}], "source": ["display_json_paginated(data, curr_page=4)"]}, {"cell_type": "code", "execution_count": 53, "id": "96da702f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== JSON数据结构 (第5页) ===\n", "        \"antonyms\": []\n", "      },\n", "      {\n", "        \"partOfSpeech\": \"verb\",\n", "        \"definitions\": [\n", "          {\n", "            \"definition\": \"To become apple-like.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          },\n", "          {\n", "            \"definition\": \"To form buds.\",\n", "            \"synonyms\": [],\n", "            \"antonyms\": []\n", "          }\n", "        ],\n", "        \"synonyms\": [],\n", "        \"antonyms\": []\n", "      }\n", "    ],\n", "\n", "... (还有 9 行)\n", "\n", "观察：最外层是一个[](列表)，里面有一个巨大的{}(字典)\n"]}], "source": ["display_json_paginated(data, curr_page=5)"]}, {"cell_type": "code", "execution_count": 54, "id": "b946b6bd", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== JSON数据结构 (第6页) ===\n", "    \"license\": {\n", "      \"name\": \"CC BY-SA 3.0\",\n", "      \"url\": \"https://creativecommons.org/licenses/by-sa/3.0\"\n", "    },\n", "    \"sourceUrls\": [\n", "      \"https://en.wiktionary.org/wiki/apple\"\n", "    ]\n", "  }\n", "]\n", "\n", "... (还有 0 行)\n", "\n", "观察：最外层是一个[](列表)，里面有一个巨大的{}(字典)\n"]}], "source": ["display_json_paginated(data, curr_page=6)"]}, {"cell_type": "markdown", "id": "b4c11e60", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 步骤B：编写解析函数，层层深入\n", "\n", "现在我们知道了结构，就可以开始编写解析函数了。这个JSON有**4层嵌套结构**：\n", "\n", "**第1层 - 外层列表 `[]`**: API返回的是一个列表，里面包含查询结果\n", "- 访问方式：`data[0]` 获取第一个（通常也是唯一的）查询结果\n", "\n", "**第2层 - 单词信息字典 `{}`**: 包含单词的所有信息\n", "- 访问方式：`data[0]['word']` 获取单词本身，`data[0]['meanings']` 获取释义列表\n", "\n", "**第3层 - 词性列表 `[]`**: `meanings`是一个列表，每个元素代表一种词性\n", "- 访问方式：遍历 `data[0]['meanings']`，每个元素包含 `partOfSpeech` 和 `definitions`\n", "\n", "**第4层 - 定义列表 `[]`**: 每种词性下的 `definitions` 又是一个列表\n", "- 访问方式：遍历 `meaning['definitions']`，每个元素包含具体的 `definition` 文本"]}, {"cell_type": "code", "execution_count": 55, "id": "05a08163", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["单词: apple\n", "\n", "词性: noun\n", "  1. A common, round fruit produced by the tree Malus domestica, cultivated in temperate climates.\n", "  2. Any of various tree-borne fruits or vegetables especially considered as resembling an apple; also (with qualifying words) used to form the names of other specific fruits such as custard apple, rose apple, thorn apple etc.\n", "  3. The fruit of the Tree of Knowledge, eaten by <PERSON> and <PERSON> according to post-Biblical Christian tradition; the forbidden fruit.\n", "  4. A tree of the genus Malus, especially one cultivated for its edible fruit; the apple tree.\n", "  5. The wood of the apple tree.\n", "  6. (in the plural) Short for apples and pears, slang for stairs.\n", "  7. The ball in baseball.\n", "  8. When smiling, the round, fleshy part of the cheeks between the eyes and the corners of the mouth.\n", "  9. A Native American or red-skinned person who acts and/or thinks like a white (Caucasian) person.\n", "  10. (ice hockey slang) An assist.\n", "\n", "词性: verb\n", "  1. To become apple-like.\n", "  2. To form buds.\n"]}], "source": ["def print_word_info(word_data):\n", "    try:\n", "        # 步骤1: 获取列表中的第一个元素（它是一个字典）\n", "        first_result = word_data[0]\n", "        print(f\"单词: {first_result.get('word', 'N/A')}\")\n", "        \n", "        # 步骤2: 检查并获取 'meanings' 键，它的值是一个列表\n", "        if 'meanings' in first_result:\n", "            # 步骤3: 遍历 meanings 列表，每个元素是一种词性的释义\n", "            for meaning in first_result['meanings']:\n", "                part_of_speech = meaning.get('partOfSpeech', 'N/A')\n", "                print(f\"\\n词性: {part_of_speech}\")\n", "                # 步骤4: 遍历 definitions 列表，获取每一条定义\n", "                for i, definition in enumerate(meaning.get('definitions', [])):\n", "                    print(f\"  {i+1}. {definition.get('definition', 'N/A')}\")\n", "                    # 挑战：你能否尝试打印出例句(example)呢？\n", "    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r, KeyError) as e:\n", "        # 专业的错误处理，能告诉我们解析失败的原因\n", "        print(f\"解析数据时出错: {e}，请检查返回的数据结构。\")\n", "\n", "# 调用函数来解析并打印之前获取的'apple'单词信息\n", "if response.status_code == 200:\n", "    print_word_info(data)\n", "else:\n", "    print(\"没有可供解析的数据。\")"]}, {"cell_type": "markdown", "id": "c87bdb5e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (函数定义):**\n", "\n", "```python\n", "def print_word_info(word_data):\n", "    try:\n", "        # 第1层：进入列表，获取第一个元素（一个大字典）\n", "        first_result = word_data[0]\n", "        print(f\"单词: {first_result['word']}\")\n", "\n", "        # 第2层：从大字典中找到 'meanings'，它是一个列表\n", "        if 'meanings' in first_result:\n", "            # 第3层：遍历 'meanings' 列表\n", "            for meaning in first_result['meanings']:\n", "                print(f\"\\n词性: {meaning['partOfSpeech']}\")\n", "```"]}, {"cell_type": "markdown", "id": "f583542d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (循环解析):**\n", "```python\n", "                # 第4层：在每个词性中，遍历 'definitions' 列表\n", "                for i, definition in enumerate(meaning['definitions']):\n", "                    print(f\"  {i+1}. {definition['definition']}\")\n", "    except Exception as e:\n", "        print(f\"解析出错: {e}\")\n", "\n", "# 调用函数来解析\n", "print_word_info(data)\n", "```"]}, {"cell_type": "markdown", "id": "5c2732fb", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 实践二：小牛翻译API (`POST`请求)\n", "小牛翻译提供免费的文本翻译API服务。这是一个更“专业”的API，它需要我们：\n", "1.  注册获取`API Key`（就像一把开门的钥匙）。\n", "2.  使用`POST`方法发送我们的请求。\n", "3.  对我们的请求进行“签名”，证明我们是合法用户。\n", "\n", "**API文档地址**: [https://niutrans.com/documents/contents/transapi_text_v2](https://niutrans.com/documents/contents/transapi_text_v2)"]}, {"cell_type": "markdown", "id": "2ee2dcd9", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 封装翻译函数与“签名”解密\n", "\n", "**什么是签名？** 为了安全，很多API要求我们把“请求内容”和“我们的密钥”组合起来，用一种叫做`MD5`的算法算出一个独一无二的“指纹”（签名`sign`）。服务器收到后，会用同样的方法计算一次，如果两个“指纹”对得上，就说明请求是可信的。\n", "\n", "**注意：** 找老师要到`apikey`，然后在代码中替换占位符。"]}, {"cell_type": "code", "execution_count": 56, "id": "46287a71", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'apple' 的翻译是: 苹果\n"]}], "source": ["import time\n", "import hashlib\n", "\n", "def translate_text(text, from_lang=\"en\", to_lang=\"zh\"):\n", "    # --- 你的API信息，需要到niutrans.com注册后替换 ---\n", "    APP_ID = \"WML1752072320905\" # 请替换成你自己的App ID\n", "    APP_KEY = \"571ed8d31b353b8069d40fb1ab96cee7\" # 请替换成你自己的App Key\n", "\n", "    # 检查用户是否已替换自己的密钥\n", "    if APP_ID.startswith(\"你的\") or APP_KEY.startswith(\"你的\"):\n", "        return \"[演示模式] 请先在代码中替换你的APP_ID和APP_KEY。\"\n", "    \n", "    # --- 根据小牛翻译文档V2版本编写 --- \n", "    url = \"https://api.niutrans.com/v2/text/translate\"\n", "    # salt 是一个随机数，这里用当前时间戳来保证每次请求都不同\n", "    salt = str(int(time.time()))\n", "    # 按照API文档要求，拼接成待签名的原始字符串\n", "    sign_raw = f\"apikey={APP_KEY}&appId={APP_ID}&from={from_lang}&srcText={text}&timestamp={salt}&to={to_lang}\"\n", "    # 使用md5算法计算签名\n", "    sign = hashlib.md5(sign_raw.encode('utf-8')).hexdigest()\n", "    \n", "    # 将所有参数打包成字典，作为POST请求的`data`\n", "    params = {\n", "        \"from\": from_lang, \"to\": to_lang,\n", "        \"appId\": APP_ID, \"timestamp\": salt,\n", "        \"authStr\": sign, \"srcText\": text\n", "    }\n", "    \n", "    try:\n", "        # 发送POST请求，参数放在data里\n", "        response = requests.post(url, data=params)\n", "        if response.status_code == 200:\n", "            result = response.json()\n", "            # 根据文档，成功时返回tgtText，失败时返回error_code\n", "            return result.get('tgtText', f\"翻译结果解析失败: {result}\")\n", "        return f\"翻译API请求失败, 状态码: {response.status_code}, 内容: {response.text}\"\n", "    except Exception as e:\n", "        return f\"调用翻译API时发生异常: {e}\"\n", "\n", "# 测试函数\n", "translated_word = translate_text(\"apple\")\n", "print(f\"'apple' 的翻译是: {translated_word}\")"]}, {"cell_type": "markdown", "id": "6d03fbf5", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (签名部分):**\n", "\n", "```python\n", "import time\n", "import hashlib\n", "\n", "def translate_text(text, from_lang=\"en\", to_lang=\"zh\"):\n", "    APP_ID = \"APP_ID\"     # 请替换\n", "    APP_KEY = \"APP_KEY\" # 请替换\n", "\n", "    # from_lang = \"en\" # 源语言\n", "    # to_lang = \"zh\" # 目标语言\n", "    # text = \"apple\" # 要翻译的文本\n", "\n", "    timestamp = str(int(time.time())) # 1. 准备当前时间戳\n", "\n", "    # 2. 按照API文档要求，拼接成待签名的原始字符串\n", "    sign_raw = \"apikey={}&appId={}&from={}&srcText={}&timestamp={}&to={}\".format(APP_KEY, APP_ID, from_lang, text, timestamp, to_lang)\n", "\n", "    # 3. 使用md5算法计算签名\n", "    sign = hashlib.md5(sign_raw.encode('utf-8')).hexdigest()\n", "```"]}, {"cell_type": "markdown", "id": "fc014a58", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (POST请求部分):**\n", "```python\n", "    # 4. 将所有参数打包成字典，作为“信封”里的内容\n", "    params = {\n", "        \"from\": from_lang, \"to\": to_lang,\n", "        \"appId\": APP_ID, \"timestamp\": timestamp,\n", "        \"authStr\": sign, \"srcText\": text\n", "    }\n", "\n", "    # 5. 发送POST请求，注意是requests.post\n", "    # `data=params`表示把params字典放进“信封”里发送\n", "    response = requests.post(url, data=params)\n", "    if response.status_code == 200:\n", "        result = response.json()\n", "        # 根据文档，成功时返回tgtText，失败时返回error_code\n", "        print(result.get('tgtText', f\"翻译结果解析失败: {result}\"))\n", "    else:\n", "        print(f\"翻译API请求失败, 状态码: {response.status_code}, 内容: {response.text}\")\n", "    return result.get('tgtText', f\"翻译结果解析失败: {result}\")\n", "```"]}, {"cell_type": "markdown", "id": "eca620a9", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 实践三：文字转语音 (TTS) API\n", "\n", "现在，我们将调用一个能将文本转换为语音的API。这个API更特别，它接收文本，返回的不是JSON，而是一个**WAV音频文件**的**二进制数据**。"]}, {"cell_type": "markdown", "id": "7ff29b8e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 处理音频流\n", "我们不能用`.json()`或`.text`来处理音频。我们需要用 **`response.content`**，它能获取到最原始的、未经处理的二进制数据，也就是音频文件的本身。\n", "\n", "**处理流程：**\n", "1.  向API发送 `POST` 请求，请求体里包含我们要朗读的文字。\n", "2.  获取响应中的原始二进制数据 (`response.content`)。\n", "3.  将这些二进制数据写入一个临时的`.wav`文件中。\n", "4.  使用`playsound`库来播放这个临时文件。\n", "5.  播放完毕后，删除临时文件，保持整洁。"]}, {"cell_type": "code", "execution_count": 57, "id": "80fe5155", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准备向TTS API发送文本: '你好，世界！这是一个测试音频。'\n", "音频已保存到临时文件，准备播放...\n", "播放完毕。\n"]}], "source": ["import tempfile\n", "import os\n", "# 我们需要安装 playsound 库: pip install playsound==1.2.2 (1.2.2版本比较稳定)\n", "from playsound import playsound\n", "import requests\n", "\n", "# --- 配置老师提供的API信息 ---\n", "# !!重要!! 请将这里的IP地址替换成老师提供的实际IP地址\n", "API_IP_ADDRESS = \"127.0.0.1\" # 这是一个环回地址，表示本机，用于测试\n", "API_PORT = 5000\n", "API_ENDPOINT = \"/synthesize\"\n", "API_URL = f\"http://{API_IP_ADDRESS}:{API_PORT}{API_ENDPOINT}\"\n", "\n", "def fetch_and_play_mp3(text_to_send):\n", "    print(f\"准备向TTS API发送文本: '{text_to_send}'\")\n", "    # 步骤1: 准备要POST的数据\n", "    payload = {\"text\": text_to_send}\n", "    tmp_file_path = None\n", "    try:\n", "        # 发送POST请求，使用json=payload，requests会自动处理好请求头\n", "        response = requests.post(API_URL, json=payload, timeout=30)\n", "        response.raise_for_status() # 检查请求是否成功\n", "        \n", "        # 检查返回的是否是音频文件\n", "        if 'audio/wav' in response.headers.get('Content-Type', '').lower():\n", "            # 步骤2: 使用.content获取二进制数据\n", "            mp3_data = response.content \n", "            # 步骤3: 创建一个带.wav后缀的临时文件来保存音频\n", "            with open(\"tts.wav\", \"wb\") as tmp_file:\n", "                tmp_file.write(mp3_data)\n", "                tmp_file_path = tmp_file.name\n", "            \n", "            print(f\"音频已保存到临时文件，准备播放...\")\n", "            # 步骤4: 播放这个临时文件\n", "            playsound(tmp_file_path)\n", "            print(\"播放完毕。\")\n", "        else:\n", "            print(f\"API未返回有效的文件。\")\n", "\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"API请求失败: {e}。请确认老师的FastAPI服务正在运行，且IP地址配置正确。\")\n", "    except Exception as e:\n", "        print(f\"播放或文件操作时发生错误: {e}\")\n", "    finally:\n", "        # 步骤5: 播放完毕后，删除临时文件，避免占用空间\n", "        if tmp_file_path and os.path.exists(tmp_file_path):\n", "            os.remove(tmp_file_path)\n", "\n", "# 测试TTS函数\n", "fetch_and_play_mp3(\"你好，世界！这是一个测试音频。\")"]}, {"cell_type": "markdown", "id": "a225cee7", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (核心逻辑):**\n", "```python\n", "import tempfile, os\n", "from playsound import playsound\n", "\n", "API_URL = \"http://老师的IP:5000/synthesize\"\n", "\n", "def fetch_and_play_wav(text):\n", "    payload = {\"text\": text}\n", "    # 发送POST请求，使用json=payload，requests会自动处理好请求头\n", "    response = requests.post(API_URL, json=payload, timeout=30)\n", "    response.raise_for_status() # 检查请求是否成功\n", "    \n", "    # 检查返回的是否是音频文件\n", "    if 'audio/wav' in response.headers.get('Content-Type', '').lower():\n", "        # 步骤2: 使用.content获取二进制数据\n", "        wav_data = response.content \n", "        # 步骤3: 创建一个带.wav后缀的临时文件来保存音频\n", "        with open(\"tts.wav\", \"wb\") as tmp_file:\n", "            tmp_file.write(wav_data)\n", "            tmp_file_path = tmp_file.name\n", "        \n", "        print(f\"音频已保存到临时文件，准备播放...\")\n", "        # 步骤4: 播放这个临时文件\n", "        playsound(tmp_file_path)\n", "        print(\"播放完毕。\")\n", "    else:\n", "        print(f\"API未返回有效的文件。\")\n", "```\n", "**你需要先安装 `playsound` 库: `pip install playsound==1.2.2`**"]}, {"cell_type": "markdown", "id": "e706f4f4", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 综合练习：智能药品说明解读器\n", "\n", "**场景**: 我们买了一款进口药，说明书是西班牙语，希望能将关键信息翻译并朗读出来。现在，我们要把今天学的所有技能整合起来！"]}, {"cell_type": "markdown", "id": "ba90c3d0", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**西班牙语说明文字:**\n", "```\n", "Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido.\n", "```\n", "**任务步骤:**\n", "1.  调用**翻译API**，将上述字符串从西班牙语(`es`)翻译成中文(`zh`)。\n", "2.  从翻译后的文本中，用简单的字符串方法**提取**出“成分”和“推荐日剂量”等关键信息。\n", "3.  将提取的关键信息拼接成一个摘要字符串。\n", "4.  调用**TTS API**，将这段最重要的信息摘要**朗读**出来。"]}, {"cell_type": "code", "execution_count": 58, "id": "96e27258", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始文本: Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido.\n", "\n", "翻译结果: 维生素C与生物类黄酮和玫瑰果。每片成分：维生素C（抗坏血酸）1000毫克。使用方法：成人每天服用一（1）片。每日推荐剂量：1片。\n", "\n", "提取的关键信息: 每片成分：维生素C（抗坏血酸）1000毫克。每日推荐剂量：1片\n", "准备向TTS API发送文本: '每片成分：维生素C（抗坏血酸）1000毫克。每日推荐剂量：1片'\n", "音频已保存到临时文件，准备播放...\n", "播放完毕。\n"]}], "source": ["# 1. 待处理的西班牙语文本\n", "spanish_text = \"Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido.\"\n", "print(f\"原始文本: {spanish_text}\")\n", "\n", "# 2. 调用翻译函数 (需要你已正确配置APP_ID和KEY)\n", "chinese_text = translate_text(spanish_text, from_lang=\"es\", to_lang=\"zh\")\n", "print(f\"\\n翻译结果: {chinese_text}\")\n", "\n", "# 3. 提取关键信息 (这里用一个简单的方法)\n", "key_info_to_speak = \"\"\n", "# 确保翻译成功了再进行提取\n", "# isinstance(chinese_text, str): 检查返回的是字符串而不是其他类型\n", "# \"[演示模式]\" not in chinese_text: 排除演示模式的提示信息\n", "# \"失败\" not in chinese_text: 排除包含失败信息的错误返回\n", "if isinstance(chinese_text, str) and \"[演示模式]\" not in chinese_text and \"失败\" not in chinese_text:\n", "    key_info_list = []\n", "    # 统一用句号分割句子，避免中英文标点混用问题\n", "    # 先把中文句号替换成英文句号，再统一分割\n", "    for sentence in chinese_text.replace('。', '.').split('.'): \n", "        # 找到包含关键词的句子\n", "        if '成分' in sentence or '剂量' in sentence:\n", "            key_info_list.append(sentence.strip())\n", "    # 将找到的关键句子用“。”拼接起来，听起来更自然\n", "    key_info_to_speak = \"。\".join(key_info_list)\n", "    print(f\"\\n提取的关键信息: {key_info_to_speak}\")\n", "else:\n", "    print(\"\\n因翻译失败，无法提取关键信息。\")\n", "\n", "# 4. 调用TTS朗读提取出的关键信息\n", "if key_info_to_speak:\n", "    fetch_and_play_mp3(key_info_to_speak)\n", "else:\n", "    print(\"\\n没有提取到可朗读的关键信息。\")"]}, {"cell_type": "markdown", "id": "d2d1377c", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 1. 待处理的西班牙语文本\n", "spanish_text = \"Vitamina C con Bioflavonoides y Escaramujo. Ingredientes por comprimido: Vitamina C (ácido ascórbico) 1000mg. Modo de empleo: Para adultos, tomar un (1) comprimido al día. Dosis diaria recomendada: 1 comprimido.\"\n", "print(f\"原始文本: {spanish_text}\")\n", "\n", "# 2. 调用翻译函数 (需要你已正确配置APP_ID和KEY)\n", "chinese_text = translate_text(spanish_text, from_lang=\"es\", to_lang=\"zh\")\n", "print(f\"\\n翻译结果: {chinese_text}\")\n", "\n", "# 3. 提取关键信息 (这里用一个简单的方法)\n", "key_info_to_speak = \"\"\n", "# 确保翻译成功了再进行提取\n", "if isinstance(chinese_text, str) and \"[演示模式]\" not in chinese_text and \"失败\" not in chinese_text:\n", "    key_info_list = []\n", "    # 统一用.分割句子，更通用\n", "    for sentence in chinese_text.replace('。', '.').split('.'): \n", "        # 找到包含关键词的句子\n", "        if '成分' in sentence or '剂量' in sentence:\n", "            key_info_list.append(sentence.strip())\n", "    # 将找到的关键句子用“。”拼接起来，听起来更自然\n", "    key_info_to_speak = \"。\".join(key_info_list)\n", "    print(f\"\\n提取的关键信息: {key_info_to_speak}\")\n", "else:\n", "    print(\"\\n因翻译失败，无法提取关键信息。\")\n", "```"]}, {"cell_type": "markdown", "id": "5fdf9007", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 4. 调用TTS朗读提取出的关键信息\n", "if key_info_to_speak:\n", "    fetch_and_play_mp3(key_info_to_speak)\n", "else:\n", "    print(\"\\n没有提取到可朗读的关键信息。\")\n", "```"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}