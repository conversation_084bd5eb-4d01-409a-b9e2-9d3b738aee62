# L10_Final.py - 打字速度记录系统完整实现
# 网络与后端开发集成项目

from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy import Column, Integer, Float, DateTime, create_engine, desc
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func
from pydantic import BaseModel
from datetime import datetime
from typing import List

# ==================== 数据库配置 ====================
SQLALCHEMY_DATABASE_URL = "sqlite:///./typing_records.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# ==================== 数据库模型 ====================
class TypingRecord(Base):
    """打字记录数据库模型"""
    __tablename__ = "records"

    id = Column(Integer, primary_key=True, index=True)
    wpm = Column(Integer, nullable=False)  # 每分钟字数
    accuracy = Column(Float, nullable=False)  # 正确率
    # server_default=func.now()让数据库在创建记录时自动填写当前时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# ==================== Pydantic 数据格式 ====================
class RecordCreate(BaseModel):
    """创建记录时的输入数据格式"""
    wpm: int
    accuracy: float

class RecordResponse(BaseModel):
    """API响应时的数据格式"""
    id: int
    wpm: int
    accuracy: float
    created_at: datetime

    class Config:
        orm_mode = True  # 允许模型从ORM对象直接转换

# ==================== CRUD 数据库操作 ====================
def create_record(db: Session, record: RecordCreate):
    """创建新的打字记录"""
    db_record = TypingRecord(**record.model_dump())
    db.add(db_record)
    db.commit()
    db.refresh(db_record)
    return db_record

def get_records(db: Session, skip: int = 0, limit: int = 100):
    """获取所有打字记录"""
    return db.query(TypingRecord).offset(skip).limit(limit).all()

def get_best_record(db: Session):
    """获取最佳打字记录（按WPM排序）"""
    return db.query(TypingRecord).order_by(desc(TypingRecord.wpm)).first()

# ==================== FastAPI 应用 ====================
# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="打字速度记录系统 API",
    description="一个记录和跟踪打字练习成绩的API系统",
    version="1.0.0"
)

# 依赖项函数：获取数据库会话
def get_db():
    """数据库会话依赖项"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ==================== API 路由 ====================
@app.get("/", tags=["首页"])
def read_root():
    """API首页"""
    return {
        "message": "欢迎使用打字速度记录系统 API",
        "version": "1.0.0",
        "endpoints": {
            "创建记录": "POST /records/",
            "查看所有记录": "GET /records/",
            "查看最佳记录": "GET /records/best/",
            "API文档": "/docs"
        }
    }

@app.post("/records/", response_model=RecordResponse, tags=["打字记录"])
def create_new_record(record: RecordCreate, db: Session = Depends(get_db)):
    """创建新的打字记录"""
    return create_record(db=db, record=record)

@app.get("/records/", response_model=List[RecordResponse], tags=["打字记录"])
def read_all_records(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取所有打字记录"""
    return get_records(db, skip=skip, limit=limit)

@app.get("/records/best/", response_model=RecordResponse, tags=["打字记录"])
def read_best_record(db: Session = Depends(get_db)):
    """获取最佳打字记录"""
    best_record = get_best_record(db)
    if best_record is None:
        raise HTTPException(status_code=404, detail="数据库中没有任何记录")
    return best_record

@app.get("/records/stats/", tags=["统计信息"])
def get_statistics(db: Session = Depends(get_db)):
    """获取打字记录统计信息"""
    records = get_records(db)
    if not records:
        return {"message": "暂无记录"}
    
    wpm_values = [record.wpm for record in records]
    accuracy_values = [record.accuracy for record in records]
    
    return {
        "总记录数": len(records),
        "平均WPM": round(sum(wpm_values) / len(wpm_values), 2),
        "最高WPM": max(wpm_values),
        "最低WPM": min(wpm_values),
        "平均正确率": round(sum(accuracy_values) / len(accuracy_values), 4),
        "最高正确率": max(accuracy_values),
        "最低正确率": min(accuracy_values)
    }

# ==================== 运行说明 ====================
if __name__ == "__main__":
    import uvicorn
    print("启动打字速度记录系统...")
    print("API文档地址: http://127.0.0.1:8000/docs")
    print("API首页: http://127.0.0.1:8000/")
    uvicorn.run(app, host="127.0.0.1", port=8000, reload=True)
