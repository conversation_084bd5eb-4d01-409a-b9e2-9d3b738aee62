{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Python群聊服务器：从健壮到高效，探索异步编程"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["上节课，我们成功用多线程技术实现了一个支持多人在线的群聊服务器。今天，我们将深入探索如何让它变得更专业、更稳定、更高效！\n", "\n", "我们将首先修复多线程并发下的一个“隐藏BUG”，然后探索一种全新的、更高效的并发模型——**异步编程(asyncio)**，让我们的服务器能够轻松应对更多的用户连接。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第一步：修复多线程的“隐藏BUG”——竞态条件"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 思想实验：当混乱发生时\n", "回顾我们的多线程群聊模型，有一个关键的共享资源：`clients`列表。所有的子线程（服务员）都会访问和修改它。\n", "\n", "**思考一个场景**：假设我们的服务器非常火爆，**同一时刻**发生了两件事：\n", "- **线程A**: 正在执行`clients.append(小明)`，准备让小明加入群聊。\n", "- **线程B**: 正在执行`clients.remove(小红)`，因为小红恰好退出了。\n", "\n", "这就像你和你的同桌，**同时在笔记本的同一行写字**。你写“今天”，他写“晴天”，最后纸上会变成什么？很可能是一团谁也看不懂的乱麻！\n", "\n", "这个计算机科学中的经典问题，叫做**竞态条件 (Race Condition)**。当多个线程同时访问和修改共享数据时，由于操作顺序无法预测，可能会导致程序崩溃、数据错乱或连接丢失。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 解决方案：引入“锁” (Lock)\n", "如何解决混乱？我们需要定一个规矩。\n", "\n", "**“发言棒”的比喻**: 为了避免在笔记本上乱写，我们定个规矩：谁想写字，必须先拿到桌上**唯一**的“发言棒”。只有拿到棒的人才能写，写完必须把棒子放回去。这样，任何时候都只有一个人在写字，秩序就得到了保证。\n", "\n", "这个“发言棒”，就是**线程锁 (threading.Lock)**。它能确保在同一时间，只有一个线程能操作被保护的数据。\n", "\n", "**看代码，体验锁：**\n", "```python\n", "import threading\n", "# 创建一个锁对象\n", "clients_lock = threading.Lock()\n", "\n", "# 在所有操作clients列表的地方，都使用with语句来加锁\n", "def add_client(client_socket):\n", "    with clients_lock: # 相当于“拿起发言棒”\n", "        clients.append(client_socket)\n", "    # with语句块结束时，会自动“放回发言棒”\n", "\n", "def remove_client(client_socket):\n", "    with clients_lock: # 加锁保护\n", "        clients.remove(client_socket)\n", "```\n", "使用`with`语句是最佳实践，它像一个尽职的会议主持人，自动帮你“加锁”和“解锁”，即使中间出错也不会忘记，非常安全。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第二步：重新审视我们的模型——同步阻塞的瓶颈"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 设计一个场景：服务器的心跳检测\n", "想象一下，为了确保客户端都还活着，我们的服务器需要**每隔5秒**就给每个客户端发送一个“你还在吗？”的心跳包，并**等待**客户端回复“我还在！”。\n", "\n", "**问题来了**：如果一个客户端（比如小A）的网络很卡，或者他的程序崩溃了，导致他迟迟不回复心跳。那么，负责服务小A的那个**服务器线程**会怎么样？\n", "\n", "答案是：这个线程会一直卡在`client_socket.recv()`那里，**被阻塞住**，死死地等待小A的回复。\n", "\n", "**更严重的问题**：如果服务器要管理1000个连接，我们就需要开1000个线程。如果其中有100个客户端都像小A一样卡住了，那么服务器上就会有100个线程被“冻结”，它们占着宝贵的内存和CPU资源，却什么也干不了。这就是**同步阻塞 (Synchronous Blocking)** 模型的巨大瓶颈。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 解决方案：一种更聪明的工作方式——异步非阻塞\n", "**打电话 vs 发微信**:\n", "- **同步阻塞 (打电话)**: 你拨通电话后，必须一直举着手机**等待**对方说话。在对方说话之前，你什么也做不了，你的时间被“阻塞”了。\n", "\n", "- **异步非阻塞 (发微信)**: 你把消息发出去后，就可以**立刻放下手机**，去做别的事情（看书、喝水）。当对方回复时，手机会“叮”一声**通知**你。你再拿起手机处理。\n", "\n", "**异步编程 (Asynchronous)** 的核心思想就是：**发起一个可能耗时很长的操作（比如网络I/O），但不要在原地等待结果，而是继续去做其他事情。当那个操作完成时，系统会通知你。**\n", "\n", "对于我们的服务器来说，这意味着**一个线程**就可以管理所有1000个客户端，因为它从不在任何一个客户端那里“死等”。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 体验同步与异步：做家务实验\n", "让我们用一个生活中的例子来直观感受效率差异。假设做饭需要三件事：烧水（5秒）、洗菜（3秒）、切菜（2秒）。\n", "\n", "**同步做法**：一件一件做，总耗时 `5 + 3 + 2 = 10` 秒。\n", "**异步做法**：按下烧水壶开关（发起任务），**不等它烧开**，立刻去洗菜；洗完菜，立刻去切菜。在切菜的过程中，水烧好了。总耗时取决于最耗时的任务，即 `Max(5, 3, 2) = 5` 秒！"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 同步模式 --- (一件一件做)\n", "[同步] 开始任务: 烧水 (需要 5 秒)\n", "[同步] 完成任务: 烧水\n", "[同步] 开始任务: 洗菜 (需要 3 秒)\n", "[同步] 完成任务: 洗菜\n", "[同步] 开始任务: 切菜 (需要 2 秒)\n", "[同步] 完成任务: 切菜\n", "同步总耗时: 10.01 秒\n", "\n", "--- 异步模式 --- (同时开始，不等待)\n", "[异步] 开始任务: 烧水 (需要 5 秒)\n", "[异步] 开始任务: 洗菜 (需要 3 秒)\n", "[异步] 开始任务: 切菜 (需要 2 秒)\n", "[异步] 完成任务: 切菜\n", "[异步] 完成任务: 洗菜\n", "[异步] 完成任务: 烧水\n", "异步总耗时: 5.00 秒\n"]}], "source": ["import time\n", "import asyncio\n", "\n", "# 同步版本\n", "def sync_task(name, duration):\n", "    print(f\"[同步] 开始任务: {name} (需要 {duration} 秒)\")\n", "    time.sleep(duration) # sleep会阻塞整个程序\n", "    print(f\"[同步] 完成任务: {name}\")\n", "\n", "def run_sync():\n", "    start_time = time.time()\n", "    print(\"--- 同步模式 --- (一件一件做)\")\n", "    sync_task(\"烧水\", 5)\n", "    sync_task(\"洗菜\", 3)\n", "    sync_task(\"切菜\", 2)\n", "    end_time = time.time()\n", "    print(f\"同步总耗时: {end_time - start_time:.2f} 秒\\n\")\n", "\n", "# 异步版本\n", "async def async_task(name, duration):\n", "    print(f\"[异步] 开始任务: {name} (需要 {duration} 秒)\")\n", "    # await asyncio.sleep() 会把控制权交出去，让CPU干别的\n", "    await asyncio.sleep(duration)\n", "    print(f\"[异步] 完成任务: {name}\")\n", "\n", "async def run_async():\n", "    start_time = time.time()\n", "    print(\"--- 异步模式 --- (同时开始，不等待)\")\n", "    # asyncio.gather 让多个异步任务并发执行\n", "    await asyncio.gather(\n", "        async_task(\"烧水\", 5),\n", "        async_task(\"洗菜\", 3),\n", "        async_task(\"切菜\", 2)\n", "    )\n", "    end_time = time.time()\n", "    print(f\"异步总耗时: {end_time - start_time:.2f} 秒\")\n", "\n", "# 运行实验\n", "run_sync()\n", "await run_async()"]}, {"cell_type": "markdown", "id": "3e0ff59b", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "import time\n", "import asyncio\n", "\n", "# 同步版本\n", "def sync_task(name, duration):\n", "    print(f\"[同步] 开始任务: {name} (需要 {duration} 秒)\")\n", "    time.sleep(duration) # sleep会阻塞整个程序\n", "    print(f\"[同步] 完成任务: {name}\")\n", "\n", "def run_sync():\n", "    start_time = time.time()\n", "    print(\"--- 同步模式 --- (一件一件做)\")\n", "    sync_task(\"烧水\", 5)\n", "    sync_task(\"洗菜\", 3)\n", "    sync_task(\"切菜\", 2)\n", "    end_time = time.time()\n", "    print(f\"同步总耗时: {end_time - start_time:.2f} 秒\\n\")\n", "```"]}, {"cell_type": "markdown", "id": "d5796719", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 异步版本\n", "async def async_task(name, duration):\n", "    print(f\"[异步] 开始任务: {name} (需要 {duration} 秒)\")\n", "    # await asyncio.sleep() 会把控制权交出去，让CPU干别的\n", "    await asyncio.sleep(duration)\n", "    print(f\"[异步] 完成任务: {name}\")\n", "\n", "async def run_async():\n", "    start_time = time.time()\n", "    print(\"--- 异步模式 --- (同时开始，不等待)\")\n", "    # asyncio.gather 让多个异步任务并发执行\n", "    await asyncio.gather(\n", "        async_task(\"烧水\", 5),\n", "        async_task(\"洗菜\", 3),\n", "        async_task(\"切菜\", 2)\n", "    )\n", "    end_time = time.time()\n", "    print(f\"异步总耗时: {end_time - start_time:.2f} 秒\")\n", "\n", "# 运行实验\n", "run_sync()\n", "await run_async()\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## `asyncio`的魔法：事件循环与`async/await`"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 核心组件：事件循环 (Event Loop)\n", "你可以把`asyncio`的**事件循环**想象成一个**精力无限的“总管家”**。\n", "他手里有个“待办事项”清单（所有客户端连接）。他会以极快的速度轮询清单上的每个任务：“客户端A，有消息吗？没有？下一个。”“客户端B，有消息吗？有！好，处理一下。下一个。”\n", "\n", "他从不在任何一个客户端那里“死等”，所以他**一个人（一个线程）**就能管理好所有客人。\n", "\n", "### 魔法咒语：`async`和`await`\n", "- **`async def`**: 这个咒语告诉总管家：“这是一个可以被中途暂停和恢复的任务。”（定义一个协程函数）\n", "- **`await`**: 在任务内部，这个咒语告诉总管家：“**我要在这里等一下**（比如等网络消息），你先去忙别的吧，我好了会通知你。”（挂起当前任务，让事件循环去执行其他任务）"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 异步群聊服务器代码 (核心部分)\n", "我们来看看异步版本的服务器与多线程版本有什么本质不同。\n", "\n", "**多线程版 (一个线程服务一个客户端):**\n", "```python\n", "def handle_client(client_socket, ...):\n", "    # client_socket.recv() -> 线程被阻塞，无法处理其他客户端\n", "    message = client_socket.recv(1024)\n", "```\n", "\n", "**asyncio版 (一个线程服务所有客户端):**\n", "```python\n", "async def handle_client(reader, writer):\n", "    # await reader.read() -> 任务被挂起，事件循环继续工作，处理其他客户端\n", "    data = await reader.read(1024)\n", "```\n", "\n", "异步版本中的`await`是关键！它表示：“这个`read`操作可能需要等待，但**不要浪费时间**，请事件循环（总管家）去处理其他客户端吧！” 这就是为什么异步可以用**单线程**处理多客户端的根本原因。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 动手实践与挑战\n", "\n", "现在，让我们亲手体验这两种并发模型的差异。\n", "\n", "### 基础任务\n", "1.  **运行线程版服务器**: 运行我们L12中健壮的多线程群聊服务器，然后连接3-4个客户端，体验一下效果。\n", "2.  **运行asyncio版服务器**: 运行老师提供的`async_chat_server.py`，同样连接3-4个客户端，体验效果。\n", "3.  **思考**: 在客户端数量少的时候，两种版本用起来有什么区别？\n", "    （答案：几乎没有！但在高并发下，它们的内部工作原理和资源消耗天差地别！）\n", "\n", "### 进阶挑战\n", "尝试在`async_chat_server.py`的基础上增加新功能：\n", "- **昵称功能**: 客户端连接后，发送的第一条消息作为昵称。服务器记录`{writer: nickname}`的映射。广播消息时显示昵称而不是IP地址。\n", "- **时间戳**: 服务器收到消息后，加上当前时间再广播。例如：`[14:30:45] 小明: 大家好!`\n", "- **私聊功能**: 实现`@昵称 消息内容`的格式。服务器查找到目标昵称对应的`writer`，只向它发送消息。"]}, {"cell_type": "markdown", "id": "041605f2", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 进阶功能实现：昵称、时间戳与私聊\n", "\n", "让我们一步步实现这些进阶功能，看看如何让我们的异步聊天服务器更加完善。"]}, {"cell_type": "markdown", "id": "ea3a242e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 1. 昵称功能的核心实现\n", "\n", "首先，我们需要扩展服务器的数据结构来支持昵称管理："]}, {"cell_type": "code", "execution_count": null, "id": "e97a8f6a", "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# 在AsyncChatServer类的__init__方法中添加昵称相关的数据结构\n", "class AsyncChatServerAdvanced:\n", "    def __init__(self, host='0.0.0.0', port=8000):\n", "        self.host = host\n", "        self.port = port\n", "        self.clients = {}  # {writer: address}\n", "        # 新增：昵称管理\n", "        self.nicknames = {}  # {writer: nickname}\n", "        self.nickname_to_writer = {}  # {nickname: writer} 用于快速查找\n", "        self.server = None\n", "        self.running = False\n", "    \n", "    def get_timestamp(self):\n", "        \"\"\"获取当前时间戳\"\"\"\n", "        from datetime import datetime\n", "        return datetime.now().strftime(\"%H:%M:%S\")"]}, {"cell_type": "markdown", "id": "cb873a07", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 在AsyncChatServer类的__init__方法中添加昵称相关的数据结构\n", "class AsyncChatServerAdvanced:\n", "    def __init__(self, host='0.0.0.0', port=8000):\n", "        self.host = host\n", "        self.port = port\n", "        self.clients = {}  # {writer: address}\n", "        # 新增：昵称管理\n", "        self.nicknames = {}  # {writer: nickname}\n", "        self.nickname_to_writer = {}  # {nickname: writer} 用于快速查找\n", "        self.server = None\n", "        self.running = False\n", "    \n", "    def get_timestamp(self):\n", "        \"\"\"获取当前时间戳\"\"\"\n", "        from datetime import datetime\n", "        return datetime.now().strftime(\"%H:%M:%S\")\n", "```"]}, {"cell_type": "markdown", "id": "5b15a05b", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 2. 昵称设置逻辑\n", "\n", "在客户端连接后，第一条消息将被用作昵称设置："]}, {"cell_type": "markdown", "id": "18480908", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["```python\n", "async def handle_client(self, reader, writer):\n", "    client_address = writer.get_extra_info('peername')\n", "    self.clients[writer] = client_address\n", "    \n", "    # 发送欢迎消息，要求设置昵称\n", "    welcome_msg = \"欢迎来到聊天室！请输入您的昵称：\"\n", "    await self.send_message_to_client(writer, welcome_msg)\n", "    \n", "    nickname_set = False\n", "    \n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "aa7804b0", "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["async def handle_client(self, reader, writer):\n", "    client_address = writer.get_extra_info('peername')\n", "    self.clients[writer] = client_address\n", "    \n", "    # 发送欢迎消息，要求设置昵称\n", "    welcome_msg = \"欢迎来到聊天室！请输入您的昵称：\"\n", "    await self.send_message_to_client(writer, welcome_msg)\n", "    \n", "    nickname_set = False\n", "    \n", "    try:\n", "        while self.running and not writer.is_closing():\n", "            data = await reader.read(1024)\n", "            if not data:\n", "                break\n", "            \n", "            decoded_message = data.decode('utf-8').strip()\n", "            if not decoded_message:\n", "                continue\n", "            \n", "            # 昵称设置逻辑\n", "            if not nickname_set:\n", "                # 检查昵称是否已被使用\n", "                if decoded_message in self.nickname_to_writer:\n", "                    error_msg = f\"昵称 '{decoded_message}' 已被使用，请选择其他昵称：\"\n", "                    await self.send_message_to_client(writer, error_msg)\n", "                    continue\n", "                \n", "                # 设置昵称\n", "                nickname = decoded_message\n", "                self.nicknames[writer] = nickname\n", "                self.nickname_to_writer[nickname] = writer\n", "                nickname_set = True\n", "                \n", "                # 确认消息\n", "                confirm_msg = f\"昵称设置成功！您现在是 {nickname}。\"\n", "                await self.send_message_to_client(writer, confirm_msg)\n", "                \n", "                # 广播加入消息\n", "                timestamp = self.get_timestamp()\n", "                join_message = f\"[{timestamp}] [系统消息] {nickname} 加入了聊天室\"\n", "                await self.broadcast(join_message, writer)\n", "                continue\n", "            \n", "            # 处理正常消息...\n", "    except Exception as e:\n", "        print(f\"[错误] 处理客户端时发生错误: {e}\")\n", "    finally:\n", "        await self.remove_client(writer)"]}, {"cell_type": "markdown", "id": "9bed53db", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    try:\n", "        while self.running and not writer.is_closing():\n", "            data = await reader.read(1024)\n", "            if not data:\n", "                break\n", "            \n", "            decoded_message = data.decode('utf-8').strip()\n", "            if not decoded_message:\n", "                continue\n", "            \n", "            # 昵称设置逻辑\n", "            if not nickname_set:\n", "                # 检查昵称是否已被使用\n", "                if decoded_message in self.nickname_to_writer:\n", "                    error_msg = f\"昵称 '{decoded_message}' 已被使用，请选择其他昵称：\"\n", "                    await self.send_message_to_client(writer, error_msg)\n", "                    continue\n", "```"]}, {"cell_type": "markdown", "id": "65f1b551", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "                # 设置昵称\n", "                nickname = decoded_message\n", "                self.nicknames[writer] = nickname\n", "                self.nickname_to_writer[nickname] = writer\n", "                nickname_set = True\n", "                \n", "                # 确认消息\n", "                confirm_msg = f\"昵称设置成功！您现在是 {nickname}。\"\n", "                await self.send_message_to_client(writer, confirm_msg)\n", "                \n", "                # 广播加入消息\n", "                timestamp = self.get_timestamp()\n", "                join_message = f\"[{timestamp}] [系统消息] {nickname} 加入了聊天室\"\n", "                await self.broadcast(join_message, writer)\n", "                continue\n", "            \n", "            # 处理正常消息...\n", "    except Exception as e:\n", "        print(f\"[错误] 处理客户端时发生错误: {e}\")\n", "    finally:\n", "        await self.remove_client(writer)\n", "```"]}, {"cell_type": "markdown", "id": "f7c08b6d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 3. 私聊功能实现\n", "\n", "使用正则表达式解析`@昵称 消息内容`格式。\n", "\n", "> 正则表达式是一个神奇的工具，它可以用来匹配和操作字符串。\n", "> 例如，`^@(\\w+)\\s+(.+)$` 这个正则表达式可以用来匹配 `@昵称 消息内容` 的格式。\n", "> - `^` 表示字符串的开始\n", "> - `@` 匹配字面的 `@` 符号\n", "> - `(\\w+)` 匹配一个或多个字母、数字或下划线（昵称）"]}, {"cell_type": "markdown", "id": "78db8f65", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "import re\n", "\n", "def parse_private_message(self, message):\n", "    # 使用正则表达式匹配 @昵称 格式\n", "    match = re.match(r'^@(\\w+)\\s+(.+)$', message.strip())\n", "    if match:\n", "        target_nickname = match.group(1)\n", "        content = match.group(2)\n", "        return True, target_nickname, content\n", "    return False, None, message\n", "```"]}, {"cell_type": "markdown", "id": "9b864870", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "async def send_private_message(self, target_nickname, message, source_writer):\n", "    \"\"\"\n", "    发送私聊消息\n", "    \"\"\"\n", "    if target_nickname in self.nickname_to_writer:\n", "        target_writer = self.nickname_to_writer[target_nickname]\n", "        if not target_writer.is_closing():\n", "            await self.send_message_to_client(target_writer, message)\n", "            return True\n", "    return False\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "99c6c36e", "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["import re\n", "\n", "def parse_private_message(self, message):\n", "    \"\"\"\n", "    解析私聊消息格式 @昵称 消息内容\n", "    \n", "    Returns:\n", "        tuple: (is_private, target_nickname, content)\n", "    \"\"\"\n", "    # 使用正则表达式匹配 @昵称 格式\n", "    match = re.match(r'^@(\\w+)\\s+(.+)$', message.strip())\n", "    if match:\n", "        target_nickname = match.group(1)\n", "        content = match.group(2)\n", "        return True, target_nickname, content\n", "    return False, None, message\n", "\n", "async def send_private_message(self, target_nickname, message, source_writer):\n", "    \"\"\"\n", "    发送私聊消息\n", "    \"\"\"\n", "    if target_nickname in self.nickname_to_writer:\n", "        target_writer = self.nickname_to_writer[target_nickname]\n", "        if not target_writer.is_closing():\n", "            await self.send_message_to_client(target_writer, message)\n", "            return True\n", "    return False"]}, {"cell_type": "markdown", "id": "5036f260", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 4. 消息处理逻辑整合\n", "\n", "将昵称、时间戳和私聊功能整合到消息处理中："]}, {"cell_type": "markdown", "id": "1110973e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 在handle_client方法中处理正常消息的部分\n", "# 处理正常消息（昵称已设置后）\n", "nickname = self.nicknames[writer]\n", "timestamp = self.get_timestamp()\n", "\n", "# 检查是否是私聊消息\n", "is_private, target_nickname, content = self.parse_private_message(decoded_message)\n", "\n", "if is_private:\n", "    # 处理私聊消息\n", "    if target_nickname == nickname:\n", "        error_msg = \"不能给自己发私聊消息！\"\n", "        await self.send_message_to_client(writer, error_msg)\n", "        continue\n", "```"]}, {"cell_type": "markdown", "id": "0a2b8e20", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    private_message = f\"[{timestamp}] [私聊] {nickname} 对您说: {content}\"\n", "    success = await self.send_private_message(target_nickname, private_message, writer)\n", "    \n", "    if success:\n", "        # 给发送者确认消息\n", "        confirm_msg = f\"[{timestamp}] [私聊已发送] 您对 {target_nickname} 说: {content}\"\n", "        await self.send_message_to_client(writer, confirm_msg)\n", "        print(f\"[私聊] {nickname} -> {target_nickname}: {content}\")\n", "    else:\n", "        # 目标用户不存在\n", "        error_msg = f\"用户 '{target_nickname}' 不在线或不存在！\"\n", "        await self.send_message_to_client(writer, error_msg)\n", "```"]}, {"cell_type": "markdown", "id": "ff8dc2a8", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "else:\n", "    # 处理群聊消息\n", "    broadcast_message = f\"[{timestamp}] {nickname}: {decoded_message}\"\n", "    print(broadcast_message)\n", "    \n", "    # 异步广播消息给所有其他客户端\n", "    await self.broadcast(broadcast_message, writer)\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "36dcb65b", "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["# 在handle_client方法中处理正常消息的部分\n", "# 处理正常消息（昵称已设置后）\n", "nickname = self.nicknames[writer]\n", "timestamp = self.get_timestamp()\n", "\n", "# 检查是否是私聊消息\n", "is_private, target_nickname, content = self.parse_private_message(decoded_message)\n", "\n", "if is_private:\n", "    # 处理私聊消息\n", "    if target_nickname == nickname:\n", "        error_msg = \"不能给自己发私聊消息！\"\n", "        await self.send_message_to_client(writer, error_msg)\n", "        continue\n", "    \n", "    private_message = f\"[{timestamp}] [私聊] {nickname} 对您说: {content}\"\n", "    success = await self.send_private_message(target_nickname, private_message, writer)\n", "    \n", "    if success:\n", "        # 给发送者确认消息\n", "        confirm_msg = f\"[{timestamp}] [私聊已发送] 您对 {target_nickname} 说: {content}\"\n", "        await self.send_message_to_client(writer, confirm_msg)\n", "        print(f\"[私聊] {nickname} -> {target_nickname}: {content}\")\n", "    else:\n", "        # 目标用户不存在\n", "        error_msg = f\"用户 '{target_nickname}' 不在线或不存在！\"\n", "        await self.send_message_to_client(writer, error_msg)\n", "else:\n", "    # 处理群聊消息\n", "    broadcast_message = f\"[{timestamp}] {nickname}: {decoded_message}\"\n", "    print(broadcast_message)\n", "    \n", "    # 异步广播消息给所有其他客户端\n", "    await self.broadcast(broadcast_message, writer)"]}, {"cell_type": "markdown", "id": "07667bba", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 5. 客户端清理逻辑\n", "\n", "当客户端断开连接时，需要清理所有相关的数据结构："]}, {"cell_type": "code", "execution_count": null, "id": "4e1bb0c1", "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["async def remove_client(self, writer):\n", "    \"\"\"\n", "    异步移除客户端，清理所有相关数据\n", "    \"\"\"\n", "    if writer in self.clients:\n", "        client_address = self.clients[writer]\n", "        nickname = self.nicknames.get(writer, str(client_address))\n", "        \n", "        # 清理所有相关数据\n", "        del self.clients[writer]\n", "        if writer in self.nicknames:\n", "            old_nickname = self.nicknames[writer]\n", "            del self.nicknames[writer]\n", "            if old_nickname in self.nickname_to_writer:\n", "                del self.nickname_to_writer[old_nickname]\n", "        \n", "        print(f\"[连接关闭] {nickname} ({client_address}) 已离开。在线人数: {len(self.clients)}\")\n", "        \n", "        # 向其他客户端广播用户离开的消息\n", "        timestamp = self.get_timestamp()\n", "        leave_message = f\"[{timestamp}] [系统消息] {nickname} 离开了聊天室\"\n", "        await self.broadcast(leave_message, writer)\n", "    \n", "    if not writer.is_closing():\n", "        try:\n", "            writer.close()\n", "            await writer.wait_closed()\n", "        except:\n", "            pass"]}, {"cell_type": "markdown", "id": "85b9feb8", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "async def remove_client(self, writer):\n", "    \"\"\"\n", "    异步移除客户端，清理所有相关数据\n", "    \"\"\"\n", "    if writer in self.clients:\n", "        client_address = self.clients[writer]\n", "        nickname = self.nicknames.get(writer, str(client_address))\n", "```"]}, {"cell_type": "markdown", "id": "9b8e7462", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        # 清理所有相关数据\n", "        del self.clients[writer]\n", "        if writer in self.nicknames:\n", "            old_nickname = self.nicknames[writer]\n", "            del self.nicknames[writer]\n", "            if old_nickname in self.nickname_to_writer:\n", "                del self.nickname_to_writer[old_nickname]\n", "        \n", "        print(f\"[连接关闭] {nickname} ({client_address}) 已离开。在线人数: {len(self.clients)}\")\n", "        \n", "``` "]}, {"cell_type": "markdown", "id": "1b21683a", "metadata": {}, "source": ["```python\n", "        # 向其他客户端广播用户离开的消息\n", "        timestamp = self.get_timestamp()\n", "        leave_message = f\"[{timestamp}] [系统消息] {nickname} 离开了聊天室\"\n", "        await self.broadcast(leave_message, writer)\n", "    \n", "    if not writer.is_closing():\n", "        try:\n", "            writer.close()\n", "            await writer.wait_closed()\n", "        except:\n", "            pass\n", "```"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}