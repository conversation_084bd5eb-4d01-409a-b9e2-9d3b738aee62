#!/usr/bin/env python3
"""
简单的文字转语音(TTS)服务器
使用 FastAPI 和 pyttsx3 实现
参考 Notebook/L3.ipynb 中的调用方式
"""

from fastapi import FastAPI, HTTPException
from fastapi.responses import Response
from pydantic import BaseModel
import pyttsx3
import tempfile
import os
import threading
import time
from typing import Optional

# 创建 FastAPI 应用
app = FastAPI(title="TTS Server", description="文字转语音服务器", version="1.0.0")

# 请求模型
class TTSRequest(BaseModel):
    text: str

# 全局 TTS 引擎锁，确保线程安全
tts_lock = threading.Lock()

def create_tts_engine():
    """创建并配置 TTS 引擎"""
    try:
        engine = pyttsx3.init()
        
        # 获取可用的语音
        voices = engine.getProperty('voices')
        
        # 尝试设置中文语音（如果可用）
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                engine.setProperty('voice', voice.id)
                break
        
        return engine
    except Exception as e:
        print(f"创建 TTS 引擎失败: {e}")
        return None

@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {
        "message": "TTS Server is running",
        "endpoints": {
            "synthesize": "/synthesize - POST 请求，将文本转换为语音",
            "health": "/health - 健康检查"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "TTS Server"}

@app.post("/synthesize")
async def synthesize_speech(request: TTSRequest):
    """
    将文本转换为语音
    返回 WAV 格式的音频文件
    """
    if not request.text.strip():
        raise HTTPException(status_code=400, detail="文本不能为空")

    # 创建临时文件来保存音频
    temp_file = None
    try:
        with tts_lock:  # 确保 TTS 引擎的线程安全
            # 创建 TTS 引擎
            engine = create_tts_engine()
            if engine is None:
                raise HTTPException(status_code=500, detail="无法初始化 TTS 引擎")
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            temp_file.close()
            
            # 将文本转换为语音并保存到临时文件
            engine.save_to_file(request.text, temp_file.name)
            engine.runAndWait()
            
            # 等待文件写入完成
            time.sleep(0.5)
            
            # 检查文件是否存在且有内容
            if not os.path.exists(temp_file.name) or os.path.getsize(temp_file.name) == 0:
                raise HTTPException(status_code=500, detail="音频文件生成失败")
            
            # 读取音频文件内容
            with open(temp_file.name, 'rb') as f:
                audio_content = f.read()
            
            # 返回音频文件
            return Response(
                content=audio_content,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": "attachment; filename=speech.wav",
                    "Content-Length": str(len(audio_content))
                }
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"语音合成失败: {str(e)}")
    
    finally:
        # 清理临时文件
        if temp_file and os.path.exists(temp_file.name):
            try:
                os.unlink(temp_file.name)
            except:
                pass

@app.get("/voices")
async def get_available_voices():
    """获取可用的语音列表"""
    try:
        engine = create_tts_engine()
        if engine is None:
            raise HTTPException(status_code=500, detail="无法初始化 TTS 引擎")
        
        voices = engine.getProperty('voices')
        voice_list = []
        
        for voice in voices:
            voice_info = {
                "id": voice.id,
                "name": voice.name,
                "languages": getattr(voice, 'languages', []),
                "gender": getattr(voice, 'gender', 'unknown'),
                "age": getattr(voice, 'age', 'unknown')
            }
            voice_list.append(voice_info)
        
        return {"voices": voice_list}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取语音列表失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    
    print("启动 TTS 服务器...")
    print("服务器将在 http://127.0.0.1:5000 上运行")
    print("API 文档: http://127.0.0.1:5000/docs")
    print("按 Ctrl+C 停止服务器")
    
    # 启动服务器
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=5000,
        log_level="info"
    )
