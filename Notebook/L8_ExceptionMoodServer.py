from fastapi import FastAPI, HTTPException
app = FastAPI()
# 用一个列表来模拟数据库，存储所有心情记录
mood_items = []
# 用一个全局变量来模拟自增ID
mood_counter = 0

@app.post("/moods")
def create_mood(item: dict):
    if "user" not in item or "mood" not in item:
        # 如果缺少字段，就抛出HTTPException
        raise HTTPException(
            status_code=400, # 400表示“错误的请求”
            detail="请求体必须包含 'user' 和 'mood' 字段"
        )
    global mood_counter
    mood_counter += 1

    # 这是一个“定时炸弹”
    new_mood = {
        "id": mood_counter,
        "user": item["user"], # 如果请求里没有"user"键,这里会崩溃!
        "mood": item["mood"], # 如果请求里没有"mood"键,这里也会崩溃!
    }

    mood_items.append(new_mood)
    return new_mood
