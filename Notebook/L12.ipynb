{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# Python网络编程：打造你的局域网“迷你微信”"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！上节课，我们学习了Socket编程，实现了在一台电脑内部的“左手与右手”对话。这很酷，但还不够！\n", "\n", "今天，我们将迎接一个新挑战：**如何把苹果递给坐在你旁边的同桌？** 我们将把本地聊天程序升级，让它走出你的电脑，实现在同一个WiFi下的局域网聊天！最终，我们将打造一个可以支持多人在线的“迷你微信”！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第一步：从`localhost`到局域网 (从自己到同桌)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 新挑战：如何把苹果递给同桌？\n", "之前我们用的`localhost`或`127.0.0.1`，是一个特殊的地址，永远指向“我自己”。所以程序只能在同一台电脑内部通信。\n", "\n", "现在，要和同桌的电脑通信，我们必须解决两个问题：\n", "1.  **服务器要“开门迎客”**：不能只接待来自`localhost`的“自家人”。\n", "2.  **客户端要“按址寻访”**：必须知道同桌电脑的**真实门牌号（局域网IP地址）**才能找对门。\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实战：找到你的IP地址\n", "首先，每个同学都需要找到自己电脑在局域网中的“门牌号”。\n", "\n", "- **在Windows上**:\n", "  1. 按下 `Win+R` 键，输入 `cmd` 并回车。\n", "  2. 在命令提示符中输入 `ipconfig` 并回车。\n", "  3. 查找“无线局域网适配器 Wi-Fi”下的“**IPv4 地址**”，它通常是`192.168.x.x`格式。\n", "\n", "- **在macOS/Linux上**:\n", "  1. 打开“终端 (Terminal)”。\n", "  2. 输入 `ifconfig` 并回车。\n", "  3. 查找 `en0` (WiFi) 或 `eth0` (有线) 下的 `inet4` 地址。\n", "\n", "**请记下这个地址，我们马上就要用它了！**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 代码改造：复用并升级L11的代码\n", "我们将在L11的代码基础上进行两处小小的修改。\n", "\n", "1.  **服务器端 `server.py`：打开大门**\n", "    ```python\n", "    # 原来的代码 (只接待自家人):\n", "    server_socket.bind(('localhost', 8000))\n", "    \n", "    # 修改后的代码 (欢迎所有客人):\n", "    server_socket.bind(('0.0.0.0', 8000))\n", "    ```\n", "    `0.0.0.0`这个特殊地址的意思是“监听本机所有网络接口的连接请求”。\n", "\n", "2.  **客户端 `client.py`：按址寻访**\n", "    ```python\n", "    # 原来的代码 (在自己家找):\n", "    client_socket.connect(('localhost', 8000))\n", "    \n", "    # 修改后的代码 (去朋友家找，需要替换成真实IP):\n", "    client_socket.connect(('***********', 8000))\n", "    ```\n", "    **重要提示**: 这里的`'***********'`需要替换为**运行服务器那台电脑的真实IP地址**。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 动手实践一：1对1局域网聊天\n", "现在，让我们来完成第一次跨电脑的通信！\n", "\n", "1.  **指定角色**: 找一个同学（同学A）当服务器，另一个同学（同学B）当客户端。\n", "2.  **服务器启动**: 同学A运行修改后的`server.py`（绑定地址为`0.0.0.0`），并把自己的IP地址告诉同学B。\n", "3.  **客户端启动**: 同学B运行`client.py`，并将连接地址修改为同学A的IP地址。\n", "4.  **开始聊天**: 如果一切顺利，同学B的程序会提示“已连接到服务器”，然后两台电脑就可以开始互相发送和接收消息了！\n", "\n", "**恭喜！你已经成功实现了两台电脑之间的网络通信，这是构建任何网络应用的基础！**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第二步：从1对1到多对多 (从两人世界到派对现场)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 新情况：第三位同学也想加入聊天\n", "现在，同学A和同学B正在愉快地聊天。如果这时同学C也想加入，会发生什么呢？\n", "\n", "**思考问题:**\n", "- C能成功连接到A的服务器吗？\n", "- 如果C连接失败，会是什么原因？\n", "\n", "让我们通过实验来找出答案！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 动手实践二：第二个客户端的窘境\n", "1.  **保持连接**: 同学A和同学B保持服务器和客户端程序运行，继续聊天。\n", "2.  **新客上门**: 同学C运行客户端程序，填入同学A的IP地址，尝试连接。\n", "3.  **观察现象**:\n", "    - 同学C的客户端程序会卡住，可能显示“正在连接...”，但没有任何进展。\n", "    - 同学A和同学B的聊天完全不受影响，好像C完全不存在一样。\n", "\n", "**为什么会这样？** 服务器明明绑定了`0.0.0.0`，应该能接受任何连接请求，为什么它会“无视”C的存在？"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 问题剖析：服务器的“专注模式” (单线程瓶颈)\n", "核心原因是：我们的服务器代码是**单线程**的，这意味着它一次只能做一件事。\n", "\n", "让我们看看服务器的关键代码部分：\n", "1. `server_socket.accept()`: 等待客户端连接。 **(这是个阻塞函数)**\n", "2. 连接成功后，进入`while`循环处理该客户端的消息。\n", "3. `client_socket.recv()`: 在循环里等待该客户端发送消息。 **(这也是个阻塞函数)**\n", "\n", "**问题所在**：当服务器接受了B的连接后，就一头扎进了与B聊天的`while`循环里。在这个循环中，程序大部分时间都在**等待**B发送消息（阻塞在`recv()`函数）。由于程序是单线程的，它**根本没有机会**回到`accept()`函数去接受C的连接请求。\n", "\n", "**就像餐厅只有一个服务员，他正忙着服务第一位客人，完全没有时间去迎接门口新来的客人。**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 解决方案：多线程 (给餐厅雇更多服务员)\n", "为了让服务器能够同时服务多个客人，我们引入**多线程**技术。\n", "\n", "**餐厅的比喻**:\n", "- **主线程 (餐厅经理)**: 他的工作很简单，只负责在门口`accept()`新客人。\n", "- **子线程 (专属服务员)**: 每当经理接待一位新客人，他就会立刻**雇佣**一个新的、独立的“服务员”（创建一个子线程），并把这位客人完全交给这个服务员去处理后续的所有聊天。经理本人则立刻回到门口，继续等待下一位新客人。\n", "\n", "这样，即使一个服务员正在和客人聊个没完，也完全不影响经理接待新客人。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 多线程服务器代码实现\n", "我们来改造`server.py`，让它支持多线程。客户端代码`client.py`无需任何改动！\n", "```python\n", "# server_multithread.py\n", "import socket\n", "import threading\n", "\n", "# 这是“服务员”的工作内容：处理单个客户端的所有通信\n", "def handle_client(client_socket, client_address):\n", "    print(f\"[新连接] {client_address} 已连接。\")\n", "    try:\n", "        while True:\n", "            message = client_socket.recv(1024).decode('utf-8')\n", "            if not message: # 如果接收到空消息, 说明客户端已断开\n", "                break\n", "            print(f\"收到来自 {client_address} 的消息: {message}\")\n", "            # 简单地将收到的消息回传给客户端\n", "            client_socket.send(f\"服务器已收到你的消息: '{message}'\".encode('utf-8'))\n", "    except ConnectionResetError:\n", "        print(f\"[连接中断] {client_address} 意外断开。\")\n", "    finally:\n", "        print(f\"[连接关闭] {client_address} 已关闭。\")\n", "        client_socket.close()\n", "```"]}, {"cell_type": "markdown", "id": "3aa57c29", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 这是“经理”的工作内容：不断接受新客人\n", "def main():\n", "    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "    server_socket.bind(('0.0.0.0', 8000))\n", "    server_socket.listen(5)\n", "    print(f\"[*] 服务器正在监听 0.0.0.0:8000\")\n", "\n", "    while True:\n", "        client_socket, client_address = server_socket.accept()\n", "        # 每接受一个新客人，就为他创建一个专属服务员（子线程）\n", "        client_handler = threading.Thread(target=handle_client, args=(client_socket, client_address))\n", "        client_handler.start() # 服务员开始工作\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 动手实践三：多人同时在线聊天\n", "\n", "1.  同学A运行新的多线程服务器程序`server_multithread.py`。\n", "2.  同学B运行客户端程序，连接到A的服务器。\n", "3.  同学C也运行客户端程序，连接到同一个服务器。\n", "4.  如果有更多同学，也可以尝试连接。\n", "\n", "**预期结果**：\n", "所有同学都应该能够成功连接到服务器，并能独立地与服务器收发消息。服务器的控制台会显示所有客户端的连接和它们发送的消息。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第三步：实现群聊功能 (真正的“迷你微信”)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 优化思路：引入“中央枢纽”\n", "现在的多线程服务器，每个“服务员”都只能和他负责的“客人”单独聊天。我们希望的是，任何一个客人说的话，所有在场的客人都能听到。这需要一个“**中央枢纽**”来转发消息。\n", "\n", "**技术挑战**:\n", "1.  服务器如何知道“所有在线的客户端”有哪些？ -> 需要一个**在线列表**。\n", "2.  当一个客户端发送消息时，服务器如何把它发给其他人？ -> 需要一个**广播**功能。\n", "3.  多线程同时操作在线列表，会不会打架？ -> 需要一把**线程锁**来维持秩序。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 群聊服务器代码实现\n", "```python\n", "# group_chat_server.py\n", "import socket\n", "import threading\n", "\n", "clients = [] # 存储所有客户端socket的列表\n", "clients_lock = threading.Lock() # 用于在多线程环境下安全地操作clients列表\n", "\n", "def broadcast(message, source_socket):\n", "    with clients_lock:\n", "        for client in clients:\n", "            if client != source_socket: # 不把消息发回给发送者自己\n", "                try:\n", "                    client.send(message)\n", "                except:\n", "                    # 如果发送失败，说明该客户端已断开，将其移除\n", "                    client.close()\n", "                    if client in clients:\n", "                        clients.remove(client)\n", "```"]}, {"cell_type": "markdown", "id": "89771e54", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def handle_client(client_socket, client_address):\n", "    with clients_lock:\n", "        clients.append(client_socket) # 将新客户端添加到列表中\n", "    print(f\"[新连接] {client_address} 已加入聊天室。当前在线人数: {len(clients)}\")\n", "    try:\n", "        while True:\n", "            message = client_socket.recv(1024)\n", "            if not message:\n", "                break\n", "            broadcast_message = f\"来自 {client_address} 的消息: {message.decode('utf-8')}\"\n", "            print(broadcast_message)\n", "            broadcast(broadcast_message.encode('utf-8'), client_socket)\n", "    finally:\n", "        with clients_lock:\n", "            if client_socket in clients:\n", "                clients.remove(client_socket)\n", "        print(f\"[连接关闭] {client_address} 已离开。在线人数: {len(clients)}\")\n", "        client_socket.close()\n", "\n", "# ... (main函数与之前相同) ...\n", "```\n", "现在，当任何一个客户端发送消息时，服务器都会把它广播给所有其他在线的客户端！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 课程总结与挑战任务\n", "\n", "通过今天的课程，我们从一个简单的1对1本地聊天程序开始，逐步发现了单线程的瓶颈，学会了用多线程解决并发问题，并最终将服务器扩展为了一个支持局域网多人在线的群聊系统。我们深入理解了IP地址、Socket通信、多线程编程等关键概念，为未来开发更复杂的网络应用打下了坚实的基础。\n", "\n", "### 挑战任务\n", "- **功能增强**：给用户加上昵称，让聊天更友好。可以在连接时要求用户输入昵称，然后在消息前显示昵称而不是IP地址。\n", "- **UI界面**：复用L11的知识，使用`Tkinter`为客户端做一个简单的图形界面，包括消息显示区、输入框和发送按钮。\n", "- **私聊功能**：在群聊的基础上，增加私聊功能。例如，输入 `@昵称 消息内容`，可以只将消息发送给特定用户。"]}, {"cell_type": "markdown", "id": "5d143fdd", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 挑战任务实现：完整的\"迷你微信\""]}, {"cell_type": "markdown", "id": "95300d68", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 任务一：带昵称功能的群聊服务器\n", "首先实现支持昵称的服务器，让聊天更加友好。\n", "\n", "```python\n", "# enhanced_chat_server.py\n", "import socket\n", "import threading\n", "import json\n", "import time\n", "\n", "class EnhancedChatServer:\n", "    def __init__(self, host='0.0.0.0', port=8000):\n", "        self.host = host\n", "        self.port = port\n", "```"]}, {"cell_type": "markdown", "id": "37eadb38", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        self.clients = {}  # {socket: {'nickname': str, 'address': tuple}}\n", "        self.clients_lock = threading.Lock()\n", "        self.server_socket = None\n", "        self.running = False\n", "    \n", "    def broadcast_message(self, message, sender_socket=None):\n", "        \"\"\"广播消息给所有客户端（除了发送者）\"\"\"\n", "        with self.clients_lock:\n", "            disconnected_clients = []\n", "            for client_socket in self.clients:\n", "```"]}, {"cell_type": "markdown", "id": "0dc085ef", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "                if client_socket != sender_socket:\n", "                    try:\n", "                        client_socket.send(message.encode('utf-8'))\n", "                    except:\n", "                        disconnected_clients.append(client_socket)\n", "            \n", "            # 清理断开的连接\n", "            for client in disconnected_clients:\n", "                self.remove_client(client)\n", "```"]}, {"cell_type": "markdown", "id": "1bfa60d2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def send_private_message(self, message, target_nickname, sender_socket):\n", "        \"\"\"发送私聊消息\"\"\"\n", "        with self.clients_lock:\n", "            target_socket = None\n", "            for client_socket, client_info in self.clients.items():\n", "                if client_info['nickname'] == target_nickname:\n", "                    target_socket = client_socket\n", "                    break\n", "            \n", "            if target_socket:\n", "```"]}, {"cell_type": "markdown", "id": "091c098e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "                try:\n", "                    target_socket.send(message.encode('utf-8'))\n", "                    # 给发送者确认消息\n", "                    sender_socket.send(f\"[私聊已发送给 {target_nickname}]\".encode('utf-8'))\n", "                    return True\n", "                except:\n", "                    self.remove_client(target_socket)\n", "                    return False\n", "            else:\n", "                sender_socket.send(f\"[错误] 用户 '{target_nickname}' 不在线\".encode('utf-8'))\n", "                return False\n", "```"]}, {"cell_type": "markdown", "id": "209a8e2b", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def handle_client(self, client_socket, client_address):\n", "        \"\"\"处理单个客户端连接\"\"\"\n", "        try:\n", "            # 首先接收昵称\n", "            client_socket.send(\"请输入您的昵称: \".encode('utf-8'))\n", "            nickname = client_socket.recv(1024).decode('utf-8').strip()\n", "            \n", "            if not nickname:\n", "                nickname = f\"用户{client_address[1]}\"\n", "```"]}, {"cell_type": "markdown", "id": "32bac7fa", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "            # 检查昵称是否已存在\n", "            with self.clients_lock:\n", "                existing_nicknames = [info['nickname'] for info in self.clients.values()]\n", "                if nickname in existing_nicknames:\n", "                    nickname = f\"{nickname}_{client_address[1]}\"\n", "                \n", "                # 添加客户端到列表\n", "                self.clients[client_socket] = {\n", "                    'nickname': nickname,\n", "                    'address': client_address\n", "                }\n", "```"]}, {"cell_type": "markdown", "id": "9b97197a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "            print(f\"[新连接] {nickname} ({client_address}) 加入聊天室\")\n", "            \n", "            # 欢迎消息\n", "            welcome_msg = f\"欢迎 {nickname} 加入聊天室！当前在线: {len(self.clients)} 人\"\n", "            client_socket.send(welcome_msg.encode('utf-8'))\n", "            \n", "            # 通知其他用户\n", "            join_msg = f\"[系统] {nickname} 加入了聊天室\"\n", "            self.broadcast_message(join_msg, client_socket)\n", "```"]}, {"cell_type": "markdown", "id": "9178cbda", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "            # 发送使用说明\n", "            help_msg = \"\\n使用说明:\\n- 直接输入消息进行群聊\\n- 输入 '@昵称 消息' 进行私聊\\n- 输入 '/list' 查看在线用户\\n- 输入 '/quit' 退出\"\n", "            client_socket.send(help_msg.encode('utf-8'))\n", "            \n", "            # 消息处理循环\n", "            while True:\n", "                try:\n", "                    message = client_socket.recv(1024).decode('utf-8').strip()\n", "                    if not message:\n", "                        break\n", "```"]}, {"cell_type": "markdown", "id": "2e0dd538", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "                    # 处理特殊命令\n", "                    if message == '/quit':\n", "                        break\n", "                    elif message == '/list':\n", "                        with self.clients_lock:\n", "                            online_users = [info['nickname'] for info in self.clients.values()]\n", "                        user_list = f\"在线用户 ({len(online_users)}): {', '.join(online_users)}\"\n", "                        client_socket.send(user_list.encode('utf-8'))\n", "                    elif message.startswith('@'):\n", "                        # 私聊消息处理\n", "                        parts = message[1:].split(' ', 1)\n", "                        if len(parts) >= 2:\n", "                            target_nickname = parts[0]\n", "                            private_msg = parts[1]\n", "                            sender_nickname = self.clients[client_socket]['nickname']\n", "                            formatted_msg = f\"[私聊] {sender_nickname}: {private_msg}\"\n", "                            self.send_private_message(formatted_msg, target_nickname, client_socket)\n", "                        else:\n", "                            client_socket.send(\"[错误] 私聊格式: @昵称 消息内容\".encode('utf-8'))\n", "```"]}, {"cell_type": "markdown", "id": "4e69b014", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "                    else:\n", "                        # 群聊消息\n", "                        sender_nickname = self.clients[client_socket]['nickname']\n", "                        broadcast_msg = f\"[群聊] {sender_nickname}: {message}\"\n", "                        self.broadcast_message(broadcast_msg, client_socket)\n", "                        print(f\"[群聊] {sender_nickname}: {message}\")\n", "                \n", "                except Exception as e:\n", "                    print(f\"[错误] 处理消息时发生错误: {e}\")\n", "                    break\n", "        \n", "        except Exception as e:\n", "            print(f\"[错误] 客户端连接处理错误: {e}\")\n", "        finally:\n", "            self.remove_client(client_socket)\n", "```"]}, {"cell_type": "markdown", "id": "32459e8d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 任务二：Tkinter图形界面客户端\n", "现在实现一个友好的图形界面客户端。\n", "\n", "```python\n", "# gui_chat_client.py\n", "import socket\n", "import threading\n", "import tkinter as tk\n", "from tkinter import scrolledtext, simpledialog, messagebox\n", "import queue\n", "\n", "class ChatClientGUI:\n", "    def __init__(self):\n", "        self.root = tk.Tk()\n", "        self.root.title(\"迷你微信 - 聊天客户端\")\n", "        self.root.geometry(\"600x500\")\n", "```"]}, {"cell_type": "markdown", "id": "3fd9db38", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        self.client_socket = None\n", "        self.connected = False\n", "        self.nickname = \"\"\n", "        self.message_queue = queue.Queue()\n", "        \n", "        self.setup_ui()\n", "        self.root.protocol(\"WM_DELETE_WINDOW\", self.on_closing)\n", "    \n", "    def setup_ui(self):\n", "        \"\"\"设置用户界面\"\"\"\n", "        # 消息显示区域\n", "        self.chat_display = scrolledtext.ScrolledText(\n", "            self.root, \n", "            wrap=tk.WORD, \n", "            state=tk.DISABLED,\n", "            height=20\n", "        )\n", "        self.chat_display.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)\n", "```"]}, {"cell_type": "markdown", "id": "d4be1af6", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        # 输入框架\n", "        input_frame = tk.<PERSON>ame(self.root)\n", "        input_frame.pack(padx=10, pady=(0, 10), fill=tk.X)\n", "        \n", "        # 消息输入框\n", "        self.message_entry = tk.Entry(input_frame, font=(\"Arial\", 12))\n", "        self.message_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))\n", "        self.message_entry.bind(\"<Return>\", self.send_message)\n", "        \n", "        # 发送按钮\n", "        self.send_button = tk.Button(\n", "            input_frame, \n", "            text=\"发送\", \n", "            command=self.send_message,\n", "            font=(\"Arial\", 12),\n", "            bg=\"#4CAF50\",\n", "            fg=\"white\"\n", "        )\n", "        self.send_button.pack(side=tk.RIGHT)\n", "```"]}, {"cell_type": "markdown", "id": "a7030c50", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        # 连接按钮\n", "        self.connect_button = tk.Button(\n", "            self.root, \n", "            text=\"连接服务器\", \n", "            command=self.connect_to_server,\n", "            font=(\"Arial\", 12),\n", "            bg=\"#2196F3\",\n", "            fg=\"white\"\n", "        )\n", "        self.connect_button.pack(pady=10)\n", "        \n", "        # 初始状态下禁用输入\n", "        self.message_entry.config(state=tk.DISABLED)\n", "        self.send_button.config(state=tk.DISABLED)\n", "```"]}, {"cell_type": "markdown", "id": "22868d0f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def connect_to_server(self):\n", "        \"\"\"连接到服务器\"\"\"\n", "        if self.connected:\n", "            return\n", "        \n", "        # 获取服务器地址\n", "        server_ip = simpledialog.askstring(\"服务器地址\", \"请输入服务器IP地址:\", initialvalue=\"localhost\")\n", "        if not server_ip:\n", "            return\n", "        \n", "        # 获取昵称\n", "        self.nickname = simpledialog.askstring(\"昵称\", \"请输入您的昵称:\")\n", "        if not self.nickname:\n", "            return\n", "```"]}, {"cell_type": "markdown", "id": "98ba82bb", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        try:\n", "            # 创建socket连接\n", "            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "            self.client_socket.connect((server_ip, 8000))\n", "            \n", "            # 发送昵称\n", "            welcome_msg = self.client_socket.recv(1024).decode('utf-8')\n", "            self.client_socket.send(self.nickname.encode('utf-8'))\n", "            \n", "            self.connected = True\n", "            \n", "            # 启动接收消息线程\n", "            receive_thread = threading.Thread(target=self.receive_messages)\n", "            receive_thread.daemon = True\n", "            receive_thread.start()\n", "```"]}, {"cell_type": "markdown", "id": "2787b02a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "            # 启动消息处理\n", "            self.process_incoming_messages()\n", "            \n", "            # 更新UI状态\n", "            self.message_entry.config(state=tk.NORMAL)\n", "            self.send_button.config(state=tk.NORMAL)\n", "            self.connect_button.config(text=\"已连接\", state=tk.DISABLED)\n", "            \n", "            self.display_message(f\"已连接到服务器 {server_ip}\")\n", "            \n", "        except Exception as e:\n", "            messagebox.showerror(\"连接错误\", f\"无法连接到服务器: {e}\")\n", "```"]}, {"cell_type": "markdown", "id": "510973fc", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 任务三：完整的私聊和群聊功能\n", "最后实现完整的消息处理逻辑。\n", "\n", "```python\n", "    def receive_messages(self):\n", "        \"\"\"接收消息的线程函数\"\"\"\n", "        while self.connected:\n", "            try:\n", "                message = self.client_socket.recv(1024).decode('utf-8')\n", "                if message:\n", "                    self.message_queue.put(message)\n", "                else:\n", "                    break\n", "            except:\n", "                break\n", "```"]}, {"cell_type": "markdown", "id": "08f631be", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def process_incoming_messages(self):\n", "        \"\"\"处理接收到的消息\"\"\"\n", "        try:\n", "            while not self.message_queue.empty():\n", "                message = self.message_queue.get_nowait()\n", "                self.display_message(message)\n", "        except queue.Empty:\n", "            pass\n", "        \n", "        if self.connected:\n", "            self.root.after(100, self.process_incoming_messages)\n", "    \n", "    def display_message(self, message):\n", "        \"\"\"在聊天窗口显示消息\"\"\"\n", "        self.chat_display.config(state=tk.NORMAL)\n", "        self.chat_display.insert(tk.END, message + \"\\n\")\n", "        self.chat_display.config(state=tk.DISABLED)\n", "        self.chat_display.see(tk.END)\n", "```"]}, {"cell_type": "markdown", "id": "b4771435", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def send_message(self, event=None):\n", "        \"\"\"发送消息\"\"\"\n", "        if not self.connected:\n", "            return\n", "        \n", "        message = self.message_entry.get().strip()\n", "        if message:\n", "            try:\n", "                self.client_socket.send(message.encode('utf-8'))\n", "                self.message_entry.delete(0, tk.END)\n", "                \n", "                # 在本地显示自己发送的消息\n", "                if message.startswith('@'):\n", "                    self.display_message(f\"[我的私聊] {message}\")\n", "                else:\n", "                    self.display_message(f\"[我] {message}\")\n", "                    \n", "            except Exception as e:\n", "                messagebox.showerror(\"发送错误\", f\"发送消息失败: {e}\")\n", "```"]}, {"cell_type": "markdown", "id": "796bb760", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def on_closing(self):\n", "        \"\"\"窗口关闭时的处理\"\"\"\n", "        if self.connected:\n", "            try:\n", "                self.client_socket.send(\"/quit\".encode('utf-8'))\n", "                self.client_socket.close()\n", "            except:\n", "                pass\n", "        self.root.destroy()\n", "    \n", "    def run(self):\n", "        \"\"\"运行GUI\"\"\"\n", "        self.root.mainloop()\n", "\n", "if __name__ == \"__main__\":\n", "    app = ChatClientGUI()\n", "    app.run()\n", "```"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}