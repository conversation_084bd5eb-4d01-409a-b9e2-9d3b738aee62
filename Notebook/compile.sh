#!/bin/bash

# ==============================================================================
#  Jupyter Notebook to Reveal.js Slides Converter
#  Author: Your Senior Software Engineer
#  Description: This script batch converts .ipynb files (L1-L15) to HTML slides.
# ==============================================================================

# 设置转换的起始和结束编号
START_NUM=1
END_NUM=15

# 主循环
echo "开始批量转换 Notebook 到 HTML Slides..."

for i in $(seq $START_NUM $END_NUM)
do
    # 构建源文件名和目标文件名
    SOURCE_FILE="L${i}.ipynb"
    OUTPUT_FILE="L${i}"

    # 检查源文件是否存在，提高脚本的健壮性
    if [ -f "$SOURCE_FILE" ]; then
        echo "正在转换: $SOURCE_FILE -> $OUTPUT_FILE"
        
        # 执行核心转换命令
        # --to slides: 指定输出格式为幻灯片
        # --output: 自定义输出文件名，避免覆盖原始文件
        jupyter nbconvert "$SOURCE_FILE" --to slides --output "$OUTPUT_FILE" --no-input
        
        # 检查上一条命令是否成功
        if [ $? -eq 0 ]; then
            echo "转换成功: $OUTPUT_FILE"
        else
            echo "错误: 转换 $SOURCE_FILE 时发生错误。"
        fi
    else
        echo "警告: 未找到文件 $SOURCE_FILE 已跳过。"
    fi
    
    echo "----------------------------------------"
done

echo "所有转换任务已完成！"

