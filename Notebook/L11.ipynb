{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# Python网络编程：Socket通信与图形化聊天室"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！之前的课程中，我们学会了使用`requests`和`FastAPI`这些强大的“高级工具”来上网和构建API。它们就像是已经装配好的汽车，开起来很方便。\n", "\n", "今天，我们要深入到“汽车”的内部，去看看它的“引擎”——**Socket**是如何工作的。我们将学习如何使用Python进行最底层的网络通信，亲手打造一个支持多人聊天的图形化程序！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 复习：我们如何在网络上通信？"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["网络通信是分层的，就像一个洋葱。我们之前学习的HTTP API工作在最外层的**应用层**，它已经为我们规定好了所有的数据格式和交互规则。\n", "\n", "今天我们要学习的**Socket编程**，则工作在更核心的**传输层**。它不关心你传输的是网页、邮件还是游戏数据，它只负责建立一个可靠的“管道”，让你可以在两台电脑之间自由地传输原始的**字节流**。这给了我们无与伦比的灵活性和控制力。\n", "\n", "| 层级 | 协议举例 | 作用 |\n", "|:---:|:---|:---|\n", "| **应用层** | HTTP, FTP, SMTP | 定义了特定应用（如网页浏览、文件传输）的数据格式和规则。|\n", "| **传输层** | **TCP**, UDP | 负责在两个程序之间建立可靠或不可靠的端到端连接。**Socket工作在这一层**。|\n", "| **网络层** | IP | 负责在网络中根据IP地址找到目标计算机（寻址和路由）。|"]}, {"cell_type": "markdown", "id": "2c28c1b2", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 一切开始之前：我们为什么需要Socket？\n", "\n", "之前的课程中，我们已经熟练掌握了 `requests` 和 `FastAPI`，它们是构建在HTTP协议之上的强大工具。HTTP的工作模式非常经典：客户端发起一个**请求(Request)**，服务器给出一个**响应(Response)**。这就像写信：我写一封信（请求）给你，你收到后给我回一封信（响应）。\n", "\n", "这种“一问一答”的模式非常适合网页浏览、API调用这类场景。但请思考以下场景：\n", "\n", "- **在线多人游戏**：你的每一次移动、射击，都需要**立刻**通知到服务器，并由服务器**立刻**广播给其他玩家。\n", "- **股票行情软件**：你需要服务器**主动、持续地**将最新的股价推送到你的电脑上。\n", "- **我们今天的目标——聊天室**：你发送一条消息，希望它能立即出现在所有人的屏幕上；同时，你也需要能**随时**接收到任何其他人发送的消息。\n", "\n", "如果用HTTP来做这些事，会非常低效。因为HTTP协议的底层虽然是TCP，但它通常是“短连接”的。每一次“问答”都可能需要经历一次完整的TCP“三次握手”来建立连接，然后“四次挥手”来断开连接。这就像为了和朋友说一句话，你需要先拨号、等对方接通、说完话、挂断，下一句话再说，再重复一遍这个完整过程。这其中的延迟和资源开销是巨大的。"]}, {"cell_type": "markdown", "id": "12e13818", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["\n", "**那么，有没有一种方法，可以让我们建立一个稳定、持久的“电话热线”？**\n", "\n", "一旦连接建立，双方就可以随时、自由地、双向地进行通话（数据传输），而不需要每次都重新拨号。这就是我们今天的主角——**Socket**——要解决的问题。它让我们能抛开HTTP的种种规定，直接在两台计算机之间搭建一条原始、高效的数据管道。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 什么是Socket？—— 网络的“电话插座”"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**Socket**（套接字）是网络通信的端点，是应用程序进行网络通信的接口。\n", "\n", "你可以把它想象成计算机墙上的一个“**电话插座**”。两台计算机想要对话，就必须各自拥有一个插座，然后用一根电话线把它们连接起来。\n", "\n", "这个插座有一个唯一的标识，由两部分组成：\n", "- **IP地址**：相当于大楼的地址，用于找到具体的计算机。\n", "- **端口号 (Port)**：相当于大楼里的房间号，用于找到这台计算机上的特定应用程序（比如我们的聊天程序）。\n", "\n", "**一个Socket的唯一标识就是 `IP地址:端口号` 的组合。**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 实战项目第一部分：本地命令行聊天程序\n", "我们先来实现一个最基础的版本：一个服务器程序和一个客户端程序，它们可以在同一台电脑上通过命令行进行一问一答的聊天。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 服务器端 (server.py) 编程步骤\n", "服务器就像一个时刻准备接电话的总机，它遵循固定的流程：\n", "1.  **`socket()`**: 准备一个电话机。\n", "2.  **`bind()`**: 告诉大家这个电话机的号码是`localhost:12345`。\n", "3.  **`listen()`**: 打开电话铃声，开始等待来电。\n", "4.  **`accept()`**: **电话响了！**接起电话，建立一个专门和对方通话的线路(`client_socket`)。\n", "5.  **`recv()` / `sendall()`**: 通过这条专用线路和对方聊天（接收/发送数据）。\n", "6.  **`close()`**: 聊完了，挂断电话。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### `server.py` 完整代码\n", "```python\n", "import socket\n", "# 1. 创建服务器socket\n", "server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "# 2. 绑定地址和端口\n", "server.bind(('localhost', 12345))\n", "# 3. 开始监听，1表示最多允许1个客户端排队\n", "server.listen(1)\n", "print(\"等待客户端连接...\")\n", "# 4. 接受连接，这是一个阻塞操作，会一直等到有客户端连接进来\n", "client, addr = server.accept()\n", "print(f\"客户端 {addr} 已连接\")\n", "```"]}, {"cell_type": "markdown", "id": "ea9ad708", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "while True:\n", "    # 5. 接收客户端消息，1024是缓冲区大小，decode将字节转为字符串\n", "    msg = client.recv(1024).decode('utf-8')\n", "    if not msg:\n", "        break # 如果收到空消息，说明客户端已断开\n", "    print(f\"客户端: {msg}\")\n", "\n", "    # 5. 发送回复，input获取输入，encode将字符串转为字节\n", "    reply = input(\"服务器: \")\n", "    client.sendall(reply.encode('utf-8'))\n", "# 6. 关闭连接\n", "client.close()\n", "server.close()\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 客户端 (client.py) 编程步骤\n", "客户端就像一个主动打电话的人：\n", "1.  **`socket()`**: 准备一个自己的电话机。\n", "2.  **`connect()`**: 按照`localhost:12345`这个号码拨打过去。\n", "3.  **`sendall()` / `recv()`**: 和总机通话。\n", "4.  **`close()`**: 说完再见，挂断电话。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### `client.py` 完整代码\n", "```python\n", "import socket\n", "\n", "# 1. 创建客户端socket\n", "client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "# 2. 连接服务器\n", "client.connect(('localhost', 12345))\n", "print(\"已连接到服务器\")\n", "```"]}, {"cell_type": "markdown", "id": "cacfa91e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "\n", "while True:\n", "    # 3. 发送消息\n", "    msg = input(\"客户端: \")\n", "    if not msg: # 如果输入为空，则准备退出\n", "        break\n", "    client.sendall(msg.encode('utf-8'))\n", "\n", "    # 3. 接收服务器回复\n", "    reply = client.recv(1024).decode('utf-8')\n", "    if not reply:\n", "        break # 如果服务器断开\n", "    print(f\"服务器: {reply}\")\n", "\n", "# 4. 关闭连接\n", "client.close()\n", "```\n", "\n", "**如何运行**：打开两个命令行窗口，一个运行`python server.py`，另一个运行`python client.py`，然后就可以开始聊天了！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 实战升级：构建图形化聊天室"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["命令行界面过于简陋，而且我们的服务器一次只能服务一位客人！接下来，我们要解决这两个问题：\n", "\n", "1.  **支持多人聊天**: 使用`多线程`技术，让我们的服务器能同时为多个客户端服务。\n", "2.  **构建图形化界面 (GUI)**: 使用Python内置的`Tkinter`库，告别黑白命令行。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 服务器的并发瓶颈：一次只能服务一位客人！\n", "在之前的代码中，`server.accept()`和`client.recv()`都是**阻塞**方法。这意味着：\n", "- 当服务器运行到`accept()`时，它会停在那里，死死地等待，直到有第一个客户端连接进来。\n", "- 当它和第一个客户端进入`while`循环聊天时，它又会停在`recv()`那里，等待这个客户端发消息。此时，**它完全无法去`accept()`第二个想连接进来的客户端！**\n", "\n", "这就像一个只有一个服务员的餐厅，当他为一位客人点餐服务时，就无法接待门口排队的新客人。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 解决方案：多线程 (Threading)\n", "为了让服务器能同时服务多个客人，我们引入多线程技术。\n", "\n", "**新的工作流程**：\n", "- **主线程 (餐厅经理)**: 它的工作变得很简单，只负责在门口`accept()`新客人。\n", "- **子线程 (专属服务员)**: 每当经理接待一位新客人，他就会立刻**召唤**一个新的、独立的“服务员”（创建一个子线程），并把这位客人完全交给这个服务员去处理后续的所有聊天（`recv`/`sendall`）。\n", "\n", "经理本人则立刻回到门口，继续等待下一位新客人。这样，即使一个服务员正在和客人进行长时间的聊天，也完全不影响经理接待新客人。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 多线程服务器代码改造\n", "```python\n", "# server_multithread.py\n", "import socket\n", "import threading\n", "\n", "# 这是“服务员”的工作内容：处理单个客户端的所有通信\n", "def handle_client(client_socket, addr):\n", "    print(f\"[新连接] 客户端 {addr} 已连接。\")\n", "    try:\n", "        while True:\n", "            msg = client_socket.recv(1024).decode('utf-8')\n", "            if not msg: break\n", "            print(f\"收到来自 {addr} 的消息: {msg}\")\n", "            # 简单地将收到的消息回传给客户端\n", "            client_socket.sendall(f\"服务器已收到: {msg}\".encode('utf-8'))\n", "    except Exception as e:\n", "        print(f\"处理客户端 {addr} 时发生错误: {e}\")\n", "    finally:\n", "        print(f\"[连接关闭] 客户端 {addr} 已断开。\")\n", "        client_socket.close()\n", "```"]}, {"cell_type": "markdown", "id": "40e129ba", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# ... (服务器绑定和监听的代码不变) ...\n", "\n", "print(\"服务器正在监听连接...\")\n", "while True:\n", "    # 主线程（经理）只负责接受新连接\n", "    client, addr = server.accept()\n", "    # 为每个新连接创建一个新的线程（服务员）来处理\n", "    thread = threading.Thread(target=handle_client, args=(client, addr))\n", "    thread.start() # 启动线程，服务员开始工作\n", "    print(f\"[活跃连接] 当前连接总数: {threading.active_count() - 1}\")\n", "```\n", "*注：客户端代码无需修改，现在你可以启动多个客户端实例同时连接这个服务器了！*"]}, {"cell_type": "markdown", "id": "99f2ea52", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 升级二：构建图形化客户端 (<PERSON><PERSON><PERSON>)\n", "\n", "命令行虽然能验证我们的网络逻辑，但用户体验极差。现在，我们将为客户端穿上漂亮的“外衣”，使用Python内置的Tkinter库来创建一个真正的图形化聊天窗口。\n", "\n", "图形界面程序有一个“天敌”：阻塞操作。\n", "\n", "我们的老客户端代码里 `client.recv(1024)` 是一个阻塞操作。如果把它直接放在GUI代码里，当程序执行到这里时，它会停下来等待服务器的消息。在等待期间，整个GUI窗口将完全冻结：无法移动，按钮无法点击，界面无法刷新。这是绝对不能接受的。\n", "\n", "> 解决方案：再次使用多线程！\n", "\n", "解决方法和我们在服务器上遇到的问题如出一辙：\n", "\n", "主线程：专门负责运行Tkinter的事件循环（`root.mainloop()`），处理所有用户界面交互，保持界面的流畅。\n", "子线程：专门负责处理网络通信，特别是`client.recv()`这个阻塞操作。当它收到消息后，再安全地通知主线程去更新界面。"]}, {"cell_type": "markdown", "id": "2e266b7c", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# client_gui.py\n", "import socket\n", "import threading\n", "import tkinter as tk\n", "from tkinter import scrolledtext, simpledialog, messagebox\n", "import queue\n", "\n", "SERVER_HOST = '127.0.0.1'\n", "SERVER_PORT = 12345\n", "\n", "class ChatClient:\n", "\n", "    def __init__(self, master):\n", "        self.master = master\n", "        self.master.title(\"图形化聊天室\")\n", "        \n", "        # 提示输入用户名\n", "        self.username = simpledialog.askstring(\"用户名\", \"请输入你的昵称:\", parent=self.master)\n", "        if not self.username:\n", "            self.master.destroy()\n", "            return\n", "        self.master.title(f\"图形化聊天室 - {self.username}\")\n", "```"]}, {"cell_type": "markdown", "id": "dcd7c962", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["\n", "```python\n", "        # 创建GUI组件\n", "        self.chat_box = scrolledtext.ScrolledText(master, state='disabled', wrap=tk.WORD)\n", "        self.chat_box.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)\n", "\n", "        self.msg_entry = tk.Entry(master, width=50)\n", "        self.msg_entry.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.X, expand=True)\n", "        self.msg_entry.bind(\"<Return>\", self.send_message)\n", "\n", "        self.send_button = tk.But<PERSON>(master, text=\"发送\", command=self.send_message)\n", "        self.send_button.pack(side=tk.RIGHT, padx=10, pady=10)\n", "\n", "        # 网络通信设置\n", "        self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "        self.message_queue = queue.Queue()\n", "\n", "        # 启动连接和接收线程\n", "        self.connect_to_server()\n", "        \n", "        # 绑定窗口关闭事件\n", "        self.master.protocol(\"WM_DELETE_WINDOW\", self.on_closing)\n", "```"]}, {"cell_type": "markdown", "id": "5da3086d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def connect_to_server(self):\n", "        try:\n", "            self.client_socket.connect((SERVER_HOST, SERVER_PORT))\n", "            # 发送用户名给服务器\n", "            self.client_socket.sendall(self.username.encode('utf-8'))\n", "\n", "            # 开启一个子线程专门接收服务器消息\n", "            receive_thread = threading.Thread(target=self.receive_messages, daemon=True)\n", "            receive_thread.start()\n", "\n", "            # 开启一个定时任务，从队列中读取消息并更新GUI\n", "            self.master.after(100, self.process_incoming)\n", "        except ConnectionRefusedError:\n", "            messagebox.showerror(\"连接错误\", \"无法连接到服务器。请确认服务器已启动。\")\n", "            self.master.destroy()\n", "```"]}, {"cell_type": "markdown", "id": "a9537d15", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def receive_messages(self):\n", "        \"\"\"这个函数在子线程中运行，负责接收网络消息\"\"\"\n", "        while True:\n", "            try:\n", "                msg = self.client_socket.recv(1024).decode('utf-8')\n", "                if not msg:\n", "                    break\n", "                # 将收到的消息放入线程安全的队列中\n", "                self.message_queue.put(msg)\n", "            except (ConnectionAbortedError, ConnectionResetError):\n", "                break\n", "        self.message_queue.put(\"与服务器的连接已断开。\")\n", "```"]}, {"cell_type": "markdown", "id": "e695d770", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def process_incoming(self):\n", "        \"\"\"这个函数在GUI主线程中运行，负责更新UI\"\"\"\n", "        while not self.message_queue.empty():\n", "            try:\n", "                msg = self.message_queue.get_nowait()\n", "                self.chat_box.config(state='normal')\n", "                self.chat_box.insert(tk.<PERSON>, msg + '\\\\n')\n", "                self.chat_box.config(state='disabled')\n", "                self.chat_box.yview(tk.END) # 自动滚动到底部\n", "            except queue.Empty:\n", "                pass\n", "        # 每隔100毫秒再次检查队列\n", "        self.master.after(100, self.process_incoming)\n", "```"]}, {"cell_type": "markdown", "id": "0929d286", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    def send_message(self, event=None):\n", "        msg = self.msg_entry.get()\n", "        if msg:\n", "            try:\n", "                self.client_socket.sendall(msg.encode('utf-8'))\n", "                self.msg_entry.delete(0, tk.END)\n", "            except BrokenPipeError:\n", "                self.message_queue.put(\"无法发送消息，连接已断开。\")\n", "\n", "    def on_closing(self):\n", "        try:\n", "            self.client_socket.close()\n", "        except:\n", "            pass # 忽略关闭时的错误\n", "        self.master.destroy()\n", "```"]}, {"cell_type": "markdown", "id": "1b23a46a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "if __name__ == \"__main__\":\n", "    root = tk.Tk()\n", "    client = ChatClient(root)\n", "    root.mainloop()\n", "```"]}, {"cell_type": "markdown", "id": "31c0a941", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码解析：**\n", "\n", "- GUI与网络分离：`receive_messages`在一个独立的`threading.Thread`中运行，它只负责`recv()`和将消息放入`queue.Queue`。这避免了GUI主线程的阻塞。\n", "- 线程安全通信：我们使用`queue.Queue`作为主线程和网络子线程之间的“信箱”。它是线程安全的，多个线程可以同时读写而不会导致数据混乱。\n", "- UI更新：`process_incoming`函数通过`master.after()`被GUI主线程周期性地调用。它会检查“信箱”(queue)里有没有新消息，如果有，就安全地更新`ScrolledText`组件。这是从子线程更新Tkinter界面的标准范式。\n", "- 用户体验：增加了`simpledialog`来输入昵称，增加了`messagebox`来处理连接错误，并实现了窗口关闭时`on_closing`的优雅退出。"]}, {"cell_type": "markdown", "id": "4b20fd83", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**配合图形化客户端，服务器也需要小幅升级**\n", "\n", "为了让聊天室体验更真实（例如，显示谁发送了消息），我们需要对多线程服务器进行小幅修改，使其能够接收用户名，并将消息广播给所有客户端。\n", "\n", "```python\n", "# server_broadcast.py (升级版的多线程服务器)\n", "import socket\n", "import threading\n", " \n", "HOST = '0.0.0.0' # 监听所有网络接口\n", "PORT = 12345\n", "clients = []\n", "client_lock = threading.Lock()\n", " \n", "def broadcast(message, _sender_socket):\n", "    with client_lock:\n", "        for client_socket in clients:\n", "            # 不把消息发回给发送者自己\n", "            # if client_socket != _sender_socket: \n", "            try:\n", "                client_socket.send(message)\n", "            except:\n", "                # 如果发送失败，说明该客户端已断开，可以将其移除\n", "                client_socket.close()\n", "                clients.remove(client_socket)\n", "```"]}, {"cell_type": "markdown", "id": "e29642be", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def handle_client(client_socket, addr):\n", "    try:\n", "        # 第一个收到的消息是用户名\n", "        username = client_socket.recv(1024).decode('utf-8')\n", "        welcome_msg = f\"系统消息: 欢迎 {username} 加入聊天室！\"\n", "        print(welcome_msg)\n", "        broadcast(welcome_msg.encode('utf-8'), client_socket)  \n", "        # 将新客户端信息存储\n", "        with client_lock:\n", "            clients.append(client_socket)\n", "        while True:\n", "            msg_bytes = client_socket.recv(1024)\n", "            if not msg_bytes:\n", "                break\n", "            # 广播消息给所有客户端\n", "            full_msg = f\"{username}: {msg_bytes.decode('utf-8')}\"\n", "            print(f\"收到来自 {addr} ({username}) 的消息: {msg_bytes.decode('utf-8')}\")\n", "            broadcast(full_msg.encode('utf-8'), client_socket)\n", "    except Exception as e:\n", "        print(f\"处理客户端 {addr} 时发生错误: {e}\")\n", "    finally:\n", "        # 当客户端断开时，从列表中移除并通知其他人\n", "        with client_lock:\n", "            if client_socket in clients:\n", "                clients.remove(client_socket)\n", "        farewell_msg = f\"系统消息: {username} 离开了聊天室。\"\n", "        print(farewell_msg)\n", "        broadcast(farewell_msg.encode('utf-8'), client_socket)\n", "        client_socket.close()\n", "```"]}, {"cell_type": "markdown", "id": "1a4860be", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def start_server():\n", "    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "    server.bind((HOST, PORT))\n", "    server.listen(5)\n", "    print(f\"服务器已在 {HOST}:{PORT} 启动，等待连接...\")\n", " \n", "    while True:\n", "        client, addr = server.accept()\n", "        thread = threading.Thread(target=handle_client, args=(client, addr))\n", "        thread.start()\n", " \n", "if __name__ == \"__main__\":\n", "    start_server()\n", "```"]}, {"cell_type": "markdown", "id": "aff3f44f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**如何运行最终的图形化聊天室**\n", "\n", "先在一个终端中运行升级版的服务器：`python server_broadcast.py`\n", "\n", "然后，可以打开多个终端，分别运行图形化客户端：`python client_gui.py`\n", "\n", "每个客户端启动时都会提示输入昵称。输入后，你就可以在多个窗口之间实时聊天了！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 终极挑战：在聊天室里传文件"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 为什么传文件这么难？—— “鸡同鸭讲”的困境\n", "与发送简单的文本（和本质上也是文本的Emoji）不同，文件传输是一个真正的挑战。因为在原始的TCP“管道”里，所有东西都是字节流，这会引发几个严重问题：\n", "\n", "1.  **消息类型混淆**: 服务器收到一长串字节，它完全不知道这串字节是普通的聊天消息，还是一张图片文件的开头？这就好比电话里突然传来一阵噪音，你不知道是对方在清嗓子还是电话线路出了问题。\n", "2.  **未知文件大小**: 即使服务器猜到这是一张图片，它也不知道这张图片有多大。它应该接收多少字节才算接收完呢？如果接收少了，图片会损坏；如果一直傻等，程序就会被阻塞。\n", "3.  **缺失文件信息**: 就算服务器奇迹般地接收了完整的文件字节，它也不知道这个文件叫什么名字（`cat.jpg`? `dog.png`?），无法正确地保存。\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 解决方案：设计一个简单的应用层协议\n", "**为什么需要应用层协议？** 因为TCP只提供了一条可靠的“电话线”，但它不规定我们“说什么”和“怎么说”。为了解决“鸡同鸭讲”的困境，我们必须在这条电话线之上，自己定义一套更高级别的“**交流规则**”——这就是**应用层协议**。\n", "\n", "**我们的协议规则（就像快递面单）**：\n", "我们规定，所有消息都以一个**JSON头**开始，这个头说清楚了后续数据的类型和信息。\n", "\n", "- **普通消息的“面单”**: \n", "  `{\"type\": \"message\", \"data\": \"你好!\"}`\n", "\n", "- **文件传输的“面单”**: \n", "  `{\"type\": \"file\", \"filename\": \"my_cat.jpg\", \"filesize\": 54321}`\n", "\n", "**传输流程**：\n", "1.  **发送方**: 先把这个JSON“面单”发过去。\n", "2.  **接收方**: 先接收并解析这个“面单”。\n", "3.  **接收方决策**: “哦，面单上说这是一个文件，名字叫`my_cat.jpg`，大小是54321字节。好的，我准备接收54321字节的数据，并把它存为`my_cat.jpg`”。\n", "4.  **发送方**: 发送文件本身的原始字节数据。\n", "5.  **接收方**: 按照计划接收指定大小的数据。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 文件发送方代码逻辑\n", "```python\n", "import json, os, socket\n", "\n", "# 1. 准备文件头部信息\n", "file_path = 'my_cat.jpg' # 假设要发送的图片文件\n", "filename = os.path.basename(file_path)\n", "filesize = os.path.getsize(file_path)\n", "\n", "header = {'type': 'file', 'filename': filename, 'filesize': filesize}\n", "header_bytes = json.dumps(header).encode('utf-8')\n", "```"]}, {"cell_type": "markdown", "id": "e3f96380", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 2. 发送头部长度（关键步骤，确保接收方知道头部有多长）\n", "# 我们约定用10个字节来表示头部的长度，不足则用空格补齐\n", "header_length_bytes = str(len(header_bytes)).encode('utf-8').ljust(10)\n", "client_socket.send(header_length_bytes)\n", "\n", "# 3. 发送头部数据\n", "client_socket.send(header_bytes)\n", "\n", "# 4. 发送文件数据\n", "with open(file_path, 'rb') as f:\n", "    for chunk in iter(lambda: f.read(4096), b''):\n", "        client_socket.sendall(chunk)\n", "print(f\"文件 '{filename}' 发送完毕。\")\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 文件接收方代码逻辑\n", "\n", "```python\n", "# 假设 client_socket 已经连接\n", "\n", "# 1. 接收固定长度的头部长度信息\n", "header_length_bytes = client_socket.recv(10)\n", "header_length = int(header_length_bytes.decode('utf-8').strip())\n", "\n", "# 2. 根据解析出的长度，接收完整的头部\n", "header_bytes = client_socket.recv(header_length)\n", "header = json.loads(header_bytes.decode('utf-8'))\n", "```"]}, {"cell_type": "markdown", "id": "e8099765", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 3. 根据头部信息接收文件数据\n", "if header.get('type') == 'file':\n", "    filename = 'received_' + header['filename']\n", "    filesize = header['filesize']\n", "    \n", "    with open(filename, 'wb') as f:\n", "        received_bytes = 0\n", "        while received_bytes < filesize:\n", "            # 计算还需要接收多少字节\n", "            remaining = filesize - received_bytes\n", "            # 本次接收的字节数不能超过缓冲区大小和剩余大小\n", "            chunk = client_socket.recv(min(4096, remaining))\n", "            if not chunk: break\n", "            f.write(chunk)\n", "            received_bytes += len(chunk)\n", "    print(f\"文件 '{filename}' 接收完毕。\")\n", "else:\n", "    # 按普通消息处理\n", "    print(f\"收到消息: {header}\")\n", "```"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}