{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# Python 和 API 的奇妙探险之旅"]}, {"cell_type": "markdown", "id": "536335c5", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！今天我将带领大家一起踏上一段奇妙的探险之旅，揭开 **应用程序编程接口（API）** 的神秘面纱。\n", "\n", "在这节课里，我们不仅会理解API的工作原理，还会亲自动手，让两个不同的API进行一场“接力赛”，最终把国际空间站的坐标变成我们看得懂的真实地名！"]}, {"cell_type": "markdown", "id": "0f8972dc", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 什么是API？让“点餐”帮你理解！\n", "API（应用程序编程接口）是不同软件之间互相“说话”的桥梁和规则。\n", "- **厨房 (数据源)**: 存储着各种“食材”(数据)的服务器。\n", "- **点餐系统 (API)**: 你(程序)和厨房(数据源)之间沟通的桥梁。\n", "- **菜单 (API 文档)**: 指导你如何正确地点餐（发送请求）。"]}, {"cell_type": "markdown", "id": "68636777", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 程序如何“点餐”：HTTP 请求\n", "\n", "在网络世界里，我们的程序通过发送一个叫做 **HTTP 请求 (Request)** 的东西来“点餐”。它就像一张点餐单，包含几个重要信息：\n", "\n", "- **URL (地址)**: 你要去的餐厅地址和菜品名字。例如：`https://api.wheretheiss.at/v1/satellites/25544`。\n", "\n", "- **方法 (Method)**: 最常见的是 `GET`，意思是我只想“获取(Get)”信息。\n", "\n", "- **请求头 (Headers)**: 额外信息，比如你的身份，或者你希望返回什么格式的数据。"]}, {"cell_type": "markdown", "id": "6f9b5172", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### “上菜了！”：理解API的回应\n", "服务器收到请求后，会给我们一个 **HTTP 响应 (Response)**，就像服务员上菜一样。它也包含几部分：\n", "\n", "1.  **状态码 (Status Code)**: 一个数字，告诉我们“点餐”的结果。\n", "    - `200 OK`: 成功！菜（数据）来了！\n", "    - `404 Not Found`: 糟糕，你点的菜（地址）不存在！\n", "    - （想看更多可爱的状态码吗？访问 [http.dog](https://http.dog/)）\n", "\n", "2.  **响应体 (Body)**: 这就是我们真正想要的“菜”——数据本身！这些数据通常使用一种叫 **JSON** 的格式。"]}, {"cell_type": "markdown", "id": "297ecd94", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 解码“菜品”：认识JSON格式\n", "JSON 是一种非常适合程序阅读的数据格式。它就像一张写得非常工整的菜单，用两种符号来组织信息：\n", "\n", "- **花括号 `{}`**: 表示一个“对象”，就像Python里的**字典**。里面是一对对的 `\"键\": 值`。\n", "  - *例子: `{\"name\": \"小明\", \"age\": 12}`*\n", "\n", "- **方括号 `[]`**: 表示一个“数组”，就像Python里的**列表**。里面是一串值。\n", "  - *例子: `[\"苹果\", \"香蕉\", \"橘子\"]`*"]}, {"cell_type": "markdown", "id": "65056d04", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 动手实践：API的“接力赛”\n", "\n", "**我们的任务：**\n", "1.  **第一棒**: 调用 **Where the ISS at API**，获取国际空间站（ISS）当前的经纬度坐标。\n", "2.  **第二棒**: 将获取到的经纬度坐标，传递给 **Nominatim API**，把它转换成具体的地理位置名称。"]}, {"cell_type": "markdown", "id": "c4c9353e", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第一棒：获取国际空间站的实时位置"]}, {"cell_type": "code", "execution_count": 81, "id": "b715cc21", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一棒完成！成功获取ISS坐标: 纬度 -40.7716, 经度 175.1049\n", "高度: 430.72 公里, 速度: 27549.02 公里/小时\n"]}], "source": ["import requests\n", "\n", "# 使用新的API：Where the ISS at\n", "iss_url = \"https://api.wheretheiss.at/v1/satellites/25544\"\n", "latitude, longitude = None, None # 初始化变量\n", "\n", "try:\n", "    response = requests.get(iss_url)\n", "    # 如果请求不成功(例如404, 500)，这行代码会抛出异常\n", "    response.raise_for_status() \n", "    \n", "    # .json()方法将服务器返回的JSON文本自动转换成Python字典\n", "    iss_data = response.json()\n", "    \n", "    # 新API直接返回latitude和longitude字段\n", "    latitude = iss_data.get('latitude')\n", "    longitude = iss_data.get('longitude')\n", "    altitude = iss_data.get('altitude')  # 额外获取高度信息\n", "    velocity = iss_data.get('velocity')  # 额外获取速度信息\n", "\n", "    if latitude and longitude:\n", "        print(f\"第一棒完成！成功获取ISS坐标: 纬度 {latitude:.4f}, 经度 {longitude:.4f}\")\n", "        print(f\"高度: {altitude:.2f} 公里, 速度: {velocity:.2f} 公里/小时\")\n", "    else:\n", "        print(\"未能从API响应中获取有效的坐标。\")\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"请求ISS位置失败: {e}\")"]}, {"cell_type": "markdown", "id": "f38173d2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "import requests\n", "\n", "# 使用新的API：Where the ISS at\n", "iss_url = \"https://api.wheretheiss.at/v1/satellites/25544\"\n", "response = requests.get(iss_url)\n", "\n", "# 检查请求并解析数据\n", "if response.status_code == 200:\n", "    iss_data = response.json()\n", "    latitude = iss_data['latitude']\n", "    longitude = iss_data['longitude']\n", "    altitude = iss_data['altitude']\n", "    velocity = iss_data['velocity']\n", "    print(f\"成功获取ISS坐标: 纬度 {latitude:.4f}, 经度 {longitude:.4f}\")\n", "    print(f\"高度: {altitude:.2f} 公里, 速度: {velocity:.2f} 公里/小时\")\n", "```"]}, {"cell_type": "markdown", "id": "ce80e06a", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第二棒：从坐标到国家！\n", "现在，我们手握经纬度这对“密码”，需要把它交给第二个API来“解密”。这个过程叫做**反向地理编码 (Reverse Geocoding)**。"]}, {"cell_type": "markdown", "id": "bd23c2cc", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 认识 Nominatim API\n", "- **功能**: 将经纬度坐标转换为地址。\n", "- **地址**: `https://nominatim.openstreetmap.org/reverse`\n", "- **关键参数**:\n", "    - `lat`: 纬度\n", "    - `lon`: 经度\n", "    - `format`: 指定返回格式，我们要`jsonv2`\n", "    - `accept-language`: 指定返回的语言，我们用`zh-CN`获取中文！"]}, {"cell_type": "markdown", "id": "1a1672b0", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### API礼仪：设置`User-Agent`\n", "许多公共API要求你在请求时“自我介绍”，这通过设置请求头（Headers）中的`User-Agent`来实现。\n", "\n", "这是一种良好的编程礼仪，表示你不是一个恶意的机器人。\n", "\n", "它就像去别人家敲门前，先说一句“你好，我是来送快递的”，而不是一言不发。\n", "\n", "大多数库（包括`requests`）都会自动帮你设置一个默认的`User-Agent`，但如果你要自定义它，可以像下面这样做（这是一个真实的浏览器UA）。\n", "```python\n", "headers = {\n", "    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'\n", "}\n", "response = requests.get(url, headers=headers)\n", "```\n", "\n", "可以看到，里面有很多有用的信息，比如浏览器版本、内核版本、操作系统版本等。\n", "\n", "真实世界中，除了浏览器的UA以外，很多自定义UA都会被服务器拒绝。这是因为有些服务器不希望被滥用，所以会检查UA是否合法。"]}, {"cell_type": "code", "execution_count": 82, "id": "a8474c97", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "开始第二棒... 正在将坐标发送给地理位置API...\n", "第二棒完成！成功将坐标转换为地名！\n"]}], "source": ["location_name = \"一个未知区域\"\n", "\n", "if latitude and longitude:\n", "    try:\n", "        print(\"\\n开始第二棒... 正在将坐标发送给地理位置API...\")\n", "        geo_url = \"https://nominatim.openstreetmap.org/reverse\"\n", "        params = {\n", "            'format': 'jsonv2',\n", "            'lat': latitude,\n", "            'lon': longitude,\n", "            'accept-language': 'zh-CN'\n", "        }\n", "        headers = {\n", "            'User-Agent': 'HelloWorld/1.0'\n", "        }\n", "        \n", "        geo_response = requests.get(geo_url, params=params, headers=headers)\n", "        geo_response.raise_for_status()\n", "        geo_data = geo_response.json()\n", "        \n", "        location_name = geo_data.get('display_name', '海洋或偏远地区上空')\n", "        print(\"第二棒完成！成功将坐标转换为地名！\")\n", "        \n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"请求地理位置失败: {e}\")\n", "else:\n", "    print(\"没有坐标，无法进行第二棒。\")"]}, {"cell_type": "markdown", "id": "bc455c34", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "if latitude and longitude:\n", "    # 准备请求Nominatim API\n", "    geo_url = \"https://nominatim.openstreetmap.org/reverse\"\n", "    params = {\n", "        'format': 'jsonv2',\n", "        'lat': latitude,\n", "        'lon': longitude,\n", "        'accept-language': 'zh-CN' # 请求中文结果\n", "    }\n", "    headers = {\n", "        'User-Agent': 'HelloWorld/1.0'\n", "    }\n", "    \n", "    # 发送请求并解析\n", "    geo_response = requests.get(geo_url, params=params, headers=headers)\n", "    geo_data = geo_response.json()\n", "    location_name = geo_data.get('display_name', '海洋上空')\n", "    print(\"成功将坐标转换为地名！\")\n", "```"]}, {"cell_type": "markdown", "id": "306fd35e", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 终点冲刺：展示最终结果！\n", "现在，我们把两棒接力赛获得的所有信息组合起来，得出一个完整的、有意义的结论。"]}, {"cell_type": "code", "execution_count": 83, "id": "de5c119d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 国际空间站实时追踪报告 ---\n", "当前坐标: 纬度 -40.7716, 经度 175.1049\n", "当前位置: 169, Harakeke Road, Te Horo Beach, Kāpiti Coast District, 惠灵顿 / 惠靈頓 / 威靈頓, 5583, 紐西蘭/新西兰\n"]}], "source": ["if latitude and longitude:\n", "    print(\"\\n--- 国际空间站实时追踪报告 ---\")\n", "    print(f\"当前坐标: 纬度 {latitude:.4f}, 经度 {longitude:.4f}\")\n", "    print(f\"当前位置: {location_name}\")\n", "else:\n", "    print(\"\\n无法生成追踪报告，因为初始位置获取失败。\")"]}, {"cell_type": "markdown", "id": "a3a36391", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "if latitude and longitude:\n", "    print(\"\\n--- 国际空间站实时追踪报告 ---\")\n", "    print(f\"当前坐标: 纬度 {latitude:.4f}, 经度 {longitude:.4f}\")\n", "    print(f\"当前位置: {location_name}\")\n", "else:\n", "    print(\"\\n无法生成追踪报告，因为初始位置获取失败。\")\n", "```"]}, {"cell_type": "markdown", "id": "practice_section", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 练习时间：探索太空中的宇航员！\n", "\n", "现在轮到你们大显身手了！我们要使用另一个有趣的API来了解现在有多少宇航员在太空中，以及他们来自哪些国家。"]}, {"cell_type": "markdown", "id": "practice_intro", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 任务说明\n", "\n", "我们将使用真实的宇航员API来获取当前在太空中的宇航员信息。你的任务是：\n", "\n", "1. **获取宇航员总数**\n", "2. **统计不同飞行器上的宇航员人数**\n", "3. **找出国际空间站(ISS)和中国天宫空间站分别有多少宇航员**\n", "\n", "**注意**: 我们使用Open Notify API，这是一个免费的公共API服务。"]}, {"cell_type": "code", "execution_count": 84, "id": "practice_code", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["太空中总共有 12 名宇航员！\n", "API状态: success\n"]}], "source": ["# 练习：分析太空中的宇航员信息\n", "import requests\n", "\n", "# 使用真实的宇航员API\n", "astronauts_url = \"http://api.open-notify.org/astros.json\"\n", "\n", "try:\n", "    response = requests.get(astronauts_url)\n", "    response.raise_for_status()\n", "    astronauts_data = response.json()\n", "    \n", "    print(f\"太空中总共有 {astronauts_data['number']} 名宇航员！\")\n", "    print(f\"API状态: {astronauts_data['message']}\")\n", "    \n", "except requests.exceptions.RequestException as e:\n", "    print(f\"获取宇航员信息失败: {e}\")\n", "    # 如果API失败，使用备用数据\n", "    astronauts_data = {\n", "    \"people\": [\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON><PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON><PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"ISS\",\n", "        \"name\": \"<PERSON><PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"Tiangong\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"Tiangong\",\n", "        \"name\": \"<PERSON>\"\n", "        },\n", "        {\n", "        \"craft\": \"Tiangong\",\n", "        \"name\": \"<PERSON>\"\n", "        }\n", "    ],\n", "    \"number\": 12,\n", "    \"message\": \"success\"\n", "    }\n", "    print(f\"使用备用数据: 太空中总共有 {astronauts_data['number']} 名宇航员！\")"]}, {"cell_type": "code", "execution_count": 85, "id": "practice_analysis", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "各飞行器宇航员分布：\n", "ISS: 9 人\n", "Tiangong: 3 人\n", "\n", "国际空间站(ISS): 9 人\n", "中国天宫空间站: 3 人\n", "\n", "太棒了！中国天宫空间站也有宇航员在工作！\n", "这说明人类太空探索正在蓬勃发展！\n"]}], "source": ["# 统计各飞行器上的宇航员人数\n", "craft_count = {}\n", "\n", "for astronaut in astronauts_data['people']:\n", "    craft = astronaut['craft']\n", "    name = astronaut['name']\n", "    \n", "    # 统计飞行器\n", "    if craft in craft_count:\n", "        craft_count[craft] += 1\n", "    else:\n", "        craft_count[craft] = 1\n", "\n", "print(\"\\n各飞行器宇航员分布：\")\n", "for craft, count in craft_count.items():\n", "    print(f\"{craft}: {count} 人\")\n", "\n", "# 特别关注ISS和天宫空间站\n", "iss_count = craft_count.get('ISS', 0)\n", "tiangong_count = craft_count.get('Tiangong', 0)\n", "\n", "print(f\"\\n国际空间站(ISS): {iss_count} 人\")\n", "print(f\"中国天宫空间站: {tiangong_count} 人\")\n", "\n", "if tiangong_count > 0:\n", "    print(\"\\n太棒了！中国天宫空间站也有宇航员在工作！\")\n", "    print(\"这说明人类太空探索正在蓬勃发展！\")"]}, {"cell_type": "markdown", "id": "practice_explanation", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 代码解析\n", "\n", "在这个练习中，我们学会了：\n", "\n", "1. **调用真实API**: 使用`http://api.open-notify.org/astros.json`获取实时宇航员数据\n", "2. **异常处理**: 使用`try-except`处理网络请求可能的失败\n", "3. **数据统计**: 用字典来统计不同飞行器上的宇航员人数\n", "4. **备用方案**: 当API失败时，使用备用数据确保程序正常运行\n", "\n", "**有趣发现**: 现在太空中不仅有国际空间站，还有中国的天宫空间站！这展示了人类太空探索的多样化发展。"]}, {"cell_type": "markdown", "id": "356cd7ad", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 恭喜！\n", "\n", "通过今天的学习，你不仅掌握了调用API的基础，还学会了如何像真正的工程师一样，将不同的API服务链接起来，解决更复杂的问题。\n", "\n", "你们都是最棒的“API魔术师”！"]}, {"cell_type": "markdown", "id": "93e4964c", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["### 谢谢大家！"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}