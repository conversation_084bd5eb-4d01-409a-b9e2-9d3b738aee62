{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# 团队打造多人联机蛇棋游戏"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！欢迎来到我们课程的最终章——团队打造一个经典的多人联机蛇棋游戏！\n", "\n", "在这个项目中，我们将分组协作，真实地体验一次游戏开发的完整流程。我们将学习如何进行团队分工、如何定义前后端通信的“暗号”（网络协议），并将之前所有学过的知识融会贯通，创造出一个可以让多个玩家在不同电脑上一起玩的联机游戏！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第一步：团队分工 (前端 vs 后端)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["一个完整的网络应用，通常分为“前端”和“后端”两部分。就像一辆汽车，它们各司其职，又紧密配合。\n", "\n", "### 前端组 (UI / 客户端)\n", "- **职责**: 负责游戏的“**长相**”。\n", "- **技术栈**: `Pygame Zero`\n", "- **核心任务**: \n", "  - 绘制漂亮的棋盘、棋子和骰子。\n", "  - 响应玩家的点击操作（比如点击“掷骰子”按钮）。\n", "  - 接收来自后端的数据，并更新游戏画面。\n", "\n", "### 后端组 (服务器)\n", "- **职责**: 负责游戏的“**大脑**”。\n", "- **技术栈**: `Python socket` 和 `threading`\n", "- **核心任务**: \n", "  - 维护所有玩家的位置和游戏状态。\n", "  - 控制游戏回合，处理玩家的行动请求。\n", "  - 计算掷骰子后的新位置，判断是否遇到蛇或梯子。\n", "  - 判断游戏胜负并广播结果。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第二步：沟通的桥梁——设计我们的网络协议"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["前端和后端是两个独立的程序，它们如何对话？答案是：**制定一套统一的“暗号”**，这就是**网络协议**。\n", "\n", "我们将使用**JSON**格式来定义我们的“暗号”，因为它既方便人阅读，也方便机器解析。每一条消息都会有一个`type`字段，来表明这条消息的意图。\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 核心“暗号”设计 (C2S: Client to Server)\n", "这些是客户端（玩家）发给服务器的消息。\n", "\n", "**1. 加入游戏 (`JOIN`)**\n", "```json\n", "{\n", "  \"type\": \"JOIN\",\n", "  \"name\": \"英雄\"\n", "}\n", "```\n", "*时机：玩家启动游戏并输入昵称后发送。*\n", "\n", "**2. 掷骰子 (`ROLL_DICE`)**\n", "```json\n", "{\n", "  \"type\": \"ROLL_DICE\"\n", "}\n", "```\n", "*时机：轮到玩家行动时，点击“掷骰子”按钮后发送。*"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 核心“暗号”设计 (S2C: Server to Client)\n", "这些是服务器发给客户端（一个或所有玩家）的消息。\n", "\n", "**1. 游戏状态 (`GAME_STATE`)**\n", "```json\n", "{\n", "  \"type\": \"GAME_STATE\",\n", "  \"data\": { ... } // 包含所有玩家的位置、名称、颜色等完整游戏快照\n", "}\n", "```\n", "*时机：服务器定期或在任何状态变化后广播给所有玩家。*\n", "\n", "**2. 轮到你了 (`YOUR_TURN`)**\n", "```json\n", "{\n", "  \"type\": \"YOUR_TURN\",\n", "  \"message\": \"轮到你掷骰子了!\"\n", "}\n", "```\n", "*时机：当轮到某个玩家行动时，服务器单独发送给该玩家。*"]}, {"cell_type": "markdown", "id": "0699fdb6", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["\n", "**3. 游戏结束 (`GAME_OVER`)**\n", "```json\n", "{\n", "  \"type\": \"GAME_OVER\",\n", "  \"winner\": \"小明\"\n", "}\n", "```\n", "*时机：当有玩家到达终点时，服务器广播给所有玩家。*"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第三步：后端组开发任务 (打造游戏的“大脑”)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 任务清单 (分步实现)\n", "\n", "1.  **搭建服务器框架**: 基于L12的多线程服务器代码，搭建好主体框架，创建`players`字典和`game_lock`线程锁。\n", "2.  **实现`JOIN`逻辑**: 在`handle_client`中，处理`type`为`JOIN`的请求。为新玩家分配一个唯一的`player_id`、一个颜色，并将其信息存入`players`字典。然后，广播一条消息通知所有玩家有新人加入。\n", "3.  **实现回合管理**: 设计一个`player_ids_in_order`列表来管理玩家顺序。实现一个`next_turn()`函数，用于切换到下一个玩家，并向他发送`YOUR_TURN`消息。\n", "4.  **实现`ROLL_DICE`逻辑**: 在`handle_client`中，处理`ROLL_DICE`请求。**注意：一定要检查`player_id`是否是当前回合的玩家！** 生成随机数，计算新位置，检查是否遇到蛇或梯子，并更新`players`字典中的位置。\n", "5.  **实现游戏结束判断**: 每次移动后，检查玩家位置是否到达终点。如果到达，广播`GAME_OVER`消息。\n", "6.  **实现广播功能**: 编写`broadcast_to_all()`函数，它能遍历`players`字典，将消息发送给所有连接的客户端，并能优雅地处理客户端断开连接的情况。\n", "\n", "**准备好你们的“发动机”，等待前端来对接！**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["#### 后端数据结构设计\n", "**棋盘 (board_layout)**\n", "```python\n", "# 字典的键是起点，值是终点\n", "BOARD_LAYOUT = {\n", "    3: 15,  # 梯子: 从3爬到15\n", "    8: 26,  # 梯子: 从8爬到26\n", "    16: 6,  # 蛇: 从16滑到6\n", "    48: 24  # 蛇: 从48滑到24\n", "}\n", "```\n", "**玩家列表 (players)**\n", "```python\n", "players = {\n", "    \"player_1\": {\n", "        \"socket\": conn_object, \n", "        \"name\": \"小明\", \n", "        \"position\": 0,\n", "        \"color\": (255, 0, 0)\n", "    },\n", "    \"player_2\": { ... }\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - 服务器框架\n", "```python\n", "import socket\n", "import threading\n", "import json\n", "import random\n", "\n", "# 游戏配置\n", "BOARD_SIZE = 50\n", "BOARD_LAYOUT = {3: 15, 8: 26, 16: 6, 48: 24}\n", "COLORS = [(255,0,0), (0,255,0), (0,0,255), (255,255,0)]\n", "\n", "# 全局变量\n", "players = {}\n", "player_ids_in_order = []\n", "current_turn_index = 0\n", "game_lock = threading.Lock()\n", "next_player_id = 1\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - 广播功能\n", "```python\n", "def broadcast_to_all(message):\n", "    \"\"\"向所有连接的玩家广播消息\"\"\"\n", "    with game_lock:\n", "        disconnected_players = []\n", "        for player_id, player_info in players.items():\n", "            try:\n", "                player_socket = player_info['socket']\n", "                player_socket.send(json.dumps(message).encode())\n", "            except:\n", "                disconnected_players.append(player_id)\n", "        \n", "        # 清理断开连接的玩家\n", "        for player_id in disconnected_players:\n", "            if player_id in players:\n", "                del players[player_id]\n", "            if player_id in player_ids_in_order:\n", "                player_ids_in_order.remove(player_id)\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - 回合管理\n", "```python\n", "def next_turn():\n", "    \"\"\"切换到下一个玩家的回合\"\"\"\n", "    global current_turn_index\n", "    \n", "    if not player_ids_in_order:\n", "        return\n", "    \n", "    current_turn_index = (current_turn_index + 1) % len(player_ids_in_order)\n", "    current_player_id = player_ids_in_order[current_turn_index]\n", "    \n", "    if current_player_id in players:\n", "        try:\n", "            message = {\"type\": \"YOUR_TURN\", \"message\": \"轮到你掷骰子了!\"}\n", "            players[current_player_id]['socket'].send(json.dumps(message).encode())\n", "        except:\n", "            pass\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - 处理客户端连接\n", "```python\n", "def handle_client(conn, addr):\n", "    \"\"\"处理单个客户端连接\"\"\"\n", "    global next_player_id\n", "    player_id = None\n", "    \n", "    try:\n", "        while True:\n", "            data = conn.recv(1024).decode()\n", "            if not data:\n", "                break\n", "            \n", "            message = json.loads(data)\n", "            \n", "            if message['type'] == 'JOIN':\n", "                with game_lock:\n", "                    player_id = f\"player_{next_player_id}\"\n", "                    next_player_id += 1\n", "```"]}, {"cell_type": "markdown", "id": "9f03da47", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - JOIN逻辑处理\n", "```python\n", "                    # 为新玩家分配颜色和初始位置\n", "                    color_index = len(players) % len(COLORS)\n", "                    players[player_id] = {\n", "                        'socket': conn,\n", "                        'name': message['name'],\n", "                        'position': 0,\n", "                        'color': COLORS[color_index]\n", "                    }\n", "                    player_ids_in_order.append(player_id)\n", "                    \n", "                    # 广播游戏状态\n", "                    game_state = create_game_state()\n", "                    broadcast_to_all(game_state)\n", "                    \n", "                    # 如果是第一个玩家，开始游戏\n", "                    if len(players) == 1:\n", "                        next_turn()\n", "```"]}, {"cell_type": "markdown", "id": "01a3176a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - ROLL_DICE逻辑处理\n", "```python\n", "            elif message['type'] == 'ROLL_DICE':\n", "                with game_lock:\n", "                    # 检查是否轮到该玩家\n", "                    if (player_id and player_ids_in_order and \n", "                        player_id == player_ids_in_order[current_turn_index]):\n", "                        \n", "                        dice_value = random.randint(1, 6)\n", "                        old_pos = players[player_id]['position']\n", "                        new_pos = min(old_pos + dice_value, BOARD_SIZE)\n", "                        \n", "                        # 检查蛇和梯子\n", "                        if new_pos in BOARD_LAYOUT:\n", "                            new_pos = BOARD_LAYOUT[new_pos]\n", "                        \n", "                        players[player_id]['position'] = new_pos\n", "                        \n", "                        # 检查胜利条件\n", "                        if new_pos >= BOARD_SIZE:\n", "```"]}, {"cell_type": "markdown", "id": "82e983ea", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - 游戏结束处理\n", "```python\n", "                            # 游戏结束\n", "                            winner_message = {\n", "                                \"type\": \"GAME_OVER\",\n", "                                \"winner\": players[player_id]['name']\n", "                            }\n", "                            broadcast_to_all(winner_message)\n", "                        else:\n", "                            # 广播游戏状态并切换回合\n", "                            game_state = create_game_state()\n", "                            broadcast_to_all(game_state)\n", "                            next_turn()\n", "                            \n", "    except Exception as e:\n", "        print(f\"客户端 {addr} 连接错误: {e}\")\n", "    finally:\n", "        conn.close()\n", "```"]}, {"cell_type": "markdown", "id": "aa0343cd", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - 创建游戏状态\n", "```python\n", "def create_game_state():\n", "    \"\"\"创建当前游戏状态的快照\"\"\"\n", "    players_data = {}\n", "    for player_id, player_info in players.items():\n", "        players_data[player_id] = {\n", "            'name': player_info['name'],\n", "            'position': player_info['position'],\n", "            'color': player_info['color']\n", "        }\n", "    \n", "    return {\n", "        \"type\": \"GAME_STATE\",\n", "        \"data\": {\n", "            \"players\": players_data,\n", "            \"board_layout\": BOARD_LAYOUT\n", "        }\n", "    }\n", "```"]}, {"cell_type": "markdown", "id": "78c9f7e8", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 后端核心代码 - 主服务器启动\n", "```python\n", "def start_server():\n", "    \"\"\"启动服务器\"\"\"\n", "    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)\n", "    server_socket.bind(('localhost', 8888))\n", "    server_socket.listen(5)\n", "    \n", "    print(\"蛇棋游戏服务器启动，监听端口 8888...\")\n", "    \n", "    while True:\n", "        conn, addr = server_socket.accept()\n", "        print(f\"新玩家连接: {addr}\")\n", "        client_thread = threading.Thread(target=handle_client, args=(conn, addr))\n", "        client_thread.daemon = True\n", "        client_thread.start()\n", "\n", "if __name__ == \"__main__\":\n", "    start_server()\n", "```"]}, {"cell_type": "markdown", "id": "97acb3cc", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第四步：前端组开发任务 (打造游戏的“长相”)"]}, {"cell_type": "markdown", "id": "b2ab8d55", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 任务清单 (分步实现)\n", "\n", "1.  **搭建客户端框架**: 创建一个Pygame Zero程序，包含`draw`函数和一个空的`game_state`字典。\n", "2.  **实现网络连接**: 编写`main`函数，在程序启动时提示用户输入服务器IP和自己的昵称，然后连接到服务器，并发送`JOIN`消息。\n", "3.  **实现后台接收线程**: 创建一个`network_receive_thread`线程，专门在后台循环接收服务器发来的消息，解析JSON，并根据消息`type`更新全局的`game_state`字典和`is_my_turn`标志位。\n", "4.  **实现`draw`函数**: 这是最核心的部分。在`draw`函数中，根据`game_state`字典里的数据，绘制出棋盘、所有玩家的棋子、提示信息等。**`draw`函数只负责“画”，不负责计算！**\n", "5.  **实现`on_mouse_down`函数**: 处理鼠标点击事件。检查是否轮到自己 (`is_my_turn`为`True`) 并且点击了“掷骰子”按钮。如果是，就向服务器发送`ROLL_DICE`消息。\n", "6.  **设计漂亮的UI**: 自由发挥！设计一个主题风格的棋盘背景、个性化的棋子，以及清晰明了的提示信息。\n", "\n", "**设计一个漂亮的UI，准备和后端架构师的“发动机”进行联调！**"]}, {"cell_type": "markdown", "id": "a7209918", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["#### 客户端数据结构设计\n", "**本地游戏状态 (game_state)**\n", "```python\n", "# 这个字典是客户端绘制画面的唯一数据来源，由网络线程负责更新\n", "game_state = {\n", "    \"players\": {\n", "        \"player_1\": {\"name\": \"小明\", \"color\": (255,0,0), \"position\": 15},\n", "        \"player_2\": {\"name\": \"小红\", \"color\": (0,0,255), \"position\": 8}\n", "    },\n", "    \"board_layout\": { \"3\": 15, \"16\": 6 },\n", "    \"event_message\": \"小明掷出了5点!\"\n", "}\n", "```\n", "**状态标志 (is_my_turn)**\n", "```python\n", "# 一个布尔值，决定是否显示和响应“掷骰子”按钮\n", "is_my_turn = False\n", "# 当收到服务器的\"YOUR_TURN\"消息时，将它设为True\n", "```"]}, {"cell_type": "markdown", "id": "00875cda", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 客户端框架\n", "```python\n", "import pgzero\n", "import pygame\n", "import socket\n", "import json\n", "import threading\n", "\n", "# 游戏窗口设置\n", "WIDTH = 800\n", "HEIGHT = 600\n", "\n", "# 全局变量\n", "game_state = {}\n", "is_my_turn = False\n", "client_socket = None\n", "my_player_name = \"\"\n", "event_message = \"等待连接服务器...\"\n", "```"]}, {"cell_type": "markdown", "id": "3f13c49d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 网络接收线程\n", "```python\n", "def network_receive_thread():\n", "    \"\"\"后台接收服务器消息的线程\"\"\"\n", "    global game_state, is_my_turn, event_message\n", "    \n", "    try:\n", "        while True:\n", "            data = client_socket.recv(1024).decode()\n", "            if not data:\n", "                break\n", "            \n", "            message = json.loads(data)\n", "            \n", "            if message['type'] == 'GAME_STATE':\n", "                game_state = message['data']\n", "                event_message = \"游戏状态已更新\"\n", "            elif message['type'] == 'YOUR_TURN':\n", "                is_my_turn = True\n", "                event_message = message['message']\n", "```"]}, {"cell_type": "markdown", "id": "d22fc61f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 游戏结束处理\n", "```python\n", "            elif message['type'] == 'GAME_OVER':\n", "                is_my_turn = False\n", "                event_message = f\"游戏结束！{message['winner']} 获胜！\"\n", "                \n", "    except Exception as e:\n", "        event_message = f\"网络连接错误: {e}\"\n", "\n", "def connect_to_server(server_ip, player_name):\n", "    \"\"\"连接到服务器并发送JOIN消息\"\"\"\n", "    global client_socket, my_player_name\n", "    \n", "    try:\n", "        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "        client_socket.connect((server_ip, 8888))\n", "        my_player_name = player_name\n", "        \n", "        # 发送JOIN消息\n", "        join_message = {\"type\\\": \"JOIN\", \"name\": player_name}\n", "        client_socket.send(json.dumps(join_message).encode())\n", "```"]}, {"cell_type": "markdown", "id": "5e6b4a92", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 绘制棋盘\n", "```python\n", "def draw_board():\n", "    \"\"\"绘制游戏棋盘\"\"\"\n", "    # 棋盘背景\n", "    screen.fill((240, 220, 180))\n", "    \n", "    # 绘制10x5的棋盘格子\n", "    cell_size = 60\n", "    start_x, start_y = 50, 50\n", "    \n", "    for row in range(5):\n", "        for col in range(10):\n", "            x = start_x + col * cell_size\n", "            y = start_y + row * cell_size\n", "            \n", "            # 计算格子编号 (蛇形排列)\n", "            if row % 2 == 0:\n", "                cell_num = row * 10 + col + 1\n", "            else:\n", "                cell_num = row * 10 + (9 - col) + 1\n", "```"]}, {"cell_type": "markdown", "id": "c1fef9b9", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 绘制格子和特殊元素\n", "```python\n", "            # 绘制格子\n", "            color = (255, 255, 255) if (row + col) % 2 == 0 else (220, 220, 220)\n", "            pygame.draw.rect(screen.surface, color, (x, y, cell_size, cell_size))\n", "            pygame.draw.rect(screen.surface, (0, 0, 0), (x, y, cell_size, cell_size), 2)\n", "            \n", "            # 绘制格子编号\n", "            screen.draw.text(str(cell_num), (x + 5, y + 5), color=\"black\", fontsize=20)\n", "            \n", "            # 绘制蛇和梯子\n", "            if 'board_layout' in game_state:\n", "                if cell_num in game_state['board_layout']:\n", "                    target = game_state['board_layout'][cell_num]\n", "                    if target > cell_num:\n", "                        # 梯子 (绿色)\n", "                        pygame.draw.circle(screen.surface, (0, 255, 0), (x + 30, y + 30), 15)\n", "                        screen.draw.text(\"↑\", (x + 25, y + 20), color=\"white\", fontsize=20)\n", "                    else:\n", "                        # 蛇 (红色)\n", "                        pygame.draw.circle(screen.surface, (255, 0, 0), (x + 30, y + 30), 15)\n", "                        screen.draw.text(\"↓\", (x + 25, y + 20), color=\"white\", fontsize=20)\n", "```"]}, {"cell_type": "markdown", "id": "08ddce07", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 绘制玩家棋子\n", "```python\n", "def draw_players():\n", "    \"\"\"绘制所有玩家的棋子\"\"\"\n", "    if 'players' not in game_state:\n", "        return\n", "    \n", "    cell_size = 60\n", "    start_x, start_y = 50, 50\n", "    \n", "    for i, (player_id, player_info) in enumerate(game_state['players'].items()):\n", "        position = player_info['position']\n", "        if position <= 0:\n", "            continue\n", "        \n", "        # 计算棋子在棋盘上的坐标\n", "        row = (position - 1) // 10\n", "        if row % 2 == 0:\n", "            col = (position - 1) % 10\n", "        else:\n", "            col = 9 - ((position - 1) % 10)\n", "```"]}, {"cell_type": "markdown", "id": "90ac1d06", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 棋子位置和UI元素\n", "```python\n", "        x = start_x + col * cell_size + 10 + (i * 15)  # 多个玩家错开显示\n", "        y = start_y + row * cell_size + 10\n", "        \n", "        # 绘制棋子\n", "        color = player_info['color']\n", "        pygame.draw.circle(screen.surface, color, (x, y), 12)\n", "        pygame.draw.circle(screen.surface, (0, 0, 0), (x, y), 12, 2)\n", "        \n", "        # 绘制玩家名称\n", "        screen.draw.text(player_info['name'], (x - 20, y + 15), \n", "                        color=\"black\", fontsize=16)\n", "\n", "def draw_ui():\n", "    \"\"\"绘制用户界面元素\"\"\"\n", "    # 绘制事件消息\n", "    screen.draw.text(event_message, (50, 400), color=\"blue\", fontsize=24)\n", "    \n", "    # 绘制掷骰子按钮\n", "    if is_my_turn:\n", "        button_rect = pygame.Rect(650, 450, 120, 50)\n", "        pygame.draw.rect(screen.surface, (0, 255, 0), button_rect)\n", "        screen.draw.text(\"掷骰子\", (670, 465), color=\"white\", fontsize=20)\n", "```"]}, {"cell_type": "markdown", "id": "3de26054", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 主绘制函数\n", "```python\n", "def draw():\n", "    \"\"\"Pygame Zero主绘制函数\"\"\"\n", "    draw_board()\n", "    draw_players()\n", "    draw_ui()\n", "\n", "def on_mouse_down(pos):\n", "    \"\"\"处理鼠标点击事件\"\"\"\n", "    global is_my_turn\n", "    \n", "    # 检查是否点击了掷骰子按钮\n", "    if is_my_turn and 650 <= pos[0] <= 770 and 450 <= pos[1] <= 500:\n", "        # 发送ROLL_DICE消息\n", "        roll_message = {\"type\": \"ROLL_DICE\"}\n", "        client_socket.send(json.dumps(roll_message).encode())\n", "        is_my_turn = False  # 防止重复点击\n", "```"]}, {"cell_type": "markdown", "id": "326f6409", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 主程序启动\n", "```python\n", "def main():\n", "    \"\"\"主程序入口\"\"\"\n", "    global event_message\n", "    \n", "    # 获取用户输入\n", "    server_ip = input(\"请输入服务器IP地址 (默认localhost): \") or \"localhost\"\n", "    player_name = input(\"请输入你的昵称: \")\n", "    \n", "    # 连接服务器\n", "    try:\n", "        connect_to_server(server_ip, player_name)\n", "        event_message = f\"已连接到服务器，欢迎 {player_name}!\"\n", "        \n", "        # 启动网络接收线程\n", "        receive_thread = threading.Thread(target=network_receive_thread)\n", "        receive_thread.daemon = True\n", "        receive_thread.start()\n", "```"]}, {"cell_type": "markdown", "id": "33b648ad", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 前端核心代码 - 启动游戏循环\n", "```python\n", "        # 启动Pygame Zero游戏循环\n", "        import pgzrun\n", "        pgzrun.go()\n", "        \n", "    except Exception as e:\n", "        print(f\"连接服务器失败: {e}\")\n", "        input(\"按回车键退出...\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "```\n", "\n", "**完整的前端代码已经展示完毕！现在前端组可以将这些代码片段组合成一个完整的`client_pgzero.py`文件。**"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第五步：联调与测试\n", "\n", "这是最激动人心的环节！当前端组和后端组都完成了各自的开发后，就可以进行联调了。\n", "\n", "1.  后端组先启动`server.py`。\n", "2.  前端组的同学启动`client_pgzero.py`，输入服务器的IP地址和自己的昵称。\n", "3.  观察两边的控制台输出和游戏窗口，检查功能是否按预期工作。\n", "4.  如果遇到问题，根据“暗号”（网络协议）来排查是哪一方的逻辑出了问题。\n", "\n", "**祝贺大家！通过团队协作，你们成功打造了一个功能完整的多人联机游戏！**"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}