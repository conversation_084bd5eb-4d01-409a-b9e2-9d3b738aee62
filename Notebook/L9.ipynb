{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# Python数据库编程：使用SQLAlchemy实现数据持久化"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！今天我们将学习一项至关重要的技能：如何让我们的程序“拥有记忆”。\n", "\n", "想象一下，如果你创建了一个很酷的心情记录应用，但每次关闭程序后所有数据都消失了，那会多么令人失望。数据库就像是一个永不忘记的笔记本，可以帮助我们安全、永久地保存所有重要信息。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 为什么我们需要数据库？"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["还记得我们上节课创建的心情记录API吗？它有一个很大的问题：所有数据都存储在内存（一个Python列表`mood_items`）中。这就像把你的笔记**写在水面上**一样——关闭程序或重启电脑后，所有数据都会消失不见！\n", "\n", "这时候，我们就需要**数据持久化 (Data Persistence)**。数据持久化就像是把笔记**写在纸上**，确保即使电脑关机，我们的数据也不会丢失。**数据库**就是专门用来实现数据持久化的强大工具。\n", "\n", "**我们的目标**: 改造上节课的心情记录API，让它能将数据永久保存在数据库中。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 如何选择你的第一个数据库：SQLite"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["数据库有很多种，但对于初学者和小型项目，**SQLite**是完美的选择。\n", "\n", "### SQLite的特点 (推荐给初学者)\n", "- **就是一个文件**: 整个数据库都保存在一个单独的文件里（例如 `moods.db`）。拷贝项目就是拷贝数据库，非常方便。\n", "- **零配置**: 不需要安装、启动或管理一个独立的服务器程序。\n", "- **轻量快捷**: 非常适合学习、小型项目和移动应用。\n", "\n", "今天，我们将使用SQLite，它能让你专注于学习Python和数据库操作的核心逻辑，而不会被复杂的环境配置问题所困扰。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 与数据库“对话”的翻译官：ORM与SQLAlchemy"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["数据库有它自己的语言，叫做**SQL (Structured Query Language)**。我们可以用SQL告诉数据库“增、删、改、查”数据。\n", "\n", "但是，直接在Python里手写SQL语句很繁琐且容易出错。这时，我们需要一位“翻译官”——**ORM (Object-Relational Mapping, 对象关系映射)**。\n", "\n", "ORM工具能自动将我们熟悉的Python对象（比如一个`Mood`类）“翻译”成数据库能听懂的SQL语言。我们今天要学习的 **SQLAlchemy** 就是Python世界中最流行、最强大的ORM工具。"]}, {"cell_type": "markdown", "id": "5ee6e05e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 数据库的核心概念：表、字段与主键\n", "\n", "在我们用代码创建数据库之前，我们先来理解一下数据库的“内部构造”，这和我们熟悉的Excel表格非常相似。\n", "\n", "想象一个Excel文件 `我们的应用.xlsx`，这个文件就是我们的**数据库 (Database)**。\n", "\n", "1.  **表 (Table)**\n", "    -   **概念**: 数据库中的一个“表”就像是Excel文件里的一个**工作表 (Sheet)**。我们通常为每一种类型的数据创建一个表。例如，我们可以有一个“心情记录表”，一个“用户表”等等。\n", "    -   **对应代码**: 在SQLAlchemy中，我们通过 `__tablename__ = \"moods\"` 来给我们的模型指定它对应的表名。\n", "\n", "2.  **列 / 字段 (Column / Field)**\n", "    -   **概念**: 表中的“列”就像是Excel工作表中的**列标题**，它定义了每一条记录应该包含哪些信息。例如，在“心情记录表”中，我们可以有`ID`, `用户名`, `心情`, `留言`这几列。\n", "    -   **对应代码**: 我们用 `user = Column(String)` 来定义一个名为 `user` 的列，并指定它的**数据类型**为字符串(`String`)。指定正确的数据类型很重要，它能保证数据存储的正确性和效率（比如数字就用`Integer`，文本用`String`）。"]}, {"cell_type": "markdown", "id": "fd16a8f5", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["3.  **主键 (Primary Key)**\n", "    -   **概念**: 这是最重要的概念之一。**主键是表中每一行记录的唯一身份证**。就像每个学生都有一个独一无二的学号一样，数据库中的每一条记录也必须有一个不会重复的值来标识自己。这个“学号”列就是主键。\n", "    -   **为什么需要主键？**: 当我们想精确地更新或删除某一条记录时（比如“删除ID为5的心情记录”），主键提供了唯一的定位方式。\n", "    -   **对应代码**: 我们通过 `id = Column(Integer, primary_key=True)` 来告诉数据库，`id` 这一列是主键。它通常是一个自增的整数。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 开始使用SQLAlchemy：三步走"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第一步：安装必要的库\n", "在使用SQLAlchemy之前，我们需要先安装它。对于与FastAPI的集成，我们还需要安装`databases`库来提供异步支持（尽管我们今天主要用同步方式）。\n", "\n", "```bash\n", "pip install sqlalchemy\n", "pip install databases[sqlite] # 安装databases并指定支持sqlite\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第二步：创建数据库连接 (Engine)\n", "首先，我们需要告诉SQLAlchemy我们的数据库在哪里、是什么类型。这通过一个特殊的**数据库连接URL**来完成。\n", "\n", "**对于SQLite，URL非常简单：**\n", "```python\n", "# database.py\n", "from sqlalchemy import create_engine\n", "\n", "# 数据库文件的路径。./moods.db 表示在当前目录下创建一个名为 moods.db 的文件\n", "DATABASE_URL = \"sqlite:///./moods.db\"\n", "\n", "# 创建一个数据库“引擎”(Engine)，它是SQLAlchemy与数据库沟通的核心接口\n", "engine = create_engine(\n", "    DATABASE_URL, \n", "    # connect_args 是SQLite特有的配置，用于允许多线程共享连接\n", "    connect_args={\"check_same_thread\": False}\n", ")\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第三步：定义数据模型 (Model)\n", "我们需要用Python的类来描述我们想在数据库里创建的表结构。这个类需要继承自SQLAlchemy提供的`Base`类。\n", "\n", "**在`database.py`文件下方继续添加：**\n", "```python\n", "# database.py\n", "from sqlalchemy.ext.declarative import declarative_base\n", "\n", "# 创建一个Base类，我们之后所有的模型都要继承自它\n", "Base = declarative_base()\n", "```"]}, {"cell_type": "markdown", "id": "9d498dfe", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**然后，新建一个`models.py`文件来定义我们的`Mood`模型：**\n", "```python\n", "# models.py\n", "from sqlalchemy import Column, Integer, String, DateTime\n", "from .database import Base # 从database.py导入Base\n", "import datetime\n", "\n", "class Mood(Base):\n", "    __tablename__ = \"moods\" # 这个模型对应的数据库表名\n", "\n", "    # 定义表的列(字段)\n", "    id = Column(Integer, primary_key=True, index=True) # id, 整数, 主键, 索引\n", "    user = Column(String, index=True) # user, 字符串, 索引\n", "    mood = Column(String)\n", "    message = Column(String, nullable=True) # 可选字段\n", "    created_at = Column(DateTime, default=datetime.datetime.now) # 创建时间\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 让模型生效：创建数据库表\n", "定义好模型后，我们还需要一条命令来让SQLAlchemy检查数据库，如果`moods`表不存在，就根据我们的模型创建它。\n", "\n", "**新建一个`main.py`文件，并加入以下代码：**\n", "```python\n", "# main.py\n", "from . import models, database\n", "\n", "# 这行代码会根据我们定义的模型，在数据库中创建所有不存在的表\n", "models.Base.metadata.create_all(bind=database.engine)\n", "```\n", "**现在，运行`main.py`文件，你就会在同目录下看到一个`moods.db`文件被创建出来了！**"]}, {"cell_type": "markdown", "id": "14600bc0", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 看见你的数据：使用数据库图形化工具\n", "\n", "我们已经成功创建了 `moods.db` 文件，但它是一个二进制文件，我们无法直接用记事本打开。那么，如何直观地查看和管理里面的数据呢？答案是使用数据库图形化界面（GUI）工具。\n", "\n", "**推荐工具**:\n", "-   **[DB Browser for SQLite](https://sqlitebrowser.org/dl/)**: 极度轻量，专门为SQLite设计，简单易用，非常适合初学者。\n", "-   **[DBeaver](https://dbeaver.io/download/)**: 功能更强大的通用数据库工具，支持SQLite、MySQL、PostgreSQL等几乎所有主流数据库，推荐未来有志于深入学习的同学安装。"]}, {"cell_type": "markdown", "id": "ab5b4a9a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**实践任务：亲眼见证CRUD**\n", "\n", "1.  **下载并安装** 上述任一工具。\n", "2.  **连接数据库**:\n", "    -   在工具中选择“连接数据库”或“新建连接”。\n", "    -   选择数据库类型为 **SQLite**。\n", "    -   浏览并选择我们项目目录下的 `moods.db` 文件。\n", "3.  **探索你的数据库**:\n", "    -   连接成功后，你应该能在左侧的导航栏看到我们创建的 `moods` 表。\n", "    -   点击 `moods` 表，切换到“数据”或“Browse Data”选项卡。现在表里应该是空的。\n", "4.  **见证奇迹的时刻**:\n", "    -   保持GUI工具打开。\n", "    -   启动你的FastAPI应用，并通过API文档（或`requests`脚本）发送一个`POST /moods`请求，创建一条新的心情记录。\n", "    -   回到GUI工具，**点击刷新按钮**。你会惊喜地发现，一条新的记录赫然出现在表中！\n", "\n", "这个过程能让你对“API后端 -> SQLAlchemy -> 数据库文件”的整个数据流有一个极其直观的认识。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 管理数据库对话：会话(Session)与依赖注入"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["我们不能直接用`Engine`来操作数据，而是需要通过一个“**会话(Session)**”来进行。会话就像是我们与数据库之间的一次对话通道，它管理着所有的操作。\n", "\n", "在FastAPI中，管理会话的最佳方式是使用“**依赖注入(Dependency Injection)**”。\n", "\n", "**简单来说**：我们创建一个`get_db()`函数，它负责“开始一次对话”(创建Session)并在对话结束后“挂断电话”(关闭Session)。然后，我们告诉FastAPI：“我的每个API端点都需要这个`get_db`函数提供的对话通道”。\n", "\n", "**在`database.py`中添加如下代码：**\n", "```python\n", "from sqlalchemy.orm import sessionmaker\n", "\n", "# 创建一个会话“工厂”，它知道如何连接到我们的数据库引擎\n", "SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\n", "\n", "# 这就是我们的依赖项\n", "def get_db():\n", "    db = SessionLocal() # 创建一个会话实例\n", "    try:\n", "        yield db # 将会话实例提供给API函数使用\n", "    finally:\n", "        db.close() # 确保无论如何，会话最终都会被关闭\n", "```"]}, {"cell_type": "markdown", "id": "8a637c80", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 深度解析：为什么需要会话 (Session)？它和“事务”有什么关系？\n", "\n", "`Engine`（引擎）建立了到数据库的连接，但为什么我们不直接用它操作数据，反而要引入一个更复杂的`Session`（会话）呢？\n", "\n", "**核心答案：为了保证操作的原子性，也就是“事务 (Transaction)”。**\n", "\n", "**一个经典的银行转账例子：**\n", "\n", "想象一下A要给B转100元。这个操作需要分两步：\n", "1.  从A的账户扣除100元。\n", "2.  给B的账户增加100元。\n", "\n", "如果在执行完第1步后，程序突然崩溃了，会发生什么？A的钱被扣了，但B没收到。钱凭空消失了！这是绝对不能接受的。"]}, {"cell_type": "markdown", "id": "ead86edf", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["\n", "**事务（Transaction）就是来解决这个问题的。** 它将一组操作捆绑成一个“**工作单元**”，并保证这个单元内的所有操作**要么全部成功，要么全部失败**（回滚到操作之前的状态）。\n", "\n", "**`Session` 就是这个工作单元的管理者。**\n", "\n", "它就像一个临时的“购物车”或“草稿区”：\n", "\n", "-   `db.add(db_mood)`: 这并不是立刻把数据写入数据库。它更像是说：“你好，Session管理员，请把这个`db_mood`对象**放入我的购物车**，我准备之后要创建它。”\n", "\n", "-   `db.delete(mood_obj)`: “管理员，请把这个对象从购物车里拿走。”\n", "\n", "-   `db.commit()`: 这是最关键的一步，它相当于说：“**好了，购物车里的东西我确认无误，现在请结账！**” `Session`会把购物车里所有的更改（增、删、改）作为一个整体事务，一次性提交给数据库，让它们永久生效。如果在提交过程中任何一步出错，整个事务都会被**回滚 (Rollback)**，数据库将回到`commit`之前的状态，保证了数据的一致性。"]}, {"cell_type": "markdown", "id": "593b10fc", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**再看我们的`get_db`函数：**\n", "\n", "```python\n", "def get_db():\n", "    db = SessionLocal() # 开始一次对话（打开一个“购物车”）\n", "    try:\n", "        yield db # 把这个“购物车”交给API函数去操作\n", "    finally:\n", "        db.close() # 无论API成功还是失败，最后都必须关闭对话（清空购物车，释放资源）\n", "```\n", "\n", "这个结构确保了每一次API请求都有一个独立的、干净的会话来处理，请求结束后资源会被妥善释放，这是一种非常健壮和高效的管理方式。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 改造心情记录API：实现增删改查(CRUD)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 1. 创建 (Create)\n", "我们来重写`POST /moods`端点，让它把数据存入数据库。\n", "**在`main.py`中：**\n", "```python\n", "from fastapi import Depends\n", "from sqlalchemy.orm import Session\n", "# 导入我们之前写的模型和Pydantic数据类\n", "from . import models, schemas, database\n", "\n", "@app.post(\"/moods\", response_model=schemas.Mood)\n", "def create_mood(mood_data: schemas.MoodCreate, db: Session = Depends(database.get_db)):\n", "    # 1. 根据Pydantic模型创建SQLAlchemy模型实例\n", "    db_mood = models.Mood(**mood_data.dict())\n", "    # 2. 将这个实例添加到会话中（准备写入）\n", "    db.add(db_mood)\n", "    # 3. 提交会话，将更改真正写入数据库\n", "    db.commit()\n", "    # 4. 刷新实例，从数据库获取最新的数据（比如自动生成的id）\n", "    db.refresh(db_mood)\n", "    return db_mood\n", "```\n", "*注意：这里假设你已在`schemas.py`中定义了Pydantic模型`Mood`和`MoodCreate`，它们与L8中的类似。*"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 2. 读取 (Read)\n", "完成获取所有心情记录和获取单个记录的API。\n", "```python\n", "# 获取所有记录\n", "@app.get(\"/moods\", response_model=List[schemas.Mood])\n", "def get_moods(db: Session = Depends(database.get_db)):\n", "    # 使用db.query()来查询Mood模型对应的表，.all()表示获取所有结果\n", "    moods = db.query(<PERSON>.Mood).all()\n", "    return moods\n", "\n", "# 获取单个记录\n", "@app.get(\"/moods/{mood_id}\", response_model=schemas.Mood)\n", "def get_mood(mood_id: int, db: Session = Depends(database.get_db)):\n", "    # .filter()方法相当于SQL中的WHERE子句，.first()表示只取第一个结果\n", "    mood = db.query(models.Mood).filter(models.Mood.id == mood_id).first()\n", "    if mood is None:\n", "        raise HTTPException(status_code=404, detail=\"心情记录未找到\")\n", "    return mood\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 3. 更新 (Update)\n", "更新和删除操作都遵循“先查找到记录，再进行操作”的模式。\n", "```python\n", "# 更新记录\n", "@app.put(\"/moods/{mood_id}\", response_model=schemas.Mood)\n", "def update_mood(mood_id: int, mood_data: schemas.MoodCreate, \n", "                db: Session = Depends(database.get_db)):\n", "    # 1. 先查找到要更新的记录\n", "    mood = db.query(models.Mood).filter(models.Mood.id == mood_id).first()\n", "    if mood is None:\n", "        raise HTTPException(status_code=404, detail=\"心情记录未找到\")\n", "    \n", "    # 2. 更新记录的属性\n", "    for key, value in mood_data.dict().items():\n", "        setattr(mood, key, value)\n", "    \n", "    # 3. 提交更改\n", "    db.commit()\n", "    db.refresh(mood)\n", "    return mood\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 4. 删除 (Delete)\n", "删除操作同样遵循\"先查找，再删除\"的模式。\n", "```python\n", "# 删除记录\n", "@app.delete(\"/moods/{mood_id}\")\n", "def delete_mood(mood_id: int, db: Session = Depends(database.get_db)):\n", "    # 1. 先查找到要删除的记录\n", "    mood = db.query(models.Mood).filter(models.Mood.id == mood_id).first()\n", "    if mood is None:\n", "        raise HTTPException(status_code=404, detail=\"心情记录未找到\")\n", "    \n", "    # 2. 删除实例\n", "    db.delete(mood)\n", "    \n", "    # 3. 提交更改\n", "    db.commit()\n", "    \n", "    return {\"message\": \"心情记录已删除\"}\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 完整的项目结构"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 项目文件组织\n", "让我们回顾一下完整的项目结构，确保所有文件都在正确的位置：\n", "\n", "```\n", "mood_api/\n", "├── main.py          # FastAPI应用主文件\n", "├── database.py      # 数据库连接和配置\n", "├── models.py        # SQLAlchemy数据模型\n", "├── schemas.py       # Pydantic数据验证模型\n", "└── moods.db         # SQLite数据库文件（运行后自动生成）\n", "```\n", "\n", "这种模块化的组织方式让代码更清晰、更易维护。每个文件都有明确的职责分工。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### schemas.py - 数据验证模型\n", "别忘了创建Pydantic模型来验证API输入输出：\n", "```python\n", "# schemas.py\n", "from pydantic import BaseModel\n", "from datetime import datetime\n", "from typing import Optional\n", "\n", "class MoodBase(BaseModel):\n", "    user: str\n", "    mood: str\n", "    message: Optional[str] = None\n", "\n", "class MoodCreate(MoodBase):\n", "    pass  # 创建时不需要额外字段\n", "\n", "class Mood(MoodBase):\n", "    id: int\n", "    created_at: datetime\n", "    \n", "    class Config:\n", "        orm_mode = True  # 允许从SQLAlchemy模型创建\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 总结与展望"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 今天我们学到了什么？\n", "\n", "1. **数据持久化的重要性**：从内存存储升级到数据库存储\n", "2. **SQLite数据库**：\n", "   - 轻量级、零配置的数据库选择\n", "   - 适合学习和小型项目\n", "3. **SQLAlchemy ORM**：\n", "   - 用Python类定义数据库表结构\n", "   - 自动处理SQL语句生成\n", "   - 提供优雅的数据库操作接口\n", "4. **数据库核心概念**：\n", "   - 表(Table)、列(Column)、主键(Primary Key)\n", "   - 会话(Session)与事务(Transaction)\n", "   - 依赖注入在资源管理中的应用"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 关键概念回顾\n", "\n", "**ORM的魔法**：\n", "- 将Python对象映射为数据库表\n", "- 自动生成SQL语句，减少手写SQL的错误\n", "- 提供跨数据库的兼容性\n", "\n", "**Session的重要性**：\n", "- 管理数据库事务，确保数据一致性\n", "- 提供\"购物车\"式的操作模式\n", "- 通过依赖注入实现优雅的资源管理\n", "\n", "**CRUD操作模式**：\n", "- Create: `db.add()` + `db.commit()`\n", "- Read: `db.query().filter().first/all()`\n", "- Update: 修改属性 + `db.commit()`\n", "- Delete: `db.delete()` + `db.commit()`"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 下节课预告\n", "\n", "- **高级查询技巧**：复杂的数据库查询和过滤\n", "- **数据库关系**：一对多、多对多关系建模\n", "- **性能优化**：索引、查询优化、连接池\n", "- **数据迁移**：如何安全地修改数据库结构\n", "\n", "### 课后作业\n", "\n", "1. **完善心情记录API**：实现完整的CRUD功能\n", "2. **添加数据验证**：为心情字段添加枚举限制（如：开心、难过、愤怒等）\n", "3. **实现搜索功能**：按用户名或关键词搜索心情记录\n", "4. **尝试其他数据库**：将SQLite替换为PostgreSQL或MySQL\n", "5. **数据可视化**：使用数据库GUI工具探索你的数据"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}