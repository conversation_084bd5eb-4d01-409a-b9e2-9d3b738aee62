{"cells": [{"cell_type": "markdown", "id": "009a33ea", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["\n", "<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# 综合项目：Python天气查询与智能建议工具"]}, {"cell_type": "markdown", "id": "21b15adf", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！今天我们将化身为应用开发者，把之前学到的所有知识融会贯通，从零开始构建一个完整、实用的天气查询小工具。它不仅能查询天气，还能像个贴心的小助手一样，给我们提供穿衣建议！"]}, {"cell_type": "markdown", "id": "65114eac", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 项目目标：打造一个智能天气助手\n", "\n", "- **功能1：实时天气查询**：输入城市名，立即获取该城市的实时天气情况。\n", "- **功能2：智能穿衣建议**：根据获取的温度，自动给出合理的穿衣建议。\n", "- **功能3：多城市速览**：能一次性查询多个我们关心的城市的天气。\n", "- **功能4：预报可视化**：将未来几天的气温变化用图表直观地展示出来。\n", "- **终极功能：天气机器人**：将天气预报自动推送到我们的手机上！"]}, {"cell_type": "markdown", "id": "2fdd298f", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 项目核心：`goweather` 天气API介绍\n", "\n", "我们将使用 `goweather.xyz` 提供的免费天气API。它非常简单，无需注册，是练习的绝佳选择。"]}, {"cell_type": "markdown", "id": "c7adf543", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### API 使用方法\n", "\n", "**请求地址**: `https://goweather.xyz/weather/<城市拼音>`\n", "\n", "例如，查询北京的天气，我们只需要访问：`https://goweather.xyz/weather/beijing`\n", "\n", "**返回的JSON格式示例:**\n", "```json\n", "{\n", "  \"temperature\": \"29 °C\",\n", "  \"wind\": \"5 km/h\",\n", "  \"description\": \"Clear\",\n", "  \"forecast\": [\n", "    { \"day\": \"1\", \"temperature\": \"34 °C\", \"wind\": \"11 km/h\" },\n", "    { \"day\": \"2\", \"temperature\": \"+26 °C\", \"wind\": \"12 km/h\" }\n", "  ]\n", "}\n", "```"]}, {"cell_type": "markdown", "id": "c7563217", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 步骤一：获取并解析天气数据\n", "\n", "这是我们项目的第一步，也是最核心的一步：从用户那里获取城市名，然后向API发送请求，拿到天气数据。"]}, {"cell_type": "code", "execution_count": 1, "id": "f5f19782", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功获取天气数据:\n", "{\n", "    \"description\": \"Partly cloudy\",\n", "    \"forecast\": [\n", "        {\n", "            \"day\": \"1\",\n", "            \"temperature\": \"+36 °C\",\n", "            \"wind\": \"17 km/h\"\n", "        },\n", "        {\n", "            \"day\": \"2\",\n", "            \"temperature\": \"+32 °C\",\n", "            \"wind\": \"11 km/h\"\n", "        },\n", "        {\n", "            \"day\": \"3\",\n", "            \"temperature\": \" °C\",\n", "            \"wind\": \"9 km/h\"\n", "        }\n", "    ],\n", "    \"temperature\": \"+25 °C\",\n", "    \"wind\": \"11 km/h\"\n", "}\n"]}], "source": ["import requests\n", "import json\n", "\n", "# 1. 使用 input() 函数从用户那里获取城市名（拼音）\n", "city = input(\"请输入城市拼音 (例如 beijing): \")\n", "\n", "# 2. 使用 f-string 构建完整的API请求URL\n", "url = f\"https://goweather.xyz/weather/{city}\"\n", "\n", "try:\n", "    # 3. 发送GET请求\n", "    response = requests.get(url)\n", "    # 这是一个非常好的习惯：如果请求失败(例如城市不存在导致404)，程序会在这里抛出异常\n", "    response.raise_for_status()\n", "\n", "    # 4. 如果请求成功，就解析JSON数据\n", "    weather_data = response.json()\n", "    print(\"成功获取天气数据:\")\n", "    print(json.dumps(weather_data, indent=4, ensure_ascii=False, sort_keys=True))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    # 捕获所有可能的网络请求错误\n", "    print(f\"获取天气失败: {e}\")\n", "    # 在失败时创建一个空字典，保证后续代码不会因变量不存在而报错\n", "    weather_data = {} "]}, {"cell_type": "markdown", "id": "507c0e95", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "import requests\n", "\n", "city = input(\"请输入城市拼音: \")\n", "url = f\"https://goweather.xyz/weather/{city}\"\n", "\n", "try:\n", "    response = requests.get(url)\n", "    response.raise_for_status()\n", "    weather_data = response.json()\n", "    print(weather_data)\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"获取天气失败: {e}\")\n", "    weather_data = {}\n", "```"]}, {"cell_type": "markdown", "id": "7008afb5", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 步骤二：数据清洗 - 从文本到数字\n", "API返回的温度（如 `\"29 °C\"`）是字符串，我们无法直接用它来做数学比较（比如 `if temp > 25`）。我们需要一个“数据清洗”的步骤，从中提取出纯数字。"]}, {"cell_type": "markdown", "id": "914bc626", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 如何从 `\"29 °C\"` 中得到数字 `29`?\n", "\n", "这是一个经典的字符串处理问题，我们可以分两步解决：\n", "1.  **分割**: 使用字符串的 `.split()` 方法。它默认会按空格分割字符串，并返回一个列表。\n", "    - `'29 °C'.split()` 会得到 `['29', '°C']`。\n", "2.  **提取和转换**: 我们只需要列表中的第一个元素 `[0]`，也就是字符串 `'29'`。然后，再用 `int()` 函数将这个字符串转换成我们需要的整数 `29`。"]}, {"cell_type": "code", "execution_count": 2, "id": "8c3a4fae", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理后的温度: 25\n", "处理后的风速: 11\n"]}], "source": ["# 确保上一步获取数据成功，weather_data不为空\n", "if weather_data:\n", "    # 使用 .get() 方法安全地获取数据。如果'temperature'键不存在，它会返回默认值'0 °C'，避免程序出错\n", "    temp_str = weather_data.get('temperature', '0 °C')\n", "    wind_str = weather_data.get('wind', '0 km/h')\n", "    description = weather_data.get('description', '未知')\n", "    \n", "    try:\n", "        # --- 数据清洗 --- \n", "        # 对温度字符串进行处理\n", "        temp_value = int(temp_str.split()[0])\n", "        # 对风速字符串进行处理\n", "        wind_value = int(wind_str.split()[0])\n", "\n", "        print(f\"处理后的温度: {temp_value}\")\n", "        print(f\"处理后的风速: {wind_value}\")\n", "\n", "    except (ValueError, IndexError):\n", "        # 如果split或int转换失败（例如返回的数据格式异常），给出提示\n", "        print(\"无法从返回数据中解析出有效的温度或风速数值。\")\n", "        # 提供默认值以防后续代码报错\n", "        temp_value, wind_value = 0, 0 \n", "else:\n", "    print(\"没有天气数据可供处理。\")"]}, {"cell_type": "markdown", "id": "6903b214", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "if weather_data:\n", "    # 使用.get()安全地获取数据\n", "    temp_str = weather_data.get('temperature', '0 °C')\n", "    wind_str = weather_data.get('wind', '0 km/h')\n", "    \n", "    try:\n", "        # 分割字符串，取第一部分，然后转成整数\n", "        temp_value = int(temp_str.split()[0])\n", "        wind_value = int(wind_str.split()[0])\n", "\n", "        print(f\"处理后的温度: {temp_value}\")\n", "        print(f\"处理后的风速: {wind_value}\")\n", "    except:\n", "        print(\"数据解析失败\")\n", "        temp_value, wind_value = 0, 0\n", "```"]}, {"cell_type": "markdown", "id": "9862fce2", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 步骤三：制作智能穿衣建议\n", "现在我们有了干净的数字，可以用 `if/elif/else` 语句来根据温度提供智能建议了。作为一名优秀的工程师，我们会把这个逻辑封装成一个独立的函数，方便以后复用。"]}, {"cell_type": "code", "execution_count": 3, "id": "ae928f5d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 天气报告 ---\n", "城市: <PERSON><PERSON><PERSON><PERSON>\n", "天气: Partly cloudy\n", "温度: +25 °C\n", "风速: 11 km/h\n", "穿衣建议: 天气炎热，建议穿T恤、短裤，别忘了防晒！\n"]}], "source": ["# 定义一个专门提供穿衣建议的函数\n", "def get_clothing_advice(temp, wind):\n", "    \"\"\"根据温度和风速返回穿衣建议的字符串\"\"\"\n", "    # 1. 首先根据温度确定基础建议\n", "    if temp < 5:\n", "        base_advice = \"天气寒冷，建议穿厚羽绒服、毛衣、保暖内衣。\"\n", "    elif 5 <= temp < 15:\n", "        base_advice = \"天气凉爽，建议穿外套、长袖衫、长裤。\"\n", "    elif 15 <= temp < 25:\n", "        base_advice = \"天气温和，建议穿薄外套或针织衫、T恤。\"\n", "    else:  # temp >= 25\n", "        base_advice = \"天气炎热，建议穿T恤、短裤，别忘了防晒！\"\n", "    \n", "    # 2. 接着根据风速（单位：千米/小时）增加额外的防风建议\n", "    #    这里我们定义一些风速阈值，可以根据实际需求调整\n", "    #    - 18 km/h (约等于3级风) 以下，风力较小\n", "    #    - 18-36 km/h (约等于4-5级风)，有明显风感\n", "    #    - 36 km/h (约等于6级风) 以上，风力很大\n", "    wind_advice = \"\"\n", "    if wind >= 36:\n", "        wind_advice = \"风力强劲，务必穿着防风外套，并注意保护头部和颈部，例如戴帽子和围巾。\"\n", "    elif wind >= 18:\n", "        wind_advice = \"有明显风感，建议选择一件防风的外套。\"\n", " \n", "    # 3. 组合最终建议，并使用 strip() 清理可能多余的空格\n", "    final_advice = (base_advice + \" \" + wind_advice).strip()\n", " \n", "    return final_advice\n", "\n", "# 确保前面的变量已成功创建\n", "if 'temp_value' in locals():\n", "    # 调用函数获取建议\n", "    advice = get_clothing_advice(temp_value, wind_value)\n", "    \n", "    # 格式化打印完整的天气报告\n", "    print(f\"\\n--- 天气报告 ---\")\n", "    print(f\"城市: {city.capitalize()}\") # .capitalize()让首字母大写\n", "    print(f\"天气: {description}\")\n", "    print(f\"温度: {temp_str}\")\n", "    print(f\"风速: {wind_str}\")\n", "    print(f\"穿衣建议: {advice}\")\n", "else:\n", "    print(\"无法生成穿衣建议，因为前面的步骤失败了。\")"]}, {"cell_type": "markdown", "id": "6bc36c32", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "def get_clothing_advice(temp, wind):\n", "    # 1. 首先根据温度确定基础建议\n", "    if temp < 5:\n", "        base_advice = \"天气寒冷，建议穿厚羽绒服、毛衣、保暖内衣。\"\n", "    elif 5 <= temp < 15:\n", "        base_advice = \"天气凉爽，建议穿外套、长袖衫、长裤。\"\n", "    elif 15 <= temp < 25:\n", "        base_advice = \"天气温和，建议穿薄外套或针织衫、T恤。\"\n", "    else:  # temp >= 25\n", "        base_advice = \"天气炎热，建议穿T恤、短裤，别忘了防晒！\"\n", "\n", "    # 2. 接着根据风速（单位：千米/小时）增加额外的防风建议\n", "    #    这里我们定义一些风速阈值，可以根据实际需求调整\n", "    #    - 18 km/h (约等于3级风) 以下，风力较小\n", "    #    - 18-36 km/h (约等于4-5级风)，有明显风感\n", "    #    - 36 km/h (约等于6级风) 以上，风力很大\n", "    wind_advice = \"\"\n", "    if wind >= 36:\n", "        wind_advice = \"风力强劲，务必穿着防风外套，并注意保护头部和颈部，例如戴帽子和围巾。\"\n", "    elif wind >= 18:\n", "        wind_advice = \"有明显风感，建议选择一件防风的外套。\"\n", " \n", "    # 3. 组合最终建议，并使用 strip() 清理可能多余的空格\n", "    final_advice = (base_advice + \" \" + wind_advice).strip()\n", " \n", "    return final_advice\n", "\n", "# 调用函数\n", "advice = get_clothing_advice(temp_value, wind_value)\n", "print(f\"穿衣建议: {advice}\")\n", "```"]}, {"cell_type": "markdown", "id": "1773b16a", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 拓展1：多城市天气速览\n", "每次只能查一个城市太麻烦了。我想同时知道北京、上海和东京的天气，怎么办？\n", "\n", "答案是：**用 `for` 循环！**"]}, {"cell_type": "markdown", "id": "b14fcd19", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 改进思路\n", "1.  **函数封装**: 把“根据城市名获取天气数据”这个核心逻辑，封装成一个独立的`get_weather(city)`函数。\n", "2.  **创建列表**: 用一个列表存储所有我们想查询的城市: `cities = ['Beijing', 'Shanghai', 'Tokyo']`。\n", "3.  **循环调用**: 使用`for`循环遍历这个城市列表，对每个城市都调用一次我们封装好的`get_weather()`函数。\n", "4.  **格式化打印**: 将获取的结果用`f-string`整齐地打印出来，让报告更美观。"]}, {"cell_type": "code", "execution_count": 4, "id": "13a4ba59", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 多城市天气速览 ---\n", "Beijing    | 温度: +25 °C   | 天气: Rain with thunderstorm\n", "Shanghai   | 温度: +29 °C   | 天气: Partly cloudy\n", "Tokyo      | 温度: +27 °C   | 天气: Clear\n", "London     | 温度: 19 °C    | 天气: Light drizzle\n"]}], "source": ["# 步骤1: 封装查询逻辑为一个函数, 这是良好编程习惯的开始\n", "def get_weather(city):\n", "    url = f\"https://goweather.xyz/weather/{city}\"\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status() # 如果请求失败 (如 404, 500), 会抛出异常\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"获取 {city} 天气失败: {e}\")\n", "        return None # 返回 None 表示失败，方便后续判断\n", "\n", "# 步骤2: 创建要查询的城市列表\n", "cities_to_check = ['Beijing', 'Shanghai', 'Tokyo', 'London']\n", "print(\"--- 多城市天气速览 ---\")\n", "\n", "# 步骤3: 循环遍历列表\n", "for city_name in cities_to_check:\n", "    weather_data_loop = get_weather(city_name)\n", "    # 步骤4: 处理并打印结果\n", "    if weather_data_loop:\n", "        # 我们只关心当前温度和描述\n", "        temperature = weather_data_loop.get('temperature', 'N/A')\n", "        description = weather_data_loop.get('description', 'N/A')\n", "        # 使用格式化字符串，让输出更整齐\n", "        # {city_name:<10} 表示左对齐，占据10个字符宽度\n", "        print(f\"{city_name:<10} | 温度: {temperature:<8} | 天气: {description}\")"]}, {"cell_type": "markdown", "id": "f09ee88e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "def get_weather(city):\n", "    url = f\"https://goweather.xyz/weather/{city}\"\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status() # 如果请求失败 (如 404, 500), 会抛出异常\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"获取 {city} 天气失败: {e}\")\n", "        return None # 返回 None 表示失败，方便后续判断\n", "\n", "cities = ['Beijing', 'Shanghai', 'Tokyo']\n", "print(\"--- 多城市天气速览 ---\")\n", "for city in cities:\n", "    weather_data = get_weather(city)\n", "    if weather_data:\n", "        temp = weather_data.get('temperature', 'N/A')\n", "        desc = weather_data.get('description', 'N/A')\n", "        # :<10 表示左对齐，占10个字符位，让输出更整齐\n", "        print(f\"{city:<10} | 温度: {temp:<8} | 天气: {desc}\")\n", "```"]}, {"cell_type": "markdown", "id": "54d6643a", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 拓展2：未来三天天气可视化\n", "一堆文字和数字不够直观，我能把未来三天的气温变化画成一张图吗？当然可以，使用我们之前学过的`matplotlib`！"]}, {"cell_type": "code", "execution_count": 11, "id": "a9981045", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# 1. 获取单个城市的天气数据\n", "city_to_plot = \"Tsuchiura\"\n", "weather_data_plot = get_weather(city_to_plot) # 复用上面的 get_weather 函数\n", "\n", "# 健壮性检查：确保数据有效\n", "if not weather_data_plot or 'forecast' not in weather_data_plot or not weather_data_plot['forecast']:\n", "    print(f\"无法获取 {city_to_plot} 的预报数据用于绘图。\")\n", "else:\n", "    # 2. 从JSON中提取出`forecast`部分\n", "    forecast_data = weather_data_plot['forecast']\n", "    \n", "    # 3. 准备绘图所需的X轴和Y轴数据列表\n", "    days = [f\"Day {item['day']}\" for item in forecast_data] # 生成X轴标签\n", "    temperatures = []\n", "    for item in forecast_data:\n", "        try:\n", "            # 3a. 清洗温度数据：去掉'°C'和前后空格，然后转为整数\n", "            temp_str = item.get('temperature', '0 °C').replace('°C', '').strip()\n", "            temperatures.append(int(temp_str))\n", "        except (ValueError, IndexError):\n", "            temperatures.append(0) # 如果数据格式不正确，则添加0\n", "    \n", "    # 4. 开始绘图\n", "    plt.figure(figsize=(8, 5)) # 创建一个 8x5 英寸的画布\n", "    plt.plot(days, temperatures, marker='o', linestyle='-') # 绘制带圆点标记的折线图\n", "    plt.title(f'{city_to_plot} 3-Day Temperature Trend') # 设置标题\n", "    plt.xlabel('Date') # 设置X轴标签\n", "    plt.ylabel('Temperature (°C)') # 设置Y轴标签\n", "    plt.grid(True) # 显示网格线，方便读数\n", "\n", "    # 在每个数据点上显示具体的温度数值\n", "    for i, temp in enumerate(temperatures):\n", "        plt.text(days[i], temp + 0.5, f' {temp}°C') # 向上偏移0.5个单位显示文本，避免重叠\n", "\n", "    plt.show() # 显示最终的图表"]}, {"cell_type": "markdown", "id": "cfa576e2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "import matplotlib.pyplot as plt\n", "\n", "weather_data = get_weather('Beijing')\n", "if weather_data and weather_data.get('forecast'):\n", "    forecast_data = weather_data['forecast']\n", "    days = [f\"Day {item['day']}\" for item in forecast_data]\n", "    temperatures = [int(item['temperature'].split()[0]) for item in forecast_data]\n", "\n", "    plt.figure(figsize=(8, 5))\n", "    plt.plot(days, temperatures, marker='o')\n", "    plt.title(f'Beijing 3-Day Temperature Trend')\n", "    plt.ylabel('Temperature (°C)')\n", "    plt.grid(True)\n", "    plt.show()\n", "```"]}, {"cell_type": "markdown", "id": "f215c988", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 终极拓展：打造一个天气机器人！\n", "如果我早上起床，不想开电脑，就想让手机告诉我今天天气怎么样，该穿什么衣服呢？我们可以把天气预报“推”到手机上！\n", "\n", "我们将使用一个叫 **Bark** 的App，它能让你的程序通过访问一个特殊的URL，给你的手机发送推送通知。"]}, {"cell_type": "markdown", "id": "546f2b33", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 设置步骤\n", "1.  **安装App**: 去手机的应用商店搜索 `Bark` 并安装。\n", "2.  **获取Key**: 安装后打开 App, 你会看到一串类似 `https://api.day.app/YOUR_UNIQUE_KEY/` 的地址。这个 `YOUR_UNIQUE_KEY` 就是你的私人密码，请复制下来，**不要泄露给任何人**。\n", "\n", "**它的API规则超级简单：**\n", "`https://api.day.app/<你的Key>/<推送标题>/<推送内容>`\n", "\n", "我们只需要用`requests.get()`访问这个拼装好的URL，就能发送通知了！"]}, {"cell_type": "code", "execution_count": 6, "id": "9adaff95", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在发送天气预报到你的手机...\n", "天气预报通知已成功发送! 请在你的手机上查看。\n"]}], "source": ["# 新建一个专门用来发送Bark通知的函数\n", "def send_bark_notification(key, title, body):\n", "    \"\"\"使用 Bark App 发送一条推送通知\"\"\"\n", "    # 检查用户是否已替换自己的密钥\n", "    if not key or key.startswith(\"YOUR\") or len(key) < 10:\n", "        print(f\"[演示模式]\\n[标题] {title}\\n[内容] {body}\")\n", "        return\n", "        \n", "    # Bark API 支持将标题和内容直接放在URL中，非常方便\n", "    url = f\"https://api.day.app/{key}/{title}/{body}\"\n", "    try:\n", "        print(\"正在发送天气预报到你的手机...\")\n", "        response = requests.get(url)\n", "        response.raise_for_status() # 检查是否发送成功\n", "        print(\"天气预报通知已成功发送! 请在你的手机上查看。\")\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"发送通知失败: {e}\")\n", "\n", "# --- 主程序区域 ---\n", "# --- 配置 --- \n", "BARK_KEY = \"kCd2MErsZreF2nQxmgCBSk\" # !!重要!! 请将这里替换成你自己的 Bark Key\n", "city_to_notify = \"Tsuchiura\"\n", "\n", "# 1. 获取天气数据\n", "weather_data = get_weather(city_to_notify) # 复用我们的函数\n", "\n", "if weather_data:\n", "    # 2. 解析和处理数据\n", "    temp_str = weather_data.get('temperature', 'N/A')\n", "    wind_str = weather_data.get('wind', 'N/A')\n", "    description = weather_data.get('description', '未知')\n", "    temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0\n", "    wind_value = int(weather_data.get('wind', '0 km/h').split()[0]) if wind_str != 'N/A' else 0\n", "\n", "    # 3. 生成穿衣建议\n", "    advice = get_clothing_advice(temp_value, wind_value) # 复用建议函数 (风速暂时忽略)\n", "    \n", "    # 4. 构建通知的标题和内容\n", "    notification_title = f\"{city_to_notify}天气速报 - {temp_str}\"\n", "    notification_body = f\"今天天气{description}，{advice}\"\n", "    \n", "    # 5. 发送通知!\n", "    send_bark_notification(BARK_KEY, notification_title, notification_body)"]}, {"cell_type": "markdown", "id": "7aa16e1d", "metadata": {}, "source": ["![](IMG_14617643C487-1.jpeg)"]}, {"cell_type": "markdown", "id": "04abc34a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (发送通知函数):**\n", "```python\n", "def send_bark_notification(key, title, body):\n", "    if not key or key.startswith(\"YOUR\"):\n", "        print(\"请先替换你的Bark Key。\")\n", "        return\n", "    \n", "    # Bark API的URL格式: .../Key/标题/内容\n", "    url = f\"https://api.day.app/{key}/{title}/{body}\"\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status()\n", "        print(\"通知已成功发送!\")\n", "    except Exception as e:\n", "        print(f\"发送失败: {e}\")\n", "```"]}, {"cell_type": "markdown", "id": "6ec7901d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (主程序):**\n", "```python\n", "BARK_KEY = \"YOUR_UNIQUE_KEY\" # 替换成你的Key\n", "city_to_notify = \"Shanghai\"\n", "\n", "weather_data = get_weather(city_to_notify)\n", "\n", "if weather_data:\n", "    # 2. 解析和处理数据\n", "    temp_str = weather_data.get('temperature', 'N/A')\n", "    wind_str = weather_data.get('wind', 'N/A')\n", "    description = weather_data.get('description', '未知')\n", "    temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0\n", "    wind_value = int(weather_data.get('wind', '0 km/h').split()[0]) if wind_str != 'N/A' else 0\n", "    \n", "    # 3. 生成穿衣建议\n", "    advice = get_clothing_advice(temp_value, wind_value) # 复用建议函数\n", "    \n", "    # 4. 构建通知的标题和内容\n", "    notification_title = f\"{city_to_notify}天气速报 - {temp_str}\"\n", "    notification_body = f\"今天天气{description}，{advice}\"\n", "    \n", "    # 发送通知!\n", "    send_bark_notification(BARK_KEY, notification_title, notification_body)\n", "```"]}, {"cell_type": "markdown", "id": "homework_section", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 作业：继续探索Bark的功能\n", "\n", "现在你已经掌握了基本的天气查询和Bark通知功能，让我们来探索更多Bark的高级功能！"]}, {"cell_type": "markdown", "id": "homework_1", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 练习1：播放声音通知\n", "\n", "Bark支持在发送通知时播放不同的声音。让我们为不同的天气情况设置不同的提示音。"]}, {"cell_type": "code", "execution_count": 15, "id": "homework_1_code", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 天气通知已发送到手机，使用声音: glass\n"]}], "source": ["def send_weather_notification_with_sound(bark_key, city, weather_data):\n", "    \"\"\"根据天气情况发送带有不同声音的通知\"\"\"\n", "    if not weather_data:\n", "        return\n", "    \n", "    temp_str = weather_data.get('temperature', 'N/A')\n", "    description = weather_data.get('description', '未知')\n", "    \n", "    # 根据天气描述选择不同的声音\n", "    if 'rain' in description.lower() or 'storm' in description.lower():\n", "        sound = 'telegraph'  # 雨天用电报声\n", "    elif 'sunny' in description.lower() or 'clear' in description.lower():\n", "        sound = 'bell'       # 晴天用铃声\n", "    elif 'cloud' in description.lower():\n", "        sound = 'glass'      # 多云用玻璃声\n", "    else:\n", "        sound = 'minuet'     # 其他情况用小步舞曲\n", "    \n", "    # 构建带声音的URL\n", "    title = f\"{city}天气播报\"\n", "    body = f\"当前温度{temp_str}，天气{description}\"\n", "    \n", "    # Bark的声音参数格式：?sound=声音名称\n", "    url = f\"https://api.day.app/{bark_key}/{title}/{body}?sound={sound}\"\n", "    \n", "    try:\n", "        response = requests.get(url)\n", "        if response.status_code == 200:\n", "            print(f\"✅ 天气通知已发送到手机，使用声音: {sound}\")\n", "        else:\n", "            print(f\"❌ 发送失败: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ 发送出错: {e}\")\n", "\n", "send_weather_notification_with_sound(<PERSON><PERSON><PERSON>_<PERSON>E<PERSON>, \"<PERSON><PERSON><PERSON><PERSON>\", get_weather(\"<PERSON><PERSON><PERSON><PERSON>\"))"]}, {"cell_type": "markdown", "id": "homework_1_demo", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "# 测试带声音的天气通知\n", "BARK_KEY = \"YOUR_UNIQUE_KEY\"\n", "city = \"Beijing\"\n", "weather_data = get_weather(city)\n", "\n", "send_weather_notification_with_sound(BARK_KEY, city, weather_data)\n", "```"]}, {"cell_type": "markdown", "id": "homework_2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 练习2：设置通知图标\n", "\n", "Bark还支持自定义通知图标，让我们为天气通知添加相应的图标。"]}, {"cell_type": "code", "execution_count": 16, "id": "homework_2_code", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 带图标的天气通知已发送\n"]}], "source": ["def send_weather_notification_with_icon(bark_key, city, weather_data):\n", "    \"\"\"发送带有天气图标的通知\"\"\"\n", "    if not weather_data:\n", "        return\n", "    \n", "    temp_str = weather_data.get('temperature', 'N/A')\n", "    description = weather_data.get('description', '未知')\n", "    \n", "    # 根据天气选择图标\n", "    if 'rain' in description.lower():\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/3313/3313998.png'  # 雨天图标\n", "    elif 'sunny' in description.lower() or 'clear' in description.lower():\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/869/869869.png'   # 晴天图标\n", "    elif 'cloud' in description.lower():\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/414/414927.png'   # 多云图标\n", "    else:\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/1163/1163661.png' # 默认天气图标\n", "    \n", "    title = f\"{city}天气预报\"\n", "    body = f\"温度: {temp_str}\\n天气: {description}\"\n", "    \n", "    # 使用icon参数设置图标\n", "    url = f\"https://api.day.app/{bark_key}/{title}/{body}?icon={icon}\"\n", "    \n", "    try:\n", "        response = requests.get(url)\n", "        if response.status_code == 200:\n", "            print(f\"✅ 带图标的天气通知已发送\")\n", "        else:\n", "            print(f\"❌ 发送失败: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ 发送出错: {e}\")\n", "\n", "send_weather_notification_with_icon(<PERSON>R<PERSON>_KEY, \"<PERSON><PERSON><PERSON><PERSON>\", get_weather(\"<PERSON><PERSON><PERSON><PERSON>\"))"]}, {"cell_type": "markdown", "id": "1ef47145", "metadata": {}, "source": ["![](IMG_1B25B86888E8-1.jpeg)"]}, {"cell_type": "markdown", "id": "homework_2_demo", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "# 测试带图标的天气通知\n", "BARK_KEY = \"YOUR_UNIQUE_KEY\"\n", "city = \"Shanghai\"\n", "weather_data = get_weather(city)\n", "\n", "send_weather_notification_with_icon(BARK_KEY, city, weather_data)\n", "```"]}, {"cell_type": "markdown", "id": "homework_3", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 练习3：定时天气播报\n", "\n", "让我们创建一个定时播报功能，每天早上8点自动发送天气预报。"]}, {"cell_type": "code", "execution_count": 9, "id": "homework_3_code", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "\n", "def schedule_daily_weather_report(bark_key, city, target_hour=8):\n", "    \"\"\"定时发送每日天气播报\"\"\"\n", "    print(f\"⏰ 定时天气播报已启动，每天{target_hour}点发送{city}的天气\")\n", "    \n", "    while True:\n", "        now = datetime.now()\n", "        \n", "        # 检查是否到了播报时间\n", "        if now.hour == target_hour and now.minute == 0:\n", "            print(f\"📢 开始发送{city}的每日天气播报...\")\n", "            \n", "            # 获取天气数据\n", "            weather_data = get_weather(city)\n", "            \n", "            if weather_data:\n", "                temp_str = weather_data.get('temperature', 'N/A')\n", "                description = weather_data.get('description', '未知')\n", "                wind_str = weather_data.get('wind', 'N/A')\n", "                \n", "                # 生成穿衣建议\n", "                temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0\n", "                wind_value = int(wind_str.split()[0]) if wind_str != 'N/A' else 0\n", "                advice = get_clothing_advice(temp_value, wind_value)\n", "                \n", "                # 发送早安天气播报\n", "                title = f\"☀️ 早安！{city}天气播报\"\n", "                body = f\"今天{description}，温度{temp_str}\\n{advice}\\n祝你有美好的一天！\"\n", "                \n", "                send_bark_notification(bark_key, title, body)\n", "                \n", "                # 等待61秒，避免重复发送\n", "                time.sleep(61)\n", "        \n", "        # 每分钟检查一次\n", "        time.sleep(60)"]}, {"cell_type": "markdown", "id": "homework_3_demo", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "# 启动定时天气播报（注意：这会一直运行）\n", "BARK_KEY = \"YOUR_UNIQUE_KEY\"\n", "city = \"Beijing\"\n", "\n", "import time\n", "from datetime import datetime\n", "\n", "def schedule_daily_weather_report(bark_key, city, target_hour=8):\n", "    \"\"\"定时发送每日天气播报\"\"\"\n", "    print(f\"⏰ 定时天气播报已启动，每天{target_hour}点发送{city}的天气\")\n", "    \n", "    while True:\n", "        now = datetime.now()\n", "        \n", "        # 检查是否到了播报时间\n", "        if now.hour == target_hour and now.minute == 0:\n", "            print(f\"📢 开始发送{city}的每日天气播报...\")\n", "            \n", "            # 获取天气数据\n", "            weather_data = get_weather(city)\n", "```"]}, {"cell_type": "markdown", "id": "192c2756", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python            \n", "            if weather_data:\n", "                temp_str = weather_data.get('temperature', 'N/A')\n", "                description = weather_data.get('description', '未知')\n", "                wind_str = weather_data.get('wind', 'N/A')\n", "                \n", "                # 生成穿衣建议\n", "                temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0\n", "                wind_value = int(wind_str.split()[0]) if wind_str != 'N/A' else 0\n", "                advice = get_clothing_advice(temp_value, wind_value)\n", "                \n", "                # 发送早安天气播报\n", "                title = f\"☀️ 早安！{city}天气播报\"\n", "                body = f\"今天{description}，温度{temp_str}\\n{advice}\\n祝你有美好的一天！\"\n", "                \n", "                send_bark_notification(bark_key, title, body)\n", "                \n", "                # 等待61秒，避免重复发送\n", "                time.sleep(61)\n", "        \n", "        # 每分钟检查一次\n", "        time.sleep(60)\n", "\n", "# 在实际使用中，建议在后台运行或使用系统定时任务\n", "schedule_daily_weather_report(BARK_KEY, city, target_hour=8)\n", "``` "]}, {"cell_type": "markdown", "id": "homework_4", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 练习4：多功能天气助手\n", "\n", "综合运用所有功能，创建一个完整的天气助手。"]}, {"cell_type": "code", "execution_count": 18, "id": "homework_4_code", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 高级天气助手通知已发送！\n", "📱 标题: 🌤️ <PERSON><PERSON><PERSON>ura天气播报\n", "📝 内容: 今天Partly cloudy，温度+25 °C\n", "天气炎热，建议穿T恤、短裤，别忘了防晒！\n", "🔊 声音: minuet\n"]}], "source": ["def advanced_weather_assistant(bark_key, city):\n", "    \"\"\"高级天气助手 - 综合所有Bark功能\"\"\"\n", "    weather_data = get_weather(city)\n", "    \n", "    if not weather_data:\n", "        print(f\"❌ 无法获取{city}的天气数据\")\n", "        return\n", "    \n", "    temp_str = weather_data.get('temperature', 'N/A')\n", "    description = weather_data.get('description', '未知')\n", "    wind_str = weather_data.get('wind', 'N/A')\n", "    \n", "    # 解析温度和风速\n", "    temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0\n", "    wind_value = int(wind_str.split()[0]) if wind_str != 'N/A' else 0\n", "    \n", "    # 生成建议\n", "    advice = get_clothing_advice(temp_value, wind_value)\n", "    \n", "    # 根据温度选择不同的通知样式\n", "    if temp_value >= 30:\n", "        # 高温警告 - 使用警告声音和红色图标\n", "        title = f\"🌡️ 高温预警 - {city}\"\n", "        body = f\"温度高达{temp_str}！\\n{advice}\\n请注意防暑降温！\"\n", "        sound = 'alarm'\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/1684/1684350.png'\n", "    elif temp_value <= 0:\n", "        # 低温提醒 - 使用提醒声音和蓝色图标\n", "        title = f\"❄️ 低温提醒 - {city}\"\n", "        body = f\"温度仅{temp_str}，天气寒冷\\n{advice}\\n请注意保暖！\"\n", "        sound = 'bell'\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/2336/2336776.png'\n", "    else:\n", "        # 普通天气 - 使用温和声音和天气图标\n", "        title = f\"🌤️ {city}天气播报\"\n", "        body = f\"今天{description}，温度{temp_str}\\n{advice}\"\n", "        sound = 'minuet'\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/1163/1163661.png'\n", "    \n", "    # 发送综合通知\n", "    url = f\"https://api.day.app/{bark_key}/{title}/{body}?sound={sound}&icon={icon}\"\n", "    \n", "    try:\n", "        response = requests.get(url)\n", "        if response.status_code == 200:\n", "            print(f\"✅ 高级天气助手通知已发送！\")\n", "            print(f\"📱 标题: {title}\")\n", "            print(f\"📝 内容: {body}\")\n", "            print(f\"🔊 声音: {sound}\")\n", "        else:\n", "            print(f\"❌ 发送失败: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ 发送出错: {e}\")\n", "\n", "advanced_weather_assistant(<PERSON><PERSON><PERSON>_<PERSON>, \"<PERSON><PERSON><PERSON><PERSON>\")"]}, {"cell_type": "markdown", "id": "cf5cf731", "metadata": {}, "source": ["![](IMG_65FBD66536C1-1.jpeg)"]}, {"cell_type": "markdown", "id": "7bf04a1a", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "# 测试高级天气助手\n", "BARK_KEY = \"YOUR_UNIQUE_KEY\"\n", "\n", "def advanced_weather_assistant(bark_key, city):\n", "    \"\"\"高级天气助手 - 综合所有Bark功能\"\"\"\n", "    weather_data = get_weather(city)\n", "    \n", "    if not weather_data:\n", "        print(f\"❌ 无法获取{city}的天气数据\")\n", "        return\n", "    \n", "    temp_str = weather_data.get('temperature', 'N/A')\n", "    description = weather_data.get('description', '未知')\n", "    wind_str = weather_data.get('wind', 'N/A')\n", "    \n", "    # 解析温度和风速\n", "    temp_value = int(temp_str.split()[0]) if temp_str != 'N/A' else 0\n", "    wind_value = int(wind_str.split()[0]) if wind_str != 'N/A' else 0\n", "```"]}, {"cell_type": "markdown", "id": "18f75210", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 生成建议\n", "    advice = get_clothing_advice(temp_value, wind_value)\n", "    \n", "    # 根据温度选择不同的通知样式\n", "    if temp_value >= 30:\n", "        # 高温警告 - 使用警告声音和红色图标\n", "        title = f\"🌡️ 高温预警 - {city}\"\n", "        body = f\"温度高达{temp_str}！\\n{advice}\\n请注意防暑降温！\"\n", "        sound = 'alarm'\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/1684/1684350.png'\n", "    elif temp_value <= 0:\n", "        # 低温提醒 - 使用提醒声音和蓝色图标\n", "        title = f\"❄️ 低温提醒 - {city}\"\n", "        body = f\"温度仅{temp_str}，天气寒冷\\n{advice}\\n请注意保暖！\"\n", "        sound = 'bell'\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/2336/2336776.png'\n", "    else:\n", "        # 普通天气 - 使用温和声音和天气图标\n", "        title = f\"🌤️ {city}天气播报\"\n", "        body = f\"今天{description}，温度{temp_str}\\n{advice}\"\n", "        sound = 'minuet'\n", "        icon = 'https://cdn-icons-png.flaticon.com/512/1163/1163661.png'\n", "```"]}, {"cell_type": "markdown", "id": "20195cd6", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "    # 发送综合通知\n", "    url = f\"https://api.day.app/{bark_key}/{title}/{body}?sound={sound}&icon={icon}\"\n", "    \n", "    try:\n", "        response = requests.get(url)\n", "        if response.status_code == 200:\n", "            print(f\"✅ 高级天气助手通知已发送！\")\n", "            print(f\"📱 标题: {title}\")\n", "            print(f\"📝 内容: {body}\")\n", "            print(f\"🔊 声音: {sound}\")\n", "        else:\n", "            print(f\"❌ 发送失败: {response.status_code}\")\n", "    except Exception as e:\n", "        print(f\"❌ 发送出错: {e}\")\n", "\n", "# 测试不同城市\n", "cities = ['Beijing', 'Shanghai', 'Tokyo']\n", "for city in cities:\n", "    print(f\"\\n--- 测试{city}天气助手 ---\")\n", "    advanced_weather_assistant(BARK_KEY, city)\n", "    time.sleep(2)  # 避免发送过快\n", "```"]}, {"cell_type": "markdown", "id": "homework_conclusion", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 作业总结\n", "\n", "通过这些练习，你已经掌握了Bark的高级功能：\n", "\n", "1. **🔊 声音通知**：为不同天气设置不同提示音\n", "2. **🎨 图标定制**：根据天气情况显示相应图标\n", "3. **⏰ 定时播报**：实现自动化天气推送\n", "4. **🤖 智能助手**：综合运用所有功能\n", "\n", "继续探索更多可能性，让你的天气助手更加智能和个性化！"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}