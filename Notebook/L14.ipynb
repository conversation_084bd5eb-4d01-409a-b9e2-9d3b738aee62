{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# 用Python打造联机乒乓球游戏"]}, {"cell_type": "markdown", "id": "675b7d95", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！今天，我们将迎来本系列课程的终极挑战——亲手创造属于你的第一个联机游戏！\n", "\n", "我们将把之前学习的网络通信知识与游戏开发结合起来，从零开始，一步一步地打造一个联机的乒乓球游戏。准备好了吗？让我们开始吧！"]}, {"cell_type": "markdown", "id": "95f32d4f", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 我们的“造物主”蓝图：三步走战略"]}, {"cell_type": "markdown", "id": "a4ef36ea", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["我们将分三步，循序渐进地完成这个项目。每一步都是一个小小的、可实现的目标。\n", "\n", "**第一步：学习游戏开发的“魔法棒” - Pygame Zero**\n", "  - 我们将从一个最简单的任务开始：在屏幕上创建一个方块，并让它动起来。\n", "\n", "**第二步：制作一个单机版的乒乓球游戏**\n", "  - 我们将创建一个能让两个人在**同一台电脑**上对战的游戏。\n", "\n", "**第三步：为游戏植入“网络灵魂”，实现联机对战**\n", "  - 我们将复用网络知识，让两台电脑上的游戏能互相通信。"]}, {"cell_type": "markdown", "id": "6e83614b", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第一步：Pygame Zero 入门"]}, {"cell_type": "markdown", "id": "05e264af", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### Pygame Zero 的“三大支柱”\n", "Pygame Zero就像一个“魔法厨房”，已经帮我们准备好了一切，我们只需要关心三件事：\n", "\n", "- **`draw()`**: “画家”，负责每一帧画什么。\n", "- **`update(dt)`**: “导演”，负责更新每个角色的状态。\n", "- **`on_key_down(key)`**: “倾听者”，负责处理玩家的键盘输入。"]}, {"cell_type": "markdown", "id": "a3b2cd42", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实践任务1：让一个方块动起来 (2分钟)\n", "**目标**: 创建一个简单的程序，让一个白色方块显示在屏幕上，并能用键盘方向键控制它移动。\n", "\n", "**操作**: 新建一个Python文件（例如`task1_move_box.py`），输入以下代码，然后运行它。\n", "**建议**: 尝试自己敲一遍代码，不要复制粘贴！"]}, {"cell_type": "markdown", "id": "ce429a21", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["```python\n", "# task1_move_box.py\n", "import pgzrun\n", "\n", "WIDTH = 800\n", "HEIGHT = 600\n", "SPEED = 5\n", "\n", "# 如果有图片 'images/paddle.png'，可以用Actor\n", "# player = Actor('paddle', center=(WIDTH / 2, HEIGHT / 2))\n", "# 如果没有图片，用Rect创建一个矩形代替\n", "player = Rect((WIDTH/2 - 10, HEIGHT/2 - 50), (20, 100))\n", "\n", "def draw():\n", "    screen.clear()\n", "    screen.draw.filled_rect(player, \"white\") # 绘制矩形\n", "\n", "def update(dt):\n", "    if keyboard.left: player.x -= SPEED\n", "    if keyboard.right: player.x += SPEED\n", "    if keyboard.up: player.y -= SPEED\n", "    if keyboard.down: player.y += SPEED\n", "\n", "pgzrun.go() # 启动游戏\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "7d4e8fe4", "metadata": {"slideshow": {"slide_type": "-"}}, "outputs": [], "source": ["# 此代码块用于执行，幻灯片中不显示\n", "# import pgzrun\n", "# WIDTH = 800\n", "# HEIGHT = 600\n", "# SPEED = 5\n", "# player = Rect((WIDTH/2 - 10, HEIGHT/2 - 50), (20, 100))\n", "# def draw():\n", "#     screen.clear()\n", "#     screen.draw.filled_rect(player, \"white\")\n", "# def update(dt):\n", "#     if keyboard.left: player.x -= SPEED\n", "#     if keyboard.right: player.x += SPEED\n", "#     if keyboard.up: player.y -= SPEED\n", "#     if keyboard.down: player.y += SPEED\n", "# pgzrun.go()"]}, {"cell_type": "markdown", "id": "3f77ad82", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第二步：实现单机版乒乓球游戏"]}, {"cell_type": "markdown", "id": "2adb527c", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 任务拆解：一步一步构建游戏\n", "我们将在上一个任务的基础上，逐步添加乒乓球游戏的各个核心元素。\n", "\n", "- **2.1 创建游戏对象 (2分钟)**：创建两个球拍和一个球。\n", "- **2.2 球拍移动 (2分钟)**：让两个玩家能分别控制自己的球拍。\n", "- **2.3 球的运动与反弹 (2分钟)**：让球自己动起来，并碰到边界时反弹。\n", "- **2.4 核心玩法：碰撞 (2分钟)**：实现球与球拍的碰撞。\n", "- **2.5 胜负规则：得分 (2分钟)**：实现得分和重置逻辑。"]}, {"cell_type": "markdown", "id": "3701d653", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实践任务2.1：创建游戏对象 (2分钟)\n", "**目标**: 在屏幕上创建两个球拍和一个球。\n", "**操作**: 新建文件`task2_pong_local.py`，加入以下初始化代码。"]}, {"cell_type": "markdown", "id": "04f0238b", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["```python\n", "import pgzrun\n", "\n", "WIDTH, HEIGHT = 800, 600\n", "PADDLE_SPEED = 10\n", "PADDLE_WIDTH, PADDLE_HEIGHT = 20, 100\n", "BALL_RADIUS = 10\n", "\n", "paddle1 = Rect((50, HEIGHT/2 - PADDLE_HEIGHT/2), (PADDLE_WIDTH, PADDLE_HEIGHT))\n", "paddle2 = Rect((WIDTH - 50 - PADDLE_WIDTH, HEIGHT/2 - PADDLE_HEIGHT/2), (PADDLE_WIDTH, PADDLE_HEIGHT))\n", "ball = Rect((0, 0), (BALL_RADIUS*2, BALL_RADIUS*2))\n", "ball.center = (WIDTH/2, HEIGHT/2)\n", "```"]}, {"cell_type": "markdown", "id": "1793083e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实践任务2.2：让球拍动起来 (2分钟)\n", "**目标**: 让左边玩家用W/S键、右边玩家用上/下箭头键控制各自的球拍上下移动。\n", "**操作**: 在`task2_pong_local.py`中添加`update`函数。"]}, {"cell_type": "markdown", "id": "9f63f2b8", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["```python\n", "def update(dt):\n", "    # 左侧球拍控制\n", "    if keyboard.w and paddle1.top > 0:\n", "        paddle1.y -= PADDLE_SPEED\n", "    if keyboard.s and paddle1.bottom < HEIGHT:\n", "        paddle1.y += PADDLE_SPEED\n", "\n", "    # 右侧球拍控制\n", "    if keyboard.up and paddle2.top > 0:\n", "        paddle2.y -= PADDLE_SPEED\n", "    if keyboard.down and paddle2.bottom < HEIGHT:\n", "        paddle2.y += PADDLE_SPEED\n", "```"]}, {"cell_type": "markdown", "id": "38a7ef32", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实践任务2.3-2.5：实现完整游戏逻辑 (5分钟)\n", "**目标**: 将球的运动、碰撞、得分逻辑全部加入，完成单机版游戏。\n", "**操作**: 将以下代码整合到`task2_pong_local.py`的相应位置。\n", "\n", "试着和同桌一起玩一玩这个游戏吧！"]}, {"cell_type": "markdown", "id": "ffc0f442", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["```python\n", "# (这是task2_pong_local.py的完整代码)\n", "import pgzrun\n", "WIDTH, HEIGHT = 800, 600\n", "PADDLE_SPEED = 10\n", "# ... 省略其他常量定义 ...\n", "\n", "paddle1 = Rect((50, HEIGHT/2-50), (20, 100))\n", "paddle2 = Rect((WIDTH-70, HEIGHT/2-50), (20, 100))\n", "ball = Rect((WIDTH/2-10, HEIGHT/2-10), (20, 20))\n", "\n", "ball_speed_x = 5\n", "ball_speed_y = 5\n", "score1 = 0\n", "score2 = 0\n", "\n", "def reset_ball():\n", "    global ball_speed_x\n", "    ball.center = (WIDTH / 2, HEIGHT / 2)\n", "    ball_speed_x *= -1\n", "```"]}, {"cell_type": "markdown", "id": "efa4a5c2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def draw():\n", "    screen.clear()\n", "    screen.draw.filled_rect(paddle1, \"white\")\n", "    screen.draw.filled_rect(paddle2, \"white\")\n", "    screen.draw.filled_circle(ball.center, BALL_RADIUS, \"white\")\n", "    screen.draw.text(f\"{score1} : {score2}\", center=(WIDTH / 2, 30), fontsize=60, color=\"white\")\n", "\n", "def update(dt):\n", "    global score1, score2, ball_speed_x, ball_speed_y\n", "    # ... (此处省略已讲解的球拍移动代码) ...\n", "    ball.x += ball_speed_x\n", "    ball.y += ball_speed_y\n", "    if ball.top < 0 or ball.bottom > HEIGHT: ball_speed_y *= -1\n", "    if ball.colliderect(paddle1) or ball.colliderect(paddle2):\n", "        ball_speed_x *= -1.1\n", "        # sounds.eep.play() # 如果有音效文件可以取消注释\n", "    if ball.left < 0: score2 += 1; reset_ball()\n", "    if ball.right > WIDTH: score1 += 1; reset_ball()\n", "\n", "pgzrun.go()\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "3a7bccfd", "metadata": {"slideshow": {"slide_type": "-"}}, "outputs": [], "source": ["# 此代码块用于执行，幻灯片中不显示\n", "# (粘贴完整的task2_pong_local.py代码)"]}, {"cell_type": "markdown", "id": "5b5d486e", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第三步：植入“网络灵魂”"]}, {"cell_type": "markdown", "id": "48e863b8", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 问题1：状态不同步\n", "**问题**: 如果两台电脑都独立运行游戏，很快就会出现“我的球在这里，你的球在那儿”的状态不一致情况。\n", "**解决方案**: **主机/客户端 (Host/Client) 模型**。\n", "- **主机 (Host)**: “总指挥官”，全权负责所有计算。\n", "- **客户端 (Client)**: “前线士兵”，只负责显示画面和发送自己的操作。"]}, {"cell_type": "markdown", "id": "45964e1c", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 问题2：游戏卡顿\n", "**问题**: 如果在游戏主循环(`update`)里处理网络收发，一旦网络延迟，整个游戏都会被卡住。\n", "**解决方案**: **多线程**。\n", "- **游戏主线程**: 专心负责游戏逻辑和画面渲染。\n", "- **网络子线程**: 专门在后台负责收发网络数据。"]}, {"cell_type": "markdown", "id": "eafa0ffb", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实践任务3&4：分组实现并测试网络版\n", "**目标**: 两人一组，一人负责主机，一人负责客户端，将单机版游戏改造成联机版。\n", "\n", "**实现方法**:\n", "我们将创建一个`task3_pong_network.py`文件，它既可以当主机，也可以当客户端。\n", "\n", "**核心改动**:\n", "1. 创建一个`GameState`类来统一管理所有需要同步的数据。\n", "2. 创建一个`network_thread_func`在后台收发数据。\n", "3. 在`update`函数中，根据`role`是'host'还是'client'来执行不同逻辑。\n", "\n", "**以下是联机版的完整代码，我们已为你添加了详细注释。请仔细阅读，理解每一部分的作用。**"]}, {"cell_type": "markdown", "id": "network_task3_1", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实践任务3：网络版基础架构 (5分钟)\n", "**目标**: 创建`task3_pong_network.py`，实现基础的网络通信架构。\n", "**操作**: 新建文件，添加以下基础代码。"]}, {"cell_type": "markdown", "id": "network_task3_2", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["```python\n", "import pgzrun\n", "import socket\n", "import threading\n", "import json\n", "import time\n", "\n", "WIDTH, HEIGHT = 800, 600\n", "PADDLE_SPEED = 10\n", "BALL_RADIUS = 10\n", "\n", "class GameState:\n", "    def __init__(self):\n", "        self.paddle1_y = HEIGHT/2 - 50\n", "        self.paddle2_y = HEIGHT/2 - 50\n", "        self.ball_x = WIDTH/2\n", "        self.ball_y = HEIGHT/2\n", "        self.ball_speed_x = 5\n", "        self.ball_speed_y = 5\n", "        self.score1 = 0\n", "        self.score2 = 0\n", "```"]}, {"cell_type": "markdown", "id": "network_task3_3", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "# 全局变量\n", "game_state = GameState()\n", "role = None  # 'host' 或 'client'\n", "sock = None\n", "conn = None\n", "client_paddle_input = {'up': False, 'down': False}\n", "running = True\n", "\n", "# 创建游戏对象\n", "paddle1 = Rect((50, game_state.paddle1_y), (20, 100))\n", "paddle2 = Rect((WIDTH-70, game_state.paddle2_y), (20, 100))\n", "ball = Rect((game_state.ball_x-10, game_state.ball_y-10), (20, 20))\n", "\n", "def reset_ball():\n", "    game_state.ball_x = WIDTH / 2\n", "    game_state.ball_y = HEIGHT / 2\n", "    game_state.ball_speed_x *= -1\n", "```"]}, {"cell_type": "markdown", "id": "network_task3_4", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def network_thread_func():\n", "    global running, client_paddle_input\n", "    while running:\n", "        try:\n", "            if role == 'host' and conn:\n", "                # 主机：发送游戏状态，接收客户端输入\n", "                data = {\n", "                    'paddle1_y': game_state.paddle1_y,\n", "                    'paddle2_y': game_state.paddle2_y,\n", "                    'ball_x': game_state.ball_x,\n", "                    'ball_y': game_state.ball_y,\n", "                    'score1': game_state.score1,\n", "                    'score2': game_state.score2\n", "                }\n", "                conn.send((json.dumps(data) + '\\n').encode())\n", "                \n", "                response = conn.recv(1024).decode().strip()\n", "                if response:\n", "                    client_paddle_input = json.loads(response)\n", "```"]}, {"cell_type": "markdown", "id": "network_task3_5", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "            elif role == 'client' and sock:\n", "                # 客户端：接收游戏状态，发送自己的输入\n", "                data = sock.recv(1024).decode().strip()\n", "                if data:\n", "                    state = json.loads(data)\n", "                    game_state.paddle1_y = state['paddle1_y']\n", "                    game_state.paddle2_y = state['paddle2_y']\n", "                    game_state.ball_x = state['ball_x']\n", "                    game_state.ball_y = state['ball_y']\n", "                    game_state.score1 = state['score1']\n", "                    game_state.score2 = state['score2']\n", "                \n", "                # 发送自己的输入\n", "                my_input = {\n", "                    'up': keyboard.up,\n", "                    'down': keyboard.down\n", "                }\n", "                sock.send((json.dumps(my_input) + '\\n').encode())\n", "```"]}, {"cell_type": "markdown", "id": "network_task3_6", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        except Exception as e:\n", "            print(f\"网络错误: {e}\")\n", "            break\n", "        time.sleep(1/60)  # 60 FPS\n", "\n", "def start_host():\n", "    global sock, conn, role\n", "    role = 'host'\n", "    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "    sock.bind(('localhost', 12345))\n", "    sock.listen(1)\n", "    print(\"等待客户端连接...\")\n", "    conn, addr = sock.accept()\n", "    print(f\"客户端已连接: {addr}\")\n", "    threading.Thread(target=network_thread_func, daemon=True).start()\n", "\n", "def start_client():\n", "    global sock, role\n", "    role = 'client'\n", "    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "    sock.connect(('localhost', 12345))\n", "    print(\"已连接到主机\")\n", "    threading.Thread(target=network_thread_func, daemon=True).start()\n", "```"]}, {"cell_type": "markdown", "id": "network_task4_1", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 实践任务4：完成游戏逻辑与测试 (5分钟)\n", "**目标**: 完成网络版游戏的核心逻辑，并进行联机测试。\n", "**操作**: 在`task3_pong_network.py`中添加以下游戏逻辑代码。"]}, {"cell_type": "markdown", "id": "network_task4_2", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["```python\n", "def draw():\n", "    screen.clear()\n", "    # 更新对象位置\n", "    paddle1.y = game_state.paddle1_y\n", "    paddle2.y = game_state.paddle2_y\n", "    ball.center = (game_state.ball_x, game_state.ball_y)\n", "    \n", "    # 绘制游戏对象\n", "    screen.draw.filled_rect(paddle1, \"white\")\n", "    screen.draw.filled_rect(paddle2, \"white\")\n", "    screen.draw.filled_circle(ball.center, BALL_RADIUS, \"white\")\n", "    \n", "    # 显示分数和角色信息\n", "    score_text = f\"{game_state.score1} : {game_state.score2}\"\n", "    screen.draw.text(score_text, center=(WIDTH/2, 30), fontsize=60, color=\"white\")\n", "    role_text = f\"角色: {role or '未连接'}\"\n", "    screen.draw.text(role_text, (10, HEIGHT-30), fontsize=30, color=\"yellow\")\n", "```"]}, {"cell_type": "markdown", "id": "network_task4_3", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def update(dt):\n", "    if role == 'host':\n", "        # 主机负责所有游戏逻辑计算\n", "        # 左侧球拍控制（主机玩家）\n", "        if keyboard.w and game_state.paddle1_y > 0:\n", "            game_state.paddle1_y -= PADDLE_SPEED\n", "        if keyboard.s and game_state.paddle1_y < HEIGHT - 100:\n", "            game_state.paddle1_y += PADDLE_SPEED\n", "        \n", "        # 右侧球拍控制（客户端玩家）\n", "        if client_paddle_input.get('up') and game_state.paddle2_y > 0:\n", "            game_state.paddle2_y -= PADDLE_SPEED\n", "        if client_paddle_input.get('down') and game_state.paddle2_y < HEIGHT - 100:\n", "            game_state.paddle2_y += PADDLE_SPEED\n", "        \n", "        # 球的运动\n", "        game_state.ball_x += game_state.ball_speed_x\n", "        game_state.ball_y += game_state.ball_speed_y\n", "```"]}, {"cell_type": "markdown", "id": "network_task4_4", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "        # 球的边界反弹\n", "        if game_state.ball_y <= BALL_RADIUS or game_state.ball_y >= HEIGHT - BALL_RADIUS:\n", "            game_state.ball_speed_y *= -1\n", "        \n", "        # 球拍碰撞检测\n", "        ball_rect = Rect((game_state.ball_x-10, game_state.ball_y-10), (20, 20))\n", "        paddle1_rect = Rect((50, game_state.paddle1_y), (20, 100))\n", "        paddle2_rect = Rect((WIDTH-70, game_state.paddle2_y), (20, 100))\n", "        \n", "        if ball_rect.colliderect(paddle1_rect) or ball_rect.colliderect(paddle2_rect):\n", "            game_state.ball_speed_x *= -1.05  # 稍微加速\n", "        \n", "        # 得分检测\n", "        if game_state.ball_x < 0:\n", "            game_state.score2 += 1\n", "            reset_ball()\n", "        elif game_state.ball_x > WIDTH:\n", "            game_state.score1 += 1\n", "            reset_ball()\n", "```"]}, {"cell_type": "markdown", "id": "network_task4_5", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def on_key_down(key):\n", "    if key == keys.H:\n", "        start_host()\n", "    elif key == keys.C:\n", "        start_client()\n", "    elif key == keys.Q:\n", "        global running\n", "        running = False\n", "        if sock:\n", "            sock.close()\n", "        if conn:\n", "            conn.close()\n", "\n", "# 启动游戏\n", "print(\"按 H 键启动主机模式，按 C 键启动客户端模式\")\n", "print(\"主机玩家使用 W/S 键控制，客户端玩家使用 ↑/↓ 键控制\")\n", "print(\"按 Q 键退出游戏\")\n", "pgzrun.go()\n", "```"]}, {"cell_type": "markdown", "id": "test_instructions", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 测试步骤\n", "**两人一组测试联机功能**:\n", "\n", "1. **第一人**: 运行`python task3_pong_network.py`，按`H`键启动主机模式。\n", "2. **第二人**: 运行`python task3_pong_network.py`，按`C`键启动客户端模式。\n", "3. **开始游戏**: 主机玩家用`W/S`键控制左侧球拍，客户端玩家用`↑/↓`键控制右侧球拍。\n", "4. **观察同步**: 注意两个屏幕上的球和分数是否保持同步。\n", "\n", "**可能遇到的问题**:\n", "- 如果连接失败，检查防火墙设置\n", "- 如果游戏卡顿，可能是网络延迟导致\n", "- 按`Q`键可以退出游戏\n", "\n", "**恭喜！你已经成功创建了第一个联机游戏！**"]}, {"cell_type": "code", "execution_count": null, "id": "network_code_exec", "metadata": {"slideshow": {"slide_type": "-"}}, "outputs": [], "source": ["# 此代码块用于执行，幻灯片中不显示\n", "# (完整的task3_pong_network.py代码可以在这里执行测试)"]}, {"cell_type": "markdown", "id": "summary_slide", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 课程总结：你已经是游戏开发者了！"]}, {"cell_type": "markdown", "id": "summary_content", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 今天你学会了什么？\n", "\n", "**技术技能**:\n", "- **Pygame Zero**: 掌握了游戏开发的基础框架\n", "- **网络编程**: 学会了Socket通信和多线程编程\n", "- **游戏架构**: 理解了主机/客户端模型\n", "- **状态同步**: 解决了多人游戏的核心难题\n", "\n", "**编程思维**:\n", "- **模块化思考**: 将复杂问题分解为小步骤\n", "- **架构设计**: 学会设计可扩展的程序结构\n", "- **调试技能**: 处理网络通信中的各种问题\n", "\n", "**下一步挑战**:\n", "- 尝试添加音效和更好的图形\n", "- 实现更多玩家的支持\n", "- 添加游戏房间和匹配系统\n", "- 探索其他类型的联机游戏"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}