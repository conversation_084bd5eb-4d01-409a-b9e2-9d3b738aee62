# Python网络编程：迷你微信聊天系统

## 📖 项目简介

这是一个完整的局域网聊天系统，实现了L12课程的所有挑战任务。系统包含一个增强版服务器和两种客户端（图形界面和命令行），支持昵称系统、群聊、私聊等功能。

## ✨ 功能特性

### 🎯 核心功能
- **昵称系统** - 用户可以设置个性化昵称，重复昵称自动添加后缀
- **群聊功能** - 支持多人同时在线聊天，消息实时广播
- **私聊功能** - 使用 `@昵称 消息` 格式进行一对一私聊
- **用户管理** - `/list` 查看在线用户，`/quit` 优雅退出

### 🖥️ 界面选择
- **图形界面客户端** - 友好的Tkinter GUI，包含消息显示区和输入框
- **命令行客户端** - 简单的文本界面，适合测试和调试

### 🚀 技术亮点
- **多线程并发** - 服务器支持多客户端同时连接
- **线程安全** - 使用锁机制保护共享数据
- **GUI与网络分离** - 避免界面冻结的最佳实践
- **异常处理** - 优雅处理网络断开和错误情况

## 📁 文件结构

```
Notebook/
├── enhanced_chat_server.py    # 增强版聊天服务器
├── gui_chat_client.py         # 图形界面客户端
├── simple_chat_client.py      # 命令行客户端
├── demo_chat_system.py        # 演示脚本（推荐使用）
├── README_Chat_System.md      # 本说明文件
└── L12.ipynb                  # 课程笔记（包含完整实现代码）
```

## 🚀 快速开始

### 方法一：使用演示脚本（推荐）

1. **运行演示脚本**
   ```bash
   python demo_chat_system.py
   ```

2. **按照菜单提示操作**
   - 选择 `1` 启动服务器
   - 选择 `2` 启动图形界面客户端
   - 选择 `3` 启动命令行客户端

### 方法二：手动启动

1. **启动服务器**
   ```bash
   python enhanced_chat_server.py
   ```

2. **启动图形界面客户端**
   ```bash
   python gui_chat_client.py
   ```

3. **或启动命令行客户端**
   ```bash
   python simple_chat_client.py
   ```

## 💬 使用说明

### 连接服务器
1. 启动服务器后，记下服务器的IP地址
2. 启动客户端，输入服务器IP地址（本机测试使用 `localhost`）
3. 输入您的昵称
4. 连接成功后即可开始聊天

### 聊天命令

#### 群聊
```
直接输入消息，所有在线用户都能看到
例：大家好！
```

#### 私聊
```
@昵称 消息内容
例：@Alice 你好，我是Bob
```

#### 系统命令
```
/list    - 查看在线用户列表
/quit    - 退出聊天
/help    - 查看帮助（仅命令行客户端）
```

## 🌐 网络配置

### 本机测试
- 服务器地址：`localhost` 或 `127.0.0.1`
- 端口：`8000`

### 局域网聊天
1. **找到服务器IP地址**
   - Windows: `ipconfig`
   - macOS/Linux: `ifconfig`

2. **配置防火墙**
   - 确保8000端口允许通信

3. **客户端连接**
   - 输入服务器的真实IP地址（如：*************）

## 🎯 演示场景

### 多人群聊
```
[系统] Alice 加入了聊天室
[系统] Bob 加入了聊天室
[群聊] Alice: 大家好！
[群聊] Bob: 你好Alice！
```

### 私聊功能
```
Alice输入: @Bob 你在哪里？
Bob收到: [私聊] Alice: 你在哪里？
Alice收到: [私聊已发送给 Bob]
```

### 用户管理
```
用户输入: /list
系统回复: 在线用户 (3): Alice, Bob, Charlie
```

## 🔧 技术实现

### 服务器端
- **多线程处理** - 每个客户端一个独立线程
- **线程安全** - 使用 `threading.Lock()` 保护共享数据
- **消息广播** - 实现群聊和私聊消息分发
- **用户管理** - 维护在线用户列表

### 客户端
- **GUI线程分离** - 网络通信在独立线程中进行
- **消息队列** - 使用 `queue.Queue` 实现线程间通信
- **实时更新** - 使用 `tkinter.after()` 定期更新界面

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器是否已启动
   - 确认IP地址和端口号正确
   - 检查防火墙设置

2. **界面冻结**
   - 重启客户端程序
   - 检查网络连接

3. **消息发送失败**
   - 检查网络连接
   - 确认服务器正常运行

### 调试技巧
- 使用命令行客户端进行测试
- 查看服务器控制台输出
- 检查错误消息提示

## 📚 学习要点

### 网络编程概念
- **Socket通信** - TCP连接的建立和维护
- **客户端-服务器架构** - 分布式应用的基本模式
- **IP地址和端口** - 网络通信的寻址机制

### 多线程编程
- **并发处理** - 同时处理多个客户端连接
- **线程安全** - 保护共享资源的访问
- **线程间通信** - 使用队列传递数据

### GUI编程
- **事件驱动** - 响应用户界面操作
- **线程分离** - 避免界面冻结
- **用户体验** - 友好的界面设计

## 🎓 扩展练习

1. **添加表情功能** - 支持发送表情符号
2. **文件传输** - 实现文件分享功能
3. **聊天记录** - 保存和查看历史消息
4. **用户认证** - 添加登录和密码功能
5. **群组管理** - 创建和管理聊天群组

## 📄 许可证

本项目仅用于教育目的，遵循MIT许可证。

---

**祝您学习愉快！如有问题，请查看代码注释或联系老师。** 🎉
