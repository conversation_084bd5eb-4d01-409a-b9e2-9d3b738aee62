#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
L12_Final.py - Python网络编程：打造你的局域网"迷你微信"

这个程序实现了一个支持多人在线的局域网聊天服务器，包含以下功能：
1. 支持多客户端同时连接（多线程）
2. 群聊功能（消息广播）
3. 客户端连接状态管理
4. 线程安全的客户端列表操作

使用方法：
1. 运行此程序作为服务器
2. 客户端使用socket连接到服务器IP的8000端口
3. 多个客户端可以同时连接并进行群聊
"""

import socket
import threading
import time
import sys

class ChatServer:
    def __init__(self, host='0.0.0.0', port=8000):
        """
        初始化聊天服务器
        
        Args:
            host (str): 服务器绑定的IP地址，'0.0.0.0'表示监听所有网络接口
            port (int): 服务器监听的端口号
        """
        self.host = host
        self.port = port
        self.clients = []  # 存储所有客户端socket的列表
        self.clients_lock = threading.Lock()  # 用于在多线程环境下安全地操作clients列表
        self.server_socket = None
        self.running = False
    
    def broadcast(self, message, source_socket):
        """
        广播消息给所有在线客户端（除了发送者）
        
        Args:
            message (bytes): 要广播的消息（字节格式）
            source_socket (socket): 消息发送者的socket，不会收到广播
        """
        with self.clients_lock:
            # 创建客户端列表的副本，避免在迭代过程中修改列表
            clients_copy = self.clients.copy()
            
        for client in clients_copy:
            if client != source_socket:  # 不把消息发回给发送者自己
                try:
                    client.send(message)
                except Exception as e:
                    # 如果发送失败，说明该客户端已断开，将其移除
                    print(f"[错误] 向客户端发送消息失败: {e}")
                    self.remove_client(client)
    
    def remove_client(self, client_socket):
        """
        安全地移除客户端
        
        Args:
            client_socket (socket): 要移除的客户端socket
        """
        with self.clients_lock:
            if client_socket in self.clients:
                self.clients.remove(client_socket)
        try:
            client_socket.close()
        except:
            pass
    
    def handle_client(self, client_socket, client_address):
        """
        处理单个客户端的所有通信（在独立线程中运行）
        
        Args:
            client_socket (socket): 客户端socket对象
            client_address (tuple): 客户端地址信息 (IP, port)
        """
        # 将新客户端添加到列表中
        with self.clients_lock:
            self.clients.append(client_socket)
        
        print(f"[新连接] {client_address} 已加入聊天室。当前在线人数: {len(self.clients)}")
        
        # 向所有其他客户端广播新用户加入的消息
        join_message = f"[系统消息] 用户 {client_address} 加入了聊天室"
        self.broadcast(join_message.encode('utf-8'), client_socket)
        
        try:
            while self.running:
                # 接收客户端消息
                message = client_socket.recv(1024)
                if not message:
                    # 如果接收到空消息，说明客户端已断开
                    break
                
                # 解码消息并准备广播
                decoded_message = message.decode('utf-8')
                broadcast_message = f"来自 {client_address} 的消息: {decoded_message}"
                print(broadcast_message)
                
                # 广播消息给所有其他客户端
                self.broadcast(broadcast_message.encode('utf-8'), client_socket)
                
        except ConnectionResetError:
            print(f"[连接中断] {client_address} 意外断开。")
        except Exception as e:
            print(f"[错误] 处理客户端 {client_address} 时发生错误: {e}")
        finally:
            # 清理工作
            self.remove_client(client_socket)
            print(f"[连接关闭] {client_address} 已离开。在线人数: {len(self.clients)}")
            
            # 向其他客户端广播用户离开的消息
            leave_message = f"[系统消息] 用户 {client_address} 离开了聊天室"
            self.broadcast(leave_message.encode('utf-8'), client_socket)
    
    def start_server(self):
        """
        启动聊天服务器
        """
        try:
            # 创建服务器socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            # 设置socket选项，允许地址重用
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定地址和端口
            self.server_socket.bind((self.host, self.port))
            
            # 开始监听连接，最多允许5个等待连接的客户端
            self.server_socket.listen(5)
            self.running = True
            
            print(f"[*] 聊天服务器正在监听 {self.host}:{self.port}")
            print(f"[*] 等待客户端连接...")
            print(f"[*] 按 Ctrl+C 停止服务器")
            
            while self.running:
                try:
                    # 接受客户端连接
                    client_socket, client_address = self.server_socket.accept()
                    
                    # 为每个客户端创建一个独立的处理线程
                    client_handler = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, client_address)
                    )
                    client_handler.daemon = True  # 设置为守护线程
                    client_handler.start()  # 启动线程
                    
                except socket.error as e:
                    if self.running:
                        print(f"[错误] 接受连接时发生错误: {e}")
                    break
                    
        except Exception as e:
            print(f"[错误] 启动服务器时发生错误: {e}")
        finally:
            self.stop_server()
    
    def stop_server(self):
        """
        停止聊天服务器
        """
        print("\n[*] 正在关闭服务器...")
        self.running = False
        
        # 关闭所有客户端连接
        with self.clients_lock:
            for client in self.clients.copy():
                try:
                    client.close()
                except:
                    pass
            self.clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("[*] 服务器已关闭")


class SimpleClient:
    """
    简单的测试客户端类（用于演示）
    """
    def __init__(self, server_host, server_port=8000):
        self.server_host = server_host
        self.server_port = server_port
        self.client_socket = None
        self.running = False
    
    def connect_to_server(self):
        """
        连接到聊天服务器
        """
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.connect((self.server_host, self.server_port))
            self.running = True
            print(f"[*] 已连接到服务器 {self.server_host}:{self.server_port}")
            return True
        except Exception as e:
            print(f"[错误] 连接服务器失败: {e}")
            return False
    
    def receive_messages(self):
        """
        接收服务器消息的线程函数
        """
        while self.running:
            try:
                message = self.client_socket.recv(1024).decode('utf-8')
                if message:
                    print(f"\n{message}")
                    print("请输入消息 (输入 'quit' 退出): ", end='', flush=True)
                else:
                    break
            except Exception as e:
                if self.running:
                    print(f"\n[错误] 接收消息失败: {e}")
                break
    
    def start_chat(self):
        """
        开始聊天
        """
        if not self.connect_to_server():
            return
        
        # 启动接收消息的线程
        receive_thread = threading.Thread(target=self.receive_messages)
        receive_thread.daemon = True
        receive_thread.start()
        
        print("欢迎来到聊天室！")
        print("请输入消息 (输入 'quit' 退出): ")
        
        try:
            while self.running:
                message = input()
                if message.lower() == 'quit':
                    break
                if message.strip():
                    self.client_socket.send(message.encode('utf-8'))
        except KeyboardInterrupt:
            pass
        finally:
            self.disconnect()
    
    def disconnect(self):
        """
        断开连接
        """
        self.running = False
        if self.client_socket:
            try:
                self.client_socket.close()
            except:
                pass
        print("\n[*] 已断开连接")


def main():
    """
    主函数 - 根据命令行参数决定运行服务器还是客户端
    """
    if len(sys.argv) == 1:
        # 默认运行服务器
        print("启动聊天服务器...")
        server = ChatServer()
        try:
            server.start_server()
        except KeyboardInterrupt:
            print("\n[*] 收到中断信号")
        finally:
            server.stop_server()
            
    elif len(sys.argv) == 2 and sys.argv[1] == 'client':
        # 运行测试客户端
        server_ip = input("请输入服务器IP地址 (默认: localhost): ").strip()
        if not server_ip:
            server_ip = 'localhost'
        
        client = SimpleClient(server_ip)
        client.start_chat()
        
    else:
        print("使用方法:")
        print("  python L12_Final.py          # 启动服务器")
        print("  python L12_Final.py client   # 启动测试客户端")


if __name__ == "__main__":
    main()
