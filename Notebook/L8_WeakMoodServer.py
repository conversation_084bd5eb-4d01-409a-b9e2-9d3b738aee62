from fastapi import FastAPI
app = FastAPI()
# 用一个列表来模拟数据库，存储所有心情记录
mood_items = []
# 用一个全局变量来模拟自增ID
mood_counter = 0

@app.post("/moods")
def create_mood(item: dict):
    global mood_counter
    mood_counter += 1

    # 这是一个“定时炸弹”
    new_mood = {
        "id": mood_counter,
        "user": item["user"], # 如果请求里没有"user"键,这里会崩溃!
        "mood": item["mood"], # 如果请求里没有"mood"键,这里也会崩溃!
    }

    mood_items.append(new_mood)
    return new_mood
