#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
async_chat_server.py - Python异步编程：高效的asyncio群聊服务器

这个程序实现了一个基于asyncio的异步群聊服务器，包含以下功能：
1. 支持多客户端同时连接（单线程异步）
2. 群聊功能（消息广播）
3. 客户端连接状态管理
4. 异步非阻塞的网络I/O操作

与多线程版本的区别：
- 使用单线程 + 事件循环，而不是多线程
- 使用async/await语法实现异步操作
- 更高的并发性能，更低的资源消耗

使用方法：
1. 运行此程序作为服务器
2. 客户端使用socket连接到服务器IP的8000端口
3. 多个客户端可以同时连接并进行群聊

作者：根据L13.ipynb内容实现
"""

import asyncio
import sys
import time
from datetime import datetime

class AsyncChatServer:
    def __init__(self, host='0.0.0.0', port=8000):
        """
        初始化异步聊天服务器

        Args:
            host (str): 服务器绑定的IP地址，'0.0.0.0'表示监听所有网络接口
            port (int): 服务器监听的端口号
        """
        self.host = host
        self.port = port
        self.clients = {}  # 存储所有客户端 {writer: address} 的字典
        self.nicknames = {}  # 存储客户端昵称 {writer: nickname} 的字典
        self.nickname_to_writer = {}  # 存储昵称到writer的映射 {nickname: writer}
        self.server = None
        self.running = False
    
    async def broadcast(self, message, source_writer=None):
        """
        异步广播消息给所有在线客户端（除了发送者）
        
        Args:
            message (str): 要广播的消息
            source_writer (StreamWriter): 消息发送者的writer，不会收到广播
        """
        if not self.clients:
            return
            
        # 创建客户端列表的副本，避免在迭代过程中修改字典
        clients_copy = list(self.clients.keys())
        
        # 并发发送消息给所有客户端
        tasks = []
        for writer in clients_copy:
            if writer != source_writer and not writer.is_closing():
                task = self.send_message_to_client(writer, message)
                tasks.append(task)
        
        # 等待所有发送任务完成
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def send_message_to_client(self, writer, message):
        """
        异步发送消息给单个客户端
        
        Args:
            writer (StreamWriter): 客户端的writer对象
            message (str): 要发送的消息
        """
        try:
            writer.write(message.encode('utf-8') + b'\n')
            await writer.drain()  # 确保数据被发送
        except Exception as e:
            # 如果发送失败，说明该客户端已断开，将其移除
            print(f"[错误] 向客户端发送消息失败: {e}")
            await self.remove_client(writer)
    
    async def remove_client(self, writer):
        """
        异步移除客户端

        Args:
            writer (StreamWriter): 要移除的客户端writer
        """
        if writer in self.clients:
            client_address = self.clients[writer]
            nickname = self.nicknames.get(writer, str(client_address))

            # 清理所有相关数据
            del self.clients[writer]
            if writer in self.nicknames:
                old_nickname = self.nicknames[writer]
                del self.nicknames[writer]
                if old_nickname in self.nickname_to_writer:
                    del self.nickname_to_writer[old_nickname]

            print(f"[连接关闭] {nickname} ({client_address}) 已离开。在线人数: {len(self.clients)}")

            # 向其他客户端广播用户离开的消息
            leave_message = f"[系统消息] {nickname} 离开了聊天室"
            await self.broadcast(leave_message, writer)

        if not writer.is_closing():
            try:
                writer.close()
                await writer.wait_closed()
            except:
                pass
    
    async def handle_client(self, reader, writer):
        """
        异步处理单个客户端的所有通信
        
        Args:
            reader (StreamReader): 客户端的reader对象
            writer (StreamWriter): 客户端的writer对象
        """
        # 获取客户端地址信息
        client_address = writer.get_extra_info('peername')
        
        # 将新客户端添加到字典中
        self.clients[writer] = client_address
        
        print(f"[新连接] {client_address} 已加入聊天室。当前在线人数: {len(self.clients)}")
        
        # 向所有其他客户端广播新用户加入的消息
        join_message = f"[系统消息] 用户 {client_address} 加入了聊天室"
        await self.broadcast(join_message, writer)
        
        try:
            while self.running and not writer.is_closing():
                # 异步接收客户端消息
                data = await reader.read(1024)
                if not data:
                    # 如果接收到空数据，说明客户端已断开
                    break
                
                # 解码消息并准备广播
                try:
                    decoded_message = data.decode('utf-8').strip()
                    if decoded_message:
                        # 添加时间戳
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        broadcast_message = f"[{timestamp}] 来自 {client_address} 的消息: {decoded_message}"
                        print(broadcast_message)
                        
                        # 异步广播消息给所有其他客户端
                        await self.broadcast(broadcast_message, writer)
                except UnicodeDecodeError:
                    print(f"[错误] 无法解码来自 {client_address} 的消息")
                    
        except asyncio.CancelledError:
            print(f"[连接中断] {client_address} 的处理任务被取消。")
        except ConnectionResetError:
            print(f"[连接中断] {client_address} 意外断开。")
        except Exception as e:
            print(f"[错误] 处理客户端 {client_address} 时发生错误: {e}")
        finally:
            # 清理工作
            await self.remove_client(writer)
    
    async def start_server(self):
        """
        启动异步聊天服务器
        """
        try:
            # 创建异步服务器
            self.server = await asyncio.start_server(
                self.handle_client,  # 客户端处理函数
                self.host,
                self.port
            )
            
            self.running = True
            
            print(f"[*] 异步聊天服务器正在监听 {self.host}:{self.port}")
            print(f"[*] 等待客户端连接...")
            print(f"[*] 按 Ctrl+C 停止服务器")
            
            # 异步等待服务器运行
            async with self.server:
                await self.server.serve_forever()
                
        except Exception as e:
            print(f"[错误] 启动服务器时发生错误: {e}")
        finally:
            await self.stop_server()
    
    async def stop_server(self):
        """
        停止异步聊天服务器
        """
        print("\n[*] 正在关闭服务器...")
        self.running = False
        
        # 关闭所有客户端连接
        if self.clients:
            tasks = []
            for writer in list(self.clients.keys()):
                tasks.append(self.remove_client(writer))
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # 关闭服务器
        if self.server:
            self.server.close()
            await self.server.wait_closed()
        
        print("[*] 服务器已关闭")


class AsyncSimpleClient:
    """
    简单的异步测试客户端类（用于演示）
    """
    def __init__(self, server_host, server_port=8000):
        self.server_host = server_host
        self.server_port = server_port
        self.reader = None
        self.writer = None
        self.running = False
    
    async def connect_to_server(self):
        """
        异步连接到聊天服务器
        """
        try:
            self.reader, self.writer = await asyncio.open_connection(
                self.server_host, self.server_port
            )
            self.running = True
            print(f"[*] 已连接到服务器 {self.server_host}:{self.server_port}")
            return True
        except Exception as e:
            print(f"[错误] 连接服务器失败: {e}")
            return False
    
    async def receive_messages(self):
        """
        异步接收服务器消息
        """
        try:
            while self.running:
                data = await self.reader.read(1024)
                if not data:
                    break
                message = data.decode('utf-8').strip()
                if message:
                    print(f"\n{message}")
                    print("请输入消息 (输入 'quit' 退出): ", end='', flush=True)
        except Exception as e:
            if self.running:
                print(f"\n[错误] 接收消息失败: {e}")
    
    async def send_message(self, message):
        """
        异步发送消息
        """
        try:
            self.writer.write(message.encode('utf-8'))
            await self.writer.drain()
        except Exception as e:
            print(f"[错误] 发送消息失败: {e}")
    
    async def start_chat(self):
        """
        开始异步聊天
        """
        if not await self.connect_to_server():
            return
        
        print("欢迎来到异步聊天室！")
        print("请输入消息 (输入 'quit' 退出): ")
        
        # 启动接收消息的任务
        receive_task = asyncio.create_task(self.receive_messages())
        
        try:
            while self.running:
                # 在异步环境中获取用户输入比较复杂，这里简化处理
                # 实际应用中可能需要使用aioconsole等库
                message = await asyncio.get_event_loop().run_in_executor(
                    None, input
                )
                
                if message.lower() == 'quit':
                    break
                if message.strip():
                    await self.send_message(message)
                    
        except KeyboardInterrupt:
            pass
        finally:
            await self.disconnect()
            receive_task.cancel()
    
    async def disconnect(self):
        """
        异步断开连接
        """
        self.running = False
        if self.writer:
            try:
                self.writer.close()
                await self.writer.wait_closed()
            except:
                pass
        print("\n[*] 已断开连接")


async def main():
    """
    异步主函数 - 根据命令行参数决定运行服务器还是客户端
    """
    if len(sys.argv) == 1:
        # 默认运行服务器
        print("启动异步聊天服务器...")
        server = AsyncChatServer()
        try:
            await server.start_server()
        except KeyboardInterrupt:
            print("\n[*] 收到中断信号")
        
    elif len(sys.argv) == 2 and sys.argv[1] == 'client':
        # 运行测试客户端
        server_ip = input("请输入服务器IP地址 (默认: localhost): ").strip()
        if not server_ip:
            server_ip = 'localhost'
        
        client = AsyncSimpleClient(server_ip)
        await client.start_chat()
        
    else:
        print("使用方法:")
        print("  python async_chat_server.py          # 启动异步服务器")
        print("  python async_chat_server.py client   # 启动测试客户端")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
