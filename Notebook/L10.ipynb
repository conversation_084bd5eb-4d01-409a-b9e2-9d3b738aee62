{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# 网络与后端开发集成项目：打字速度记录系统"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！欢迎来到我们的终极集成项目。在这个项目中, 我们将把之前所有课程学到的知识融会贯通，从网络基础、HTTP协议、API开发到数据库操作，完整地体验一次真实世界中后端应用的开发流程。\n", "\n", "准备好开始这段编程冒险了吗? 让我们一起探索Python后端开发的奇妙世界！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 项目启动与知识回顾\n", "在开始之前，我们先回顾一下已经掌握的武器库："]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 我们已经掌握了什么？\n", "\n", "- **网络基础**: TCP/IP协议栈、客户端-服务器模型、网络通信基本原理。\n", "- **HTTP协议**: 请求-响应模型、GET/POST等HTTP方法、状态码、请求头与请求体结构。\n", "- **FastAPI框架**: 路由定义(`@app.get`)、请求处理、参数接收、响应生成、数据自动校验。\n", "- **ORM与数据库**: 使用SQLAlchemy定义数据模型、建立数据库连接、实现基本的CRUD操作。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 我们的目标：打字速度记录系统"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**项目愿景**: 打造一个记录打字练习成绩的API系统，功能类似健身应用记录锻炼数据。\n", "\n", "### 核心功能\n", "\n", "1.  **记录成绩**: 每次练习后，可以提交本次的打字速度(WPM)和正确率数据。\n", "2.  **查询历史**: 能够浏览所有历史练习记录，跟踪自己的进步轨迹。\n", "3.  **查看最佳**: 可以一键查询历史最佳成绩，激励自己持续练习。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第一阶段：需求分析 (谋定而后动)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["需求分析就像是画画前先想好要画什么。在开始编写代码前，我们需要把项目的细节想清楚，避免开发过程中的返工。\n", "\n", "### 1. 核心功能拆解\n", "- **创建记录**: 用户完成打字练习后，需要一个功能来提交`WPM`和`正确率`。\n", "- **读取记录列表**: 需要一个功能来展示所有历史成绩，以便跟踪进步。\n", "- **读取最佳记录**: 需要一个功能来查询历史最高分，用于激励。\n", "\n", "### 2. 输入数据规范\n", "当我们**创建记录**时，用户需要提供什么数据？\n", "- `wpm` (每分钟字数): 应该是一个**整数**。\n", "- `accuracy` (正确率): 应该是一个**小数** (例如 0.985)。\n", "\n", "### 3. 输出数据规范\n", "当一条记录被**成功创建**后，API应该返回什么？\n", "- 新创建记录的**完整信息**，这应该包括系统自动生成的`ID`和`创建时间戳`。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第二阶段：API接口设计 (定义系统的“窗口”)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["API是我们程序对外的“窗口”，一个设计良好的API应该清晰易懂。根据我们的需求，可以设计出以下接口：\n", "\n", "- `POST /records/`\n", "  - **功能**: 创建新的打字记录。\n", "  - **接收**: `wpm`和`accuracy`数据。\n", "  - **返回**: 新记录的完整信息。\n", "\n", "- `GET /records/`\n", "  - **功能**: 获取所有打字记录。\n", "  - **返回**: 包含所有历史成绩的列表。\n", "\n", "- `GET /records/best/`\n", "  - **功能**: 获取最佳打字记录。\n", "  - **返回**: 历史最高分的单条记录。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第三阶段：数据库设计 (规划数据的“储物柜”)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["我们需要精心设计数据的存储方式，就像为每条记录安排合适的“家”。对于我们的项目，简洁的设计是最好的：我们只需要**一张表**来存储所有打字记录。\n", "\n", "### `records` 表结构设计\n", "\n", "| 字段名 (Field) | 数据类型 (Type) | 约束 (Constraint) | 说明 (Description) |\n", "|:--------------:|:----------------:|:------------------:|:-------------------|\n", "| `id`           | 整数 (Integer)   | 主键, 自增         | 每条记录的唯一标识符 |\n", "| `wpm`          | 整数 (Integer)   | 非空               | 每分钟字数         |\n", "| `accuracy`     | 浮点数 (Float)   | 非空               | 正确率 (如 0.985)  |\n", "| `created_at`   | 日期时间         | 默认当前时间       | 记录创建的时间戳   |"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第四阶段：项目文件结构 (搭建项目的“骨架”)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["在开始编码前，我们先规划好项目的文件布局。一个清晰的结构能让我们的代码职责分明，易于维护和扩展。这体现了重要的**分层架构**思想。\n", "\n", "我们将项目组织成一个名为`typing_project`的文件夹，内部包含：\n", "\n", "- **`database.py`**: **数据库连接层** - 负责所有与数据库连接相关的配置。\n", "- **`models.py`**: **ORM模型层** - 定义数据库表的Python类映射。\n", "- **`schemas.py`**: **数据格式层** - 使用Pydantic定义API请求和响应的数据结构规范。\n", "- **`crud.py`**: **数据访问层 (CRUD)** - 包含所有直接操作数据库的函数（增删改查）。\n", "- **`main.py`**: **API接口层** - 程序的主入口，负责处理HTTP请求和响应，调用`crud.py`中的函数来完成业务逻辑。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 第五阶段：编码实现 (从蓝图到现实)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 1. 定义数据库模型 `models.py`\n", "我们使用SQLAlchemy ORM将设计的`records`表转换为Python类。\n", "```python\n", "# typing_project/models.py\n", "from sqlalchemy import Column, Integer, Float, DateTime\n", "from sqlalchemy.sql import func\n", "from .database import Base # 从同级目录的database.py导入Base\n", "\n", "class TypingRecord(Base):\n", "    __tablename__ = \"records\"\n", "\n", "    id = Column(Integer, primary_key=True, index=True)\n", "    wpm = Column(Integer, nullable=False)\n", "    accuracy = Column(Float, nullable=False)\n", "    # server_default=func.now()让数据库在创建记录时自动填写当前时间\n", "    created_at = Column(DateTime(timezone=True), server_default=func.now())\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 2. 定义数据格式 `schemas.py`\n", "我们使用Pydantic为API的输入和输出创建清晰的数据结构规范。\n", "```python\n", "# typing_project/schemas.py\n", "from pydantic import BaseModel\n", "from datetime import datetime\n", "\n", "# 创建记录时，用户只需要提供这两个字段\n", "class RecordCreate(BaseModel):\n", "    wpm: int\n", "    accuracy: float\n", "\n", "# API响应时，需要返回完整的记录信息\n", "class RecordResponse(BaseModel):\n", "    id: int\n", "    wpm: int\n", "    accuracy: float\n", "    created_at: datetime\n", "\n", "    class Config:\n", "        orm_mode = True # 允许模型从ORM对象（如TypingRecord）直接转换\n", "        from_attributes = True # V2推荐，和上面是一个功能\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 3. 配置数据库连接 `database.py`\n", "配置SQLite数据库连接和会话管理工具。\n", "```python\n", "# typing_project/database.py\n", "from sqlalchemy import create_engine\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy.orm import sessionmaker\n", "\n", "# SQLite数据库URL：sqlite:///./typing_records.db\n", "# 解释：sqlite（协议）+ ://（分隔符）+ 空主机 + ./typing_records.db（相对路径）\n", "SQLALCHEMY_DATABASE_URL = \"sqlite:///./typing_records.db\"\n", "\n", "# connect_args参数对SQLite是必需的，允许多线程环境下的并发访问\n", "engine = create_engine(\n", "    SQLALCHEMY_DATABASE_URL, \n", "    connect_args={\"check_same_thread\": False}\n", ")\n", "\n", "SessionLocal = sessionmaker(bind=engine)\n", "Base = declarative_base()\n", "```"]}, {"cell_type": "markdown", "id": "06400543", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### URL 的通用结构\n", "首先，我们需要理解标准URL（统一资源定位符）的结构。它的通用格式是：\n", "\n", "```bash\n", "scheme:[//[user:password@]host[:port]][/]path[?query][#fragment]\n", "```\n", "对于数据库连接，我们只需要关注简化版：\n", "```bash\n", "scheme://host/path\n", "```\n", "- `scheme`: 协议类型，如 http、ftp、https、sqlite。它告诉应用程序如何处理这个连接。\n", "- `://`: 标准语法分隔符，用来分隔协议和主机部分。\n", "- `host`: 主机名或IP地址，指向资源所在的服务器。例如 `www.google.com` 或 `127.0.0.1`。\n", "- `/path`: 资源在主机上的具体路径，例如 `/images/logo.png`。"]}, {"cell_type": "markdown", "id": "76d0343f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 将URL标准应用到SQLite\n", "\n", "关键问题：SQLite不是网络服务器，而是本地文件。它没有host、port或user信息。那么，如何构建符合标准的SQLite URL呢？\n", "\n", "这就是为什么会出现 `///` 的原因。让我们逐步构建SQLite的URL：\n", "\n", "**Scheme**: 很简单，就是 `sqlite`。这告诉SQLAlchemy：“请使用SQLite数据库驱动。”\n", "\n", "> sqlite:\n", "\n", "**2. Host（主机）**: SQLite是本地文件，没有网络主机，所以host部分为空。但URL标准要求保留 `://` 分隔符。\n", "\n", "```\n", "sqlite:// + (空主机名)\n", "```"]}, {"cell_type": "markdown", "id": "0f522cf4", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**3. Path（路径）**: 指定数据库文件在文件系统中的位置。\n", "\n", "**绝对路径示例**：\n", "- 文件位置：`/var/data/my_app.db`\n", "- URL构建：`sqlite://` + (空主机) + `/`（分隔符） + `/var/data/my_app.db`\n", "- 最终结果：`sqlite:////var/data/my_app.db`\n", "\n", "**相对路径示例**（我们项目中使用的）：\n", "- 文件位置：`./typing_records.db`（当前目录下）\n", "- URL构建：`sqlite://` + (空主机) + `/`（分隔符） + `./typing_records.db`\n", "- 最终结果：`sqlite:///./typing_records.db`\n", "\n", "这就解释了为什么SQLite URL中会出现三个斜杠 `///`：第一个斜杠分隔协议，第二个斜杠表示空主机，第三个斜杠开始文件路径。"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 4. 编写数据库操作 `crud.py`\n", "这是我们的“数据管家”，所有与数据库的直接交互都封装在这里。\n", "\n", "```python\n", "# typing_project/crud.py\n", "from sqlalchemy.orm import Session\n", "from sqlalchemy import desc\n", "from . import models, schemas\n", "\n", "def create_record(db: Session, record: schemas.RecordCreate):\n", "    db_record = models.TypingRecord(**record.model_dump())\n", "    db.add(db_record)\n", "    db.commit()\n", "    db.refresh(db_record)\n", "    return db_record\n", "```"]}, {"cell_type": "markdown", "id": "e4bfd307", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["```python\n", "def get_records(db: Session):\n", "    return db.query(models.TypingRecord).all()\n", "\n", "def get_best_record(db: Session):\n", "    return db.query(models.TypingRecord).order_by(desc(models.TypingRecord.wpm)).first()\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 5. 组装API服务 `main.py`\n", "这是我们应用的“总指挥中心”，负责接收请求、调用CRUD函数并返回响应。\n", "```python\n", "# typing_project/main.py\n", "from fastapi import FastAPI, Depends, HTTPException\n", "from sqlalchemy.orm import Session\n", "from typing import List\n", "from . import crud, models, schemas, database\n", "\n", "# 创建数据库表\n", "models.Base.metadata.create_all(bind=database.engine)\n", "\n", "app = FastAPI(title=\"打字速度记录系统 API\")\n", "\n", "# 依赖项函数\n", "def get_db():\n", "    db = database.SessionLocal()\n", "    try: \n", "        yield db\n", "    finally: \n", "        db.close()\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### `main.py` (Part 2 - Endpoints)\n", "```python\n", "# ... (接上页的代码) ...\n", "\n", "@app.post(\"/records/\", response_model=schemas.RecordResponse, tags=[\"打字记录\"])\n", "def create_new_record(record: schemas.RecordCreate, db: Session = Depends(get_db)):\n", "    return crud.create_record(db=db, record=record)\n", "```"]}, {"cell_type": "markdown", "id": "5248704e", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### `main.py` (Part 2 - Endpoints)\n", "\n", "```python\n", "@app.get(\"/records/\", response_model=List[schemas.RecordResponse], tags=[\"打字记录\"])\n", "def read_all_records(db: Session = Depends(get_db)):\n", "    return crud.get_records(db)\n", "```"]}, {"cell_type": "markdown", "id": "1aa337d2", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### `main.py` (Part 2 - Endpoints)\n", "\n", "```python\n", "@app.get(\"/records/best/\", response_model=schemas.RecordResponse, tags=[\"打字记录\"])\n", "def read_best_record(db: Session = Depends(get_db)):\n", "    best_record = crud.get_best_record(db)\n", "    if best_record is None:\n", "        raise HTTPException(status_code=404, detail=\"数据库中没有任何记录\")\n", "    return best_record\n", "```"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 见证奇迹的时刻：运行你的第一个后端服务！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["1.  **启动服务**:\n", "    - 确保你的命令行终端位于`typing_project`文件夹的**上一级**目录。\n", "    - 运行命令: `uvicorn typing_project.main:app --reload`\n", "\n", "2.  **探索自动生成的API文档**:\n", "    - 服务器运行后，打开浏览器。\n", "    - 访问 `http://127.0.0.1:8000/docs`。\n", "    - 你会看到一个漂亮的、交互式的API文档，里面详细列出了我们刚刚编写的三个API接口。\n", "\n", "3.  **在文档中直接测试**:\n", "    - 尝试使用`POST /records/`接口，创建一个新的打字记录。\n", "    - 然后使用`GET /records/`接口，看看是否能查到你刚刚创建的记录。\n", "    - 最后，用`GET /records/best/`来验证最佳记录功能！"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 课程总结\n", "\n", "在这节课中，我们完成了一个小型但完整的后端项目的全过程，从需求分析到最终的编码实现。通过这个实践，我们巩固了前几节课学到的所有核心知识，并体验了真实的后端开发流程，为未来的学习和更复杂的项目打下了坚实的基础。恭喜大家！"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}