{"cells": [{"cell_type": "markdown", "id": "270e8ba7", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<style>\n", "/* 清除 body 的 margin，防止被伪元素影响布局 */\n", "body {\n", "    margin: 0;\n", "}\n", "\n", "/* 完全清除 body::before 任何默认行为 */\n", "body::before {\n", "    all: unset;\n", "    content: \"\";\n", "    background: url('https://i.postimg.cc/s2ZMPzBM/temp-Image9i-KMM0.avif') no-repeat;\n", "    background-size: contain;\n", "    position: fixed;\n", "    top: 32px;\n", "    right: 32px;\n", "    width: 256px;\n", "    height: 77px;\n", "    z-index: 999999;\n", "    pointer-events: none;\n", "    opacity: 0.9;\n", "    display: block;\n", "}\n", "\n", "/* 控制 slide 中图片最大高度 */\n", ".reveal .slides img {\n", "    max-height: 420px;\n", "    width: auto;\n", "}\n", "\n", "/* 顶部进度条 */\n", ".reveal .progress {\n", "    position: absolute;\n", "    top: 0;\n", "    left: 0;\n", "    bottom: auto;\n", "    width: 100%;\n", "    height: 5px;\n", "    background: #4A8FD3;\n", "    z-index: 9999;\n", "}\n", "\n", ".reveal .progress span {\n", "    display: block;\n", "    height: 5px;\n", "    transition: width 0.2s ease;\n", "    background: rgb(255, 227, 113);\n", "}\n", "\n", "/* 给 SVG 箭头图标染色（用 filter 实现） */\n", ".reveal .navigate-left,\n", ".reveal .navigate-right,\n", ".reveal .navigate-up,\n", ".reveal .navigate-down {\n", "    filter: invert(23%) sepia(100%) saturate(1533%) hue-rotate(198deg) brightness(90%) contrast(89%);\n", "}\n", "\n", "</style>\n", "\n", "# Python数据可视化与算法: 汇率趋势大揭秘"]}, {"cell_type": "markdown", "id": "c99ae401", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["同学们好！今天我们不仅要学习如何用酷炫的图表把数据“画”出来，还要更进一步，像真正的金融分析师一样，学习解读API文档，并用算法来发现汇率数据中隐藏的秘密！"]}, {"cell_type": "markdown", "id": "84e776ef", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 课程目标\n", "\n", "*   **工程师必备技能**: 学会如何阅读官方文档来理解任何一个新API的用法。\n", "*   **数据可视化**: 掌握使用`matplotlib`库，将枯燥的数字变成直观的趋势图。\n", "*   **算法思维启蒙**: 学习用**暴力搜索**和**贪心算法**等策略，解决有趣的金融问题。"]}, {"cell_type": "markdown", "id": "57b2f149", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 工程师的必备技能：如何阅读API文档\n", "\n", "API就像一个神秘的魔法盒子，你只有阅读它的“使用说明书”——也就是**API文档**——才能知道如何施展它的魔法。学会阅读文档，你就能解锁互联网上无数个API的超能力！"]}, {"cell_type": "markdown", "id": "5675b931", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第一步：找到API的“大本营” - 基础地址 (Base URL)\n", "\n", "每个API都有一个“大本营”地址，我们所有的请求都要从这里出发。在`frankfurter`的文档中，我们可以找到它的公共API地址。\n", "\n", "> The public API is available at `api.frankfurter.dev`.\n", "\n", "在文档的例子中，我们还看到了`/v1/`这个部分，这代表API的“版本1”。所以，我们的基础地址是：\n", "\n", "**`https://api.frankfurter.dev/v1/`**\n", "\n", "记住这个地址，我们所有的请求都将基于它来构建！"]}, {"cell_type": "markdown", "id": "84e73c0c", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第二步：选择不同的“功能门” - 端点 (Endpoints)\n", "\n", "API的不同功能就像大本营里的不同房间，每个房间都有一个“门牌号”，这就是**端点**。我们把它接在基础地址后面，来告诉API我们想用哪个功能。\n", "\n", "- **获取最新汇率**: `/latest`\n", "  - *完整路径: `.../latest`*\n", "- **获取历史汇率**: `/<date>` (这里的`<date>`是一个**占位符**，需要我们用真实日期替换，比如`/2023-01-01`)\n", "  - *完整路径: `.../1999-01-04`*\n", "- **获取时间段汇率**: `/<start_date>..<end_date>` (用`..`连接开始和结束日期)\n", "  - *完整路径: `.../2024-01-01..2024-06-30`*"]}, {"cell_type": "markdown", "id": "7216874c", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第三步：提交“详细要求” - 查询参数 (Parameters)\n", "\n", "选好了功能门，我们还需要告诉API一些更具体的要求，比如“我想要美元兑换人民币的汇率”。这些详细要求就是**查询参数**。\n", "\n", "**语法规则：**\n", "- 以 `?` 开始，接在端点后面。\n", "- 多个参数之间用 `&` 连接。\n", "\n", "**常用参数：**\n", "- `base=...` 或 `from=...` : 指定你的源货币 (e.g., `base=USD`)。\n", "- `symbols=...` 或 `to=...` : 指定你想要的目标货币，多个用逗号隔开 (e.g., `symbols=CNY,JPY`)。"]}, {"cell_type": "markdown", "id": "c70428a6", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第四步：组装一个完整的API请求！\n", "\n", "现在，让我们把学到的所有部分组装起来，构造一个我们课程中会用到的请求：**获取从2024年1月1日至今，人民币(CNY)兑日元(JPY)的历史汇率。**\n", "\n", "1.  **基础地址**: `https://api.frankfurter.dev/v1/`\n", "2.  **功能门 (端点)**: `2024-01-01..` (注意，结束日期留空代表“至今”)\n", "3.  **详细要求 (参数)**: `?from=CNY` 和 `&to=JPY`\n", "\n", "**最终的URL地址就是：**\n", "\n", "`https://api.frankfurter.dev/v1/2024-01-01..?from=CNY&to=JPY`\n", "\n", "看，我们已经像专业工程师一样，通过阅读文档，自己拼装出了一个复杂的API请求！"]}, {"cell_type": "markdown", "id": "43d0c52d", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 实践一：绘制历史汇率趋势图"]}, {"cell_type": "markdown", "id": "7764f16f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第一步：获取数据的小帮手\n", "\n", "我们要先编写一个函数，它的任务是根据我们给定的**时间范围**和**货币对**，去API那里取回历史数据。这就像派出一个机器人助手，让它帮我们整理好需要的资料。"]}, {"cell_type": "markdown", "id": "258d3d07", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (函数定义):**\n", "```python\n", "import requests\n", "from datetime import date, timedelta\n", "\n", "# 定义一个函数，专门用来获取历史汇率\n", "def get_historical_rates(start_date, end_date, from_curr, to_curr):\n", "    # 使用 f-string 动态构建API的URL地址\n", "    url = f\"https://api.frankfurter.app/{start_date}..{end_date}?from={from_curr}&to={to_curr}\"\n", "    \n", "    # 使用 try-except 保证程序在网络错误时不会崩溃\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status() # 检查请求是否成功\n", "        data = response.json()\n", "```"]}, {"cell_type": "markdown", "id": "d5feae88", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (数据处理与返回):**\n", "```python\n", "        # (接上页)\n", "        # 安全地获取汇率历史记录\n", "        rates_history = data.get('rates', {})\n", "        if not rates_history: # 如果没有数据，就返回空\n", "            return None, None\n", "            \n", "        # 把日期排序，确保我们的图表是按时间顺序画的\n", "        dates = sorted(rates_history.keys())\n", "        # 提取每个日期对应的汇率值\n", "        rates = [rates_history[d][to_curr] for d in dates]\n", "        return dates, rates\n", "        \n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"获取历史汇率失败: {e}\")\n", "        return None, None\n", "```"]}, {"cell_type": "markdown", "id": "01dd5135", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第二步：绘制图表的魔法师\n", "\n", "数据拿到手了！现在轮到我们的“魔法师”——`matplotlib`库登场了。它能把这一堆日期和数字，变成一目了然的折线图。"]}, {"cell_type": "code", "execution_count": 2, "id": "2a257cf5", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"image/png": "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******************************************************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", "text/plain": ["<Figure size 1500x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 以下是完整的可执行代码\n", "import requests\n", "import matplotlib.pyplot as plt\n", "from datetime import date, timedelta\n", "\n", "def get_historical_rates(start_date, end_date, from_curr, to_curr):\n", "    url = f\"https://api.frankfurter.app/{start_date}..{end_date}?from={from_curr}&to={to_curr}\"\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status()\n", "        data = response.json()\n", "        rates_history = data.get('rates', {})\n", "        if not rates_history:\n", "            return None, None\n", "        dates = sorted(rates_history.keys())\n", "        rates = [rates_history[date][to_curr] for date in dates]\n", "        return dates, rates\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"获取历史汇率失败: {e}\")\n", "        return None, None\n", "\n", "# 动态获取过去30天的数据 (人民币兑日元)\n", "today = date.today()\n", "one_month_ago = today - <PERSON><PERSON><PERSON>(days=30)\n", "dates, rates = get_historical_rates(one_month_ago, today, \"CNY\", \"JPY\")\n", "\n", "if dates and rates:\n", "    plt.figure(figsize=(15, 6))\n", "    plt.plot(dates, rates, marker='.', linestyle='-')\n", "    plt.title(f\"CNY to JPY Exchange Rate Trend (Last 30 Days)\")\n", "    plt.xlabel(\"Date\")\n", "    plt.ylabel(\"Rate (JPY per CNY)\")\n", "    plt.grid(True)\n", "    plt.gca().xaxis.set_major_locator(plt.MaxNLocator(10))\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "67e110ae", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示 (调用函数与绘图):**\n", "```python\n", "import matplotlib.pyplot as plt\n", "# 动态获取过去30天的数据 (人民币兑日元)\n", "today = date.today()\n", "one_month_ago = today - <PERSON><PERSON><PERSON>(days=30)\n", "dates, rates = get_historical_rates(one_month_ago, today, \"CNY\", \"JPY\")\n", "\n", "# 确认我们成功获取到了数据再进行绘图\n", "if dates and rates:\n", "    plt.figure(figsize=(15, 6)) # 创建一个大一点的画布\n", "    plt.plot(dates, rates, marker='.', linestyle='-') # 绘制折线图\n", "    plt.title(f\"CNY to JPY Exchange Rate (Last 30 Days)\") # 标题\n", "    plt.xlabel(\"Date\") # X轴标签\n", "    plt.ylabel(\"Rate (JPY per CNY)\") # Y轴标签\n", "    plt.grid(True) # 显示网格线\n", "    plt.gca().xaxis.set_major_locator(plt.MaxNLocator(10)) # 减少X轴标签数量，避免重叠\n", "    plt.show() # 展示图表！\n", "```"]}, {"cell_type": "markdown", "id": "91a8173c", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 练习与挑战"]}, {"cell_type": "markdown", "id": "3cc8b0f6", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 练习1: 我的命令行汇率计算器 (复习)\n", "*   **任务**: 巩固API请求和用户交互，编写一个Python小程序，提示用户输入源货币和目标货币代码，然后调用API获取最新汇率并打印。"]}, {"cell_type": "code", "execution_count": 2, "id": "e7a0a566", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "查询结果：1 CNY = 20.561 JPY\n"]}], "source": ["def interactive_exchange_rate_checker():\n", "    # 从用户那里获取输入，并转换为大写\n", "    from_currency = input(\"请输入源货币代码 (例如 CNY): \").upper()\n", "    to_currency = input(\"请输入目标货币代码 (例如 JPY): \").upper()\n", "    \n", "    # 构建API请求URL\n", "    url = f\"https://api.frankfurter.app/latest?from={from_currency}&to={to_currency}\"\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status()\n", "        data = response.json()\n", "        # 从返回的字典中解析出汇率\n", "        rate = data['rates'][to_currency]\n", "        print(f\"\\n查询结果：1 {from_currency} = {rate} {to_currency}\")\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"\\n请求失败: {e}\")\n", "    except KeyError: # 如果用户输入的货币代码有误，会触发这个错误\n", "        print(\"\\n无法解析返回的数据，请确认输入的货币代码是否正确。\")\n", "\n", "interactive_exchange_rate_checker() # 取消注释来运行"]}, {"cell_type": "markdown", "id": "3357dc95", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "def interactive_exchange_rate_checker():\n", "    from_currency = input(\"源货币 (e.g., CNY): \").upper()\n", "    to_currency = input(\"目标货币 (e.g., JPY): \").upper()\n", "    \n", "    url = f\"https://api.frankfurter.app/latest?from={from_currency}&to={to_currency}\"\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status()\n", "        data = response.json()\n", "        rate = data['rates'][to_currency]\n", "        print(f\"\\n1 {from_currency} = {rate} {to_currency}\")\n", "    except Exception as e:\n", "        print(f\"\\n查询失败: {e}\")\n", "\n", "interactive_exchange_rate_checker()\n", "```"]}, {"cell_type": "markdown", "id": "47dc6b0c", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 练习2: 寻找历史最高点\n", "*   **任务**: 何时用人民币换日元最划算？练习处理时间序列数据，使用`datetime`模块动态生成日期，并从数据集中寻找**最高值**。"]}, {"cell_type": "code", "execution_count": 3, "id": "a86ff7ba", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "正在查询 CNY 到 JPY 在过去3年的历史max值...\n", "查询完成：过去 3 年内, CNY兑JPY的历史max值为 22.1830，出现在 2024-07-01。\n"]}], "source": ["def find_historical_extreme(years, from_curr, to_curr, mode='max'):\n", "    # 使用datetime计算N年前的日期\n", "    today = date.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=years*365)\n", "    \n", "    print(f\"\\n正在查询 {from_curr} 到 {to_curr} 在过去{years}年的历史{mode}值...\")\n", "    # 复用我们之前写的函数来获取数据\n", "    dates, rates = get_historical_rates(start_date, today, from_curr, to_curr)\n", "\n", "    if not rates: # 如果没有获取到数据\n", "        print(\"未能获取到任何历史汇率数据。\")\n", "        return\n", "    \n", "    # 使用 Python 内置的 max() 或 min() 函数找到极值\n", "    extreme_rate = max(rates) if mode == 'max' else min(rates)\n", "    # 使用 .index() 方法找到极值在列表中的位置\n", "    extreme_day_index = rates.index(extreme_rate)\n", "    # 根据位置，从日期列表中找到对应的日期\n", "    extreme_date = dates[extreme_day_index]\n", "    \n", "    print(f\"查询完成：过去 {years} 年内, {from_curr}兑{to_curr}的历史{mode}值为 {extreme_rate:.4f}，出现在 {extreme_date}。\")\n", "\n", "find_historical_extreme(3, 'CNY', 'JPY', mode='max')"]}, {"cell_type": "markdown", "id": "b371936f", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "def find_historical_extreme(years, from_curr, to_curr, mode='max'):\n", "    today = date.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=years*365)\n", "    \n", "    print(f\"正在查询过去{years}年的历史{mode}值...\")\n", "    dates, rates = get_historical_rates(start_date, today, from_curr, to_curr)\n", "\n", "    if not rates: return\n", "    \n", "    # 使用 max() 或 min() 找到极值\n", "    extreme_rate = max(rates) if mode == 'max' else min(rates)\n", "    # 使用 .index() 找到极值的位置，从而找到对应的日期\n", "    extreme_date = dates[rates.index(extreme_rate)]\n", "    \n", "    print(f\"历史{mode}值为 {extreme_rate:.4f}，出现在 {extreme_date}。\")\n", "\n", "find_historical_extreme(3, 'CNY', 'JPY', mode='max')\n", "```"]}, {"cell_type": "markdown", "id": "aefd2977", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 练习3: 三角套利机会搜寻器\n", "\n", "**挑战**: 真实世界的外汇市场效率很高，套利机会转瞬即逝。我们不再模拟，而是编写一个程序，在多种主要货币中**主动搜索**可能存在的三角套利机会。"]}, {"cell_type": "markdown", "id": "fc6debc9", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第一步：获取汇率的函数\n", "\n", "首先我们需要一个简单的函数，输入两个货币代码，通过API获取它们之间的汇率。这个函数不需要缓存，保持简单。\n", "\n", "```python\n", "import requests\n", "\n", "def get_rate(from_curr, to_curr):\n", "    url = f\"https://api.frankfurter.app/latest?from={from_curr}&to={to_curr}\"\n", "    response = requests.get(url)\n", "    data = response.json()\n", "    return data['rates'][to_curr]\n", "\n", "# 测试函数\n", "rate = get_rate('USD', 'CNY')\n", "print(f\"1 USD = {rate} CNY\")\n", "```"]}, {"cell_type": "markdown", "id": "step2_permutation", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第二步：使用permutation生成三角套利路径\n", "\n", "现在我们要用`itertools.permutations`来生成所有可能的三种货币组合，然后调用第一步的函数获取三个汇率，计算最终金额。\n", "\n", "```python\n", "from itertools import permutations\n", "\n", "def calculate_arbitrage(currencies):\n", "    c1, c2, c3 = currencies\n", "    \n", "    # 获取三段汇率\n", "    rate1 = get_rate(c1, c2)  # c1 -> c2\n", "    rate2 = get_rate(c2, c3)  # c2 -> c3  \n", "    rate3 = get_rate(c3, c1)  # c3 -> c1\n", "    \n", "    # 计算最终金额\n", "    final_amount = 1 * rate1 * rate2 * rate3\n", "    return final_amount\n", "\n", "# 测试一个三角套利路径\n", "test_path = ['USD', 'EUR', 'JPY']\n", "result = calculate_arbitrage(test_path)\n", "print(f\"路径 {' -> '.join(test_path)} -> {test_path[0]}\")\n", "print(f\"最终金额: {result:.6f}\")\n", "```"]}, {"cell_type": "markdown", "id": "step3_try_except", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第三步：引入try-except错误处理\n", "\n", "万一输错了货币代码或者网络出问题会报错，所以我们需要增加try和except来处理异常，还要用all()函数检查所有汇率都获取成功。\n", "\n", "```python\n", "def get_rate_safe(from_curr, to_curr):\n", "    try:\n", "        url = f\"https://api.frankfurter.app/latest?from={from_curr}&to={to_curr}\"\n", "        response = requests.get(url, timeout=5)\n", "        response.raise_for_status()\n", "        data = response.json()\n", "        return data['rates'][to_curr]\n", "    except:\n", "        return None  # 如果出错就返回None\n", "\n", "def calculate_arbitrage_safe(currencies):\n", "    c1, c2, c3 = currencies\n", "    rates = [get_rate_safe(c1, c2), get_rate_safe(c2, c3), get_rate_safe(c3, c1)]\n", "    \n", "    if not all(rates):  # 检查是否所有汇率都获取成功\n", "        return None\n", "    \n", "    return rates[0] * rates[1] * rates[2]\n", "```"]}, {"cell_type": "markdown", "id": "step4_find_best", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第四步：找到当天最大的套利机会\n", "\n", "现在我们要遍历所有可能的三种货币组合，找到当天amount最大的那个组合，并打印出最佳的套利路径。\n", "\n", "```python\n", "def find_best_arbitrage_today():\n", "    currencies = ['USD', 'EUR', 'JPY', 'GBP', 'CNY']\n", "    best_amount = 0\n", "    best_path = None\n", "    \n", "    print(\"正在搜索今天的最佳套利机会...\")\n", "    \n", "    for path in permutations(currencies, 3):\n", "        amount = calculate_arbitrage_safe(path)\n", "        if amount and amount > best_amount:\n", "            best_amount = amount\n", "            best_path = path\n", "    \n", "    if best_amount > 1.0001:  # 如果有超过0.01%的利润\n", "        profit = (best_amount - 1) * 100\n", "        print(f\"最佳路径: {' -> '.join(best_path)} -> {best_path[0]}\")\n", "        print(f\"理论利润: {profit:.4f}%\")\n", "    else:\n", "        print(\"今天没有发现明显的套利机会\")\n", "```"]}, {"cell_type": "markdown", "id": "step5_homework", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 第五步：作业\n", "\n", "增加一个参数，找到过去7天里面三角套利的最佳机会，并给出是哪一天的哪个方案。\n", "\n", "**作业1**: 历史三角套利机会搜索\n", "```python\n", "def find_best_arbitrage_history(days=7):\n", "    # 获取过去7天的数据\n", "    # 对每一天都进行三角套利搜索\n", "    # 返回最佳的日期和路径\n", "    pass\n", "```\n", "\n", "**作业2**: 单次交易最大利润\n", "```python\n", "def max_profit_single_trade(rates):\n", "    # 如果过去30天只能做一次买入卖出\n", "    # 可以赚多少钱？\n", "    pass\n", "```"]}, {"cell_type": "markdown", "id": "7e62dce0", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 练习4: 贪心算法 - 多次交易的最大收益\n", "**升级挑战**: 如果我们**可以进行无限次交易**（即今天买，明天就可以卖，然后再买再卖），如何计算最大总收益？\n", "\n", "这是一个经典的算法问题，它的解法体现了**贪心算法(Greedy Algorithm)**的智慧。"]}, {"cell_type": "markdown", "id": "b366124d", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["### 贪心策略深度解析\n", "对于“可以多次交易”的规则，贪心策略变得异常简单和强大。我们可以把它想象成**爬山**：\n", "\n", "*   **目标**: 获得从山脚到山顶的所有**垂直爬升高度**。\n", "*   **策略**: 我们只需要在山的每一段**上坡路**都向上爬即可。我们不需要关心下坡路，因为我们可以在下坡前“卖掉”（休息），然后在下一个上坡路的起点再“买入”（继续爬）。\n", "*   **结论**: 将所有上坡路段的高度加起来，就是我们能获得的总爬升高度，也就是我们的**最大总利润**。\n", "\n", "**代码实现**: 遍历整个价格数组，只要明天的价格 `prices[i+1]` 比今天 `prices[i]` 高，我们就累加这个**利润差** `prices[i+1] - prices[i]`。"]}, {"cell_type": "code", "execution_count": 3, "id": "976e59a7", "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 贪心算法：多次交易最大收益分析 (CNY->JPY) ---\n", "分析周期: 2025-06-17 到 2025-07-16\n", "如果可以无限次交易，理论上可以捕获的总利润点数(差价之和)为: 1.3353\n", "基于初始汇率 20.1410，10,000 CNY 的本金最终可增值为: 10,662.98 CNY\n"]}], "source": ["def calculate_max_profit_greedy(prices):\n", "    if not prices or len(prices) < 2:\n", "        return 0\n", "    \n", "    total_profit = 0\n", "    # 遍历价格列表，但不包括最后一个元素，因为需要和后一个元素比较\n", "    for i in range(len(prices) - 1):\n", "        # 如果明天的价格比今天高，这就是一个“上坡路”\n", "        if prices[i+1] > prices[i]:\n", "            # 我们就“抓住”这段利润\n", "            profit = prices[i+1] - prices[i]\n", "            total_profit += profit\n", "            \n", "    return total_profit\n", "\n", "print(\"\\n--- 贪心算法：多次交易最大收益分析 (CNY->JPY) ---\")\n", "# 复用之前获取的过去30天的CNY->JPY汇率数据 (变量`rates`)\n", "if rates:\n", "    # 假设汇率数组代表了人民币的“购买力”，我们要计算购买力的总增长\n", "    # 注意：这里计算的是一个相对增长值，而不是绝对金额\n", "    total_profit_points = calculate_max_profit_greedy(rates)\n", "    \n", "    print(f\"分析周期: {dates[0]} 到 {dates[-1]}\")\n", "    print(f\"如果可以无限次交易，理论上可以捕获的总利润点数(差价之和)为: {total_profit_points:.4f}\")\n", "    \n", "    # 演示：如果初始投入10000 CNY，最终能变成多少\n", "    initial_principal = 10000\n", "    final_value = initial_principal + (initial_principal / rates[0] * total_profit_points)\n", "    print(f\"基于初始汇率 {rates[0]:.4f}，10,000 CNY 的本金最终可增值为: {final_value:,.2f} CNY\")\n", "else:\n", "    print(\"没有历史汇率数据可供分析。\")"]}, {"cell_type": "markdown", "id": "4c658624", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["**代码展示：**\n", "```python\n", "def calculate_max_profit_greedy(prices):\n", "    if not prices or len(prices) < 2:\n", "        return 0\n", "    \n", "    total_profit = 0\n", "    for i in range(len(prices) - 1):\n", "        # 贪心策略：只要明天比今天价格高，就累加这个利润\n", "        if prices[i+1] > prices[i]:\n", "            total_profit += prices[i+1] - prices[i]\n", "            \n", "    return total_profit\n", "\n", "if rates:\n", "    total_profit_points = calculate_max_profit_greedy(rates)\n", "    print(f\"可捕获的总利润点数为: {total_profit_points:.4f}\")\n", "```"]}, {"cell_type": "markdown", "id": "8946e31a", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## 总结\n", "\n", "今天，我们像真正的工程师和数据分析师一样，完成了从**获取数据**、**理解数据**到**用算法挖掘数据价值**的全过程！\n", "\n", "- 我们学会了**阅读API文档**，这是解锁网络数据的钥匙。\n", "- 我们用`matplotlib`让冰冷的数字**\"开口说话\"**。\n", "- 我们用**算法**在数据中寻找别人看不到的**机会**。\n", "\n", "编程的魔力就是这样，它能把你的想法变成解决实际问题的强大工具。继续探索吧，未来的数据大师们！"]}], "metadata": {"kernelspec": {"display_name": "XFrame", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}