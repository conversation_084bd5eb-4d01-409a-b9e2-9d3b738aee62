# main.py
from fastapi import FastAPI

# 1. 创建一个FastAPI实例，就像创建了一个应用对象
app = FastAPI()

# 2. 创建一个GET端点
# @app.get("/") 是一个“装饰器”，它告诉FastAPI：
# “如果有人用GET方法访问根路径'/'，请执行紧跟在下面的这个函数”
@app.get("/")
def read_root():
    return {"message": "你好，世界!"}

# 3. 创建另一个带参数的GET端点 (这里纠正PPT中的错误，应为GET)
# 这个装饰器告诉FastAPI，处理对'/greet'路径的GET请求
@app.get("/greet")
# (name: str = "同学") 定义了一个名为name的查询参数
# 它的类型是字符串，默认值是“同学”
def greet_user(name: str = "同学"):
    return {"message": f"你好, {name}!"}
