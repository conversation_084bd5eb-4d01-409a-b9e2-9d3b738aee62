"""
L9_Final.py - 完整的心情记录API with SQLAlchemy数据持久化
"""

from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from pydantic import BaseModel
from typing import Optional, List
import datetime

# ==================== 数据库配置 (database.py 内容) ====================

# 数据库文件的路径。./moods.db 表示在当前目录下创建一个名为 moods.db 的文件
DATABASE_URL = "sqlite:///./moods.db"

# 创建一个数据库"引擎"(Engine)，它是SQLAlchemy与数据库沟通的核心接口
engine = create_engine(
    DATABASE_URL, 
    # connect_args 是SQLite特有的配置，用于允许多线程共享连接
    connect_args={"check_same_thread": False}
)

# 创建一个Base类，我们之后所有的模型都要继承自它
Base = declarative_base()

# 创建一个会话"工厂"，它知道如何连接到我们的数据库引擎
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 这就是我们的依赖项
def get_db():
    db = SessionLocal()  # 创建一个会话实例
    try:
        yield db  # 将会话实例提供给API函数使用
    finally:
        db.close()  # 确保无论如何，会话最终都会被关闭

# ==================== 数据模型 (models.py 内容) ====================

class Mood(Base):
    __tablename__ = "moods"  # 这个模型对应的数据库表名

    # 定义表的列(字段)
    id = Column(Integer, primary_key=True, index=True)  # id, 整数, 主键, 索引
    user = Column(String, index=True)  # user, 字符串, 索引
    mood = Column(String)
    message = Column(String, nullable=True)  # 可选字段
    created_at = Column(DateTime, default=datetime.datetime.now)  # 创建时间

# ==================== Pydantic模型 (schemas.py 内容) ====================

# 创建心情记录时，用户只需要提供这些字段
class MoodCreate(BaseModel):
    user: str                # user字段必须是字符串
    mood: str                # mood字段必须是字符串
    message: Optional[str] = None  # message字段是可选的字符串

# API响应时，需要返回完整的记录信息
class MoodResponse(BaseModel):
    id: int
    user: str
    mood: str
    message: Optional[str] = None
    created_at: datetime.datetime

    class Config:
        orm_mode = True  # 允许模型从ORM对象（如Mood）直接转换

# ==================== FastAPI应用 ====================

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(title="心情记录系统 API - 数据库版本")

# ==================== API端点 ====================

@app.post("/moods", response_model=MoodResponse)
def create_mood(mood_data: MoodCreate, db: Session = Depends(get_db)):
    """
    创建一条新的心情记录
    - **user**: 用户名
    - **mood**: 心情状态
    - **message**: 可选的心情描述
    """
    # 1. 根据Pydantic模型创建SQLAlchemy模型实例
    db_mood = Mood(**mood_data.dict())
    # 2. 将这个实例添加到会话中（准备写入）
    db.add(db_mood)
    # 3. 提交会话，将更改真正写入数据库
    db.commit()
    # 4. 刷新实例，从数据库获取最新的数据（比如自动生成的id）
    db.refresh(db_mood)
    return db_mood

@app.get("/moods", response_model=List[MoodResponse])
def get_moods(db: Session = Depends(get_db)):
    """
    获取所有心情记录
    """
    # 使用db.query()来查询Mood模型对应的表，.all()表示获取所有结果
    moods = db.query(Mood).all()
    return moods

@app.get("/moods/{mood_id}", response_model=MoodResponse)
def get_mood(mood_id: int, db: Session = Depends(get_db)):
    """
    根据ID获取单个心情记录
    - **mood_id**: 心情记录的ID
    """
    # .filter()方法相当于SQL中的WHERE子句，.first()表示只取第一个结果
    mood = db.query(Mood).filter(Mood.id == mood_id).first()
    if mood is None:
        raise HTTPException(status_code=404, detail="心情记录未找到")
    return mood

@app.get("/moods/user/{user_name}", response_model=List[MoodResponse])
def get_user_moods(user_name: str, db: Session = Depends(get_db)):
    """
    获取指定用户的所有心情记录
    - **user_name**: 要查询的用户名
    """
    user_moods = db.query(Mood).filter(Mood.user == user_name).all()
    if not user_moods:
        raise HTTPException(status_code=404, detail=f"用户 '{user_name}' 未找到任何心情记录")
    return user_moods

@app.put("/moods/{mood_id}", response_model=MoodResponse)
def update_mood(mood_id: int, mood_data: MoodCreate, db: Session = Depends(get_db)):
    """
    更新指定ID的心情记录
    - **mood_id**: 要更新的心情记录ID
    - **mood_data**: 新的心情数据
    """
    mood = db.query(Mood).filter(Mood.id == mood_id).first()
    if mood is None:
        raise HTTPException(status_code=404, detail="心情记录未找到")
    
    # 更新字段
    mood.user = mood_data.user
    mood.mood = mood_data.mood
    mood.message = mood_data.message
    
    db.commit()
    db.refresh(mood)
    return mood

@app.delete("/moods/{mood_id}")
def delete_mood(mood_id: int, db: Session = Depends(get_db)):
    """
    删除指定ID的心情记录
    - **mood_id**: 要删除的心情记录ID
    """
    mood = db.query(Mood).filter(Mood.id == mood_id).first()
    if mood is None:
        raise HTTPException(status_code=404, detail="心情记录未找到")
    
    db.delete(mood)  # 删除实例
    db.commit()      # 提交更改
    return {"message": "心情记录已删除"}

# ==================== 启动说明 ====================

if __name__ == "__main__":
    import uvicorn
    print("启动心情记录API服务器...")
    print("访问 http://localhost:8000/docs 查看API文档")
    print("数据将持久化保存在 moods.db 文件中")
    uvicorn.run(app, host="0.0.0.0", port=8000)
