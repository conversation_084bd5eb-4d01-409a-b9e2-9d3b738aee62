import json
import ipaddress
from datetime import datetime

class NetworkGrader:
    def __init__(self):
        self.max_score = 100
        self.criteria = {
            'device_count': 20,
            'connectivity': 30,
            'ip_configuration': 25,
            'routing_complexity': 15,
            'network_design': 10
        }
    
    def grade_environment(self, environment):
        scores = {}
        total_score = 0
        
        topology = environment.get_topology()
        devices = topology['devices']
        connections = topology['connections']
        
        scores['device_count'] = self._grade_device_count(devices)
        scores['connectivity'] = self._grade_connectivity(devices, connections)
        scores['ip_configuration'] = self._grade_ip_configuration(devices)
        scores['routing_complexity'] = self._grade_routing_complexity(devices)
        scores['network_design'] = self._grade_network_design(devices, connections)
        
        for criterion, weight in self.criteria.items():
            total_score += scores[criterion] * (weight / 100)
        
        return {
            'total_score': round(total_score, 2),
            'detailed_scores': scores,
            'feedback': self._generate_feedback(scores),
            'timestamp': datetime.now().isoformat()
        }
    
    def _grade_device_count(self, devices):
        device_count = len(devices)
        router_count = sum(1 for d in devices.values() if d['type'] == 'router')
        client_count = sum(1 for d in devices.values() if d['type'] == 'client')
        
        if device_count < 3:
            return 30
        elif device_count < 5:
            return 60
        elif device_count < 8:
            return 85
        else:
            return 100
    
    def _grade_connectivity(self, devices, connections):
        if len(devices) < 2:
            return 0
        
        device_ids = set(devices.keys())
        connected_devices = set()
        
        for conn in connections:
            connected_devices.add(conn['device1'])
            connected_devices.add(conn['device2'])
        
        connectivity_ratio = len(connected_devices) / len(device_ids)
        
        if connectivity_ratio >= 0.8:
            return 100
        elif connectivity_ratio >= 0.6:
            return 80
        elif connectivity_ratio >= 0.4:
            return 60
        elif connectivity_ratio >= 0.2:
            return 40
        else:
            return 20
    
    def _grade_ip_configuration(self, devices):
        configured_devices = 0
        valid_configurations = 0
        subnet_diversity = set()
        
        for device in devices.values():
            interfaces = device.get('interfaces', {})
            if interfaces:
                configured_devices += 1
                
                for iface, config in interfaces.items():
                    try:
                        ip = ipaddress.IPv4Address(config['ip'])
                        mask = config['mask']
                        
                        if mask.isdigit():
                            prefix_len = int(mask)
                        else:
                            prefix_len = ipaddress.IPv4Network(f"0.0.0.0/{mask}").prefixlen
                        
                        network = ipaddress.IPv4Network(f"{config['ip']}/{prefix_len}", strict=False)
                        subnet_diversity.add(str(network.network_address))
                        valid_configurations += 1
                        
                    except (ipaddress.AddressValueError, ValueError):
                        pass
        
        if len(devices) == 0:
            return 0
        
        config_ratio = configured_devices / len(devices)
        subnet_bonus = min(len(subnet_diversity) * 10, 30)
        
        base_score = config_ratio * 70
        return min(base_score + subnet_bonus, 100)
    
    def _grade_routing_complexity(self, devices):
        total_routes = 0
        devices_with_routes = 0
        
        for device in devices.values():
            if device['type'] == 'router':
                route_count = len(device.get('routing_table', []))
                if route_count > 0:
                    devices_with_routes += 1
                    total_routes += route_count
        
        router_count = sum(1 for d in devices.values() if d['type'] == 'router')
        
        if router_count == 0:
            return 0
        
        avg_routes = total_routes / router_count if router_count > 0 else 0
        
        if avg_routes >= 3:
            return 100
        elif avg_routes >= 2:
            return 80
        elif avg_routes >= 1:
            return 60
        elif devices_with_routes > 0:
            return 40
        else:
            return 20
    
    def _grade_network_design(self, devices, connections):
        router_count = sum(1 for d in devices.values() if d['type'] == 'router')
        client_count = sum(1 for d in devices.values() if d['type'] == 'client')
        
        if router_count == 0 or client_count == 0:
            return 30
        
        ratio = min(client_count / router_count, router_count / client_count)
        
        if ratio >= 0.5:
            return 100
        elif ratio >= 0.3:
            return 80
        elif ratio >= 0.2:
            return 60
        else:
            return 40
    
    def _generate_feedback(self, scores):
        feedback = []
        
        if scores['device_count'] < 70:
            feedback.append("建议添加更多网络设备来增加网络复杂度")
        
        if scores['connectivity'] < 70:
            feedback.append("部分设备未连接，检查网络连接")
        
        if scores['ip_configuration'] < 70:
            feedback.append("需要为更多设备配置IP地址")
        
        if scores['routing_complexity'] < 70:
            feedback.append("路由器需要配置更多路由规则")
        
        if scores['network_design'] < 70:
            feedback.append("网络设计需要更好的路由器和客户端平衡")
        
        if not feedback:
            feedback.append("网络配置良好！")
        
        return feedback

class TeacherDashboard:
    def __init__(self, user_environments):
        self.user_environments = user_environments
        self.grader = NetworkGrader()
    
    def get_all_scores(self):
        results = {}
        for user_id, env in self.user_environments.items():
            username = user_id.replace('user_', '')
            results[username] = self.grader.grade_environment(env)
        return results
    
    def get_user_score(self, username):
        user_id = f"user_{username}"
        if user_id in self.user_environments:
            return self.grader.grade_environment(self.user_environments[user_id])
        return None
    
    def export_scores_csv(self):
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        writer.writerow(['用户名', '总分', '设备数量', '连通性', 'IP配置', '路由复杂度', '网络设计', '时间'])
        
        all_scores = self.get_all_scores()
        for username, score_data in all_scores.items():
            scores = score_data['detailed_scores']
            writer.writerow([
                username,
                score_data['total_score'],
                scores['device_count'],
                scores['connectivity'],
                scores['ip_configuration'],
                scores['routing_complexity'],
                scores['network_design'],
                score_data['timestamp']
            ])
        
        return output.getvalue()

def create_test_scenarios():
    scenarios = [
        {
            'name': '基础连接测试',
            'description': '创建2个路由器和4个客户端，确保所有设备互联',
            'requirements': {
                'min_routers': 2,
                'min_clients': 4,
                'min_connections': 5
            }
        },
        {
            'name': '子网配置测试',
            'description': '配置至少3个不同的子网',
            'requirements': {
                'min_subnets': 3,
                'min_configured_devices': 6
            }
        },
        {
            'name': '复杂网络测试',
            'description': '创建包含多个路由器和子网的复杂网络拓扑',
            'requirements': {
                'min_routers': 3,
                'min_clients': 6,
                'min_subnets': 4,
                'min_routes': 6
            }
        }
    ]
    return scenarios
