#!/usr/bin/env python3

import subprocess
import sys
import os

def check_dependencies():
    try:
        import flask
        print("Flask已安装")
        return True
    except ImportError:
        print("Flask未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("依赖安装完成")
            return True
        except subprocess.CalledProcessError:
            print("依赖安装失败")
            return False

def main():
    print("网络模拟器启动脚本")
    print("=" * 30)
    
    if not check_dependencies():
        print("请手动安装依赖: pip install -r requirements.txt")
        return
    
    print("启动网络模拟器服务...")
    print("访问地址:")
    print("- 学生端: http://localhost:8080")
    print("- 教师端: http://localhost:8080/teacher")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 30)
    
    try:
        from network_simulator import app
        app.run(host='0.0.0.0', port=8080, debug=False)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
