#!/usr/bin/env python3

import requests
import json

BASE_URL = "http://localhost:8080"

def test_ping_randomness():
    print("测试ping值随机化...")
    
    # 登录
    session = requests.Session()
    login_response = session.post(f"{BASE_URL}/login", json={"username": "ping_test"})
    if login_response.status_code != 200:
        print("✗ 登录失败")
        return
    
    # 添加设备
    device_response = session.post(f"{BASE_URL}/api/add_device", 
                                 json={"type": "router", "name": "测试路由器", "x": 100, "y": 100})
    if device_response.status_code != 200:
        print("✗ 添加设备失败")
        return
    
    device_id = device_response.json()['device']['id']
    
    # 配置接口
    config_response = session.post(f"{BASE_URL}/api/configure_interface",
                                 json={
                                     "device_id": device_id,
                                     "interface": "eth0",
                                     "ip_address": "***********",
                                     "subnet_mask": "24"
                                 })
    if config_response.status_code != 200:
        print("✗ 配置接口失败")
        return
    
    # 执行多次ping命令，检查结果是否随机
    ping_results = []
    for i in range(5):
        ping_response = session.post(f"{BASE_URL}/api/execute_command",
                                   json={
                                       "device_id": device_id,
                                       "command": "ping *******"
                                   })
        if ping_response.status_code == 200:
            output = ping_response.json()['output']
            ping_results.append(output)
            print(f"  Ping {i+1}: {output}")
        else:
            print(f"✗ Ping {i+1} 失败")
            return
    
    # 检查结果是否不同（随机性）
    unique_results = set(ping_results)
    if len(unique_results) > 1:
        print("✓ Ping值随机化成功 - 检测到不同的时间值")
    else:
        print("⚠ Ping值可能不够随机 - 所有结果相同")
    
    print(f"总共执行了 {len(ping_results)} 次ping，得到 {len(unique_results)} 个不同结果")

if __name__ == "__main__":
    try:
        test_ping_randomness()
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保服务器正在运行在 http://localhost:8080")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
