<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师端 - 网络模拟器</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .teacher-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .score-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .score-table th,
        .score-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .score-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .score-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .score-good {
            color: #28a745;
            font-weight: bold;
        }
        
        .score-medium {
            color: #ffc107;
            font-weight: bold;
        }
        
        .score-poor {
            color: #dc3545;
            font-weight: bold;
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .controls button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background-color: #0056b3;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .feedback-list {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .feedback-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feedback-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>教师端 - 网络模拟器</h1>
        <a href="/" style="color: white; text-decoration: none;">返回首页</a>
    </div>
    
    <div class="teacher-container">
        <div class="controls">
            <button onclick="refreshScores()">刷新分数</button>
            <button onclick="exportScores()">导出CSV</button>
            <span>自动刷新: <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()"></span>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalStudents">0</div>
                <div>总学生数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgScore">0</div>
                <div>平均分</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="highScores">0</div>
                <div>优秀(≥80分)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="lowScores">0</div>
                <div>需改进(<60分)</div>
            </div>
        </div>
        
        <table class="score-table">
            <thead>
                <tr>
                    <th>学生姓名</th>
                    <th>总分</th>
                    <th>设备数量</th>
                    <th>连通性</th>
                    <th>IP配置</th>
                    <th>路由复杂度</th>
                    <th>网络设计</th>
                    <th>提交时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="scoresTableBody">
            </tbody>
        </table>
        
        <div id="detailModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 600px; width: 90%;">
                <h3 id="detailTitle">学生详情</h3>
                <div id="detailContent"></div>
                <button onclick="closeDetail()" style="margin-top: 20px; padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">关闭</button>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        
        async function refreshScores() {
            try {
                const response = await fetch('/api/teacher/scores');
                const scores = await response.json();
                updateScoresTable(scores);
                updateStats(scores);
            } catch (error) {
                console.error('获取分数失败:', error);
            }
        }
        
        function updateScoresTable(scores) {
            const tbody = document.getElementById('scoresTableBody');
            tbody.innerHTML = '';
            
            for (const [username, scoreData] of Object.entries(scores)) {
                const row = document.createElement('tr');
                const detailedScores = scoreData.detailed_scores;
                
                row.innerHTML = `
                    <td>${username}</td>
                    <td class="${getScoreClass(scoreData.total_score)}">${scoreData.total_score}</td>
                    <td>${detailedScores.device_count.toFixed(1)}</td>
                    <td>${detailedScores.connectivity.toFixed(1)}</td>
                    <td>${detailedScores.ip_configuration.toFixed(1)}</td>
                    <td>${detailedScores.routing_complexity.toFixed(1)}</td>
                    <td>${detailedScores.network_design.toFixed(1)}</td>
                    <td>${new Date(scoreData.timestamp).toLocaleString()}</td>
                    <td><button onclick="showDetail('${username}')" style="padding: 5px 10px; background: #17a2b8; color: white; border: none; border-radius: 3px; cursor: pointer;">详情</button></td>
                `;
                
                tbody.appendChild(row);
            }
        }
        
        function updateStats(scores) {
            const totalStudents = Object.keys(scores).length;
            const totalScore = Object.values(scores).reduce((sum, s) => sum + s.total_score, 0);
            const avgScore = totalStudents > 0 ? (totalScore / totalStudents).toFixed(1) : 0;
            const highScores = Object.values(scores).filter(s => s.total_score >= 80).length;
            const lowScores = Object.values(scores).filter(s => s.total_score < 60).length;
            
            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('avgScore').textContent = avgScore;
            document.getElementById('highScores').textContent = highScores;
            document.getElementById('lowScores').textContent = lowScores;
        }
        
        function getScoreClass(score) {
            if (score >= 80) return 'score-good';
            if (score >= 60) return 'score-medium';
            return 'score-poor';
        }
        
        async function showDetail(username) {
            try {
                const response = await fetch(`/api/teacher/score/${username}`);
                const scoreData = await response.json();
                
                document.getElementById('detailTitle').textContent = `${username} - 详细分析`;
                
                const content = `
                    <h4>分数详情</h4>
                    <p><strong>总分:</strong> ${scoreData.total_score}</p>
                    <p><strong>设备数量:</strong> ${scoreData.detailed_scores.device_count.toFixed(1)}</p>
                    <p><strong>连通性:</strong> ${scoreData.detailed_scores.connectivity.toFixed(1)}</p>
                    <p><strong>IP配置:</strong> ${scoreData.detailed_scores.ip_configuration.toFixed(1)}</p>
                    <p><strong>路由复杂度:</strong> ${scoreData.detailed_scores.routing_complexity.toFixed(1)}</p>
                    <p><strong>网络设计:</strong> ${scoreData.detailed_scores.network_design.toFixed(1)}</p>
                    
                    <h4>改进建议</h4>
                    <div class="feedback-list">
                        ${scoreData.feedback.map(f => `<div class="feedback-item">${f}</div>`).join('')}
                    </div>
                `;
                
                document.getElementById('detailContent').innerHTML = content;
                document.getElementById('detailModal').style.display = 'block';
            } catch (error) {
                console.error('获取详情失败:', error);
            }
        }
        
        function closeDetail() {
            document.getElementById('detailModal').style.display = 'none';
        }
        
        function toggleAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            if (checkbox.checked) {
                autoRefreshInterval = setInterval(refreshScores, 10000);
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
            }
        }
        
        function exportScores() {
            window.open('/api/teacher/export', '_blank');
        }
        
        window.addEventListener('load', refreshScores);
    </script>
</body>
</html>
