<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络模拟器</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="container">
        <div class="login-box">
            <h1>网络模拟器</h1>
            <p>请输入用户名开始学习</p>
            <form id="loginForm">
                <input type="text" id="username" placeholder="输入用户名" required>
                <button type="submit">开始</button>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('username').value;
            
            const response = await fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username })
            });
            
            const result = await response.json();
            if (result.success) {
                window.location.href = '/simulator';
            } else {
                alert('登录失败，请重试');
            }
        });
    </script>
</body>
</html>
