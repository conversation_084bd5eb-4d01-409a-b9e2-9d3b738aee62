#!/usr/bin/env python3

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_login():
    print("测试用户登录...")
    response = requests.post(f"{BASE_URL}/login", 
                           json={"username": "test_student"})
    if response.status_code == 200:
        print("✓ 登录成功")
        return response.cookies
    else:
        print("✗ 登录失败")
        return None

def test_add_device(cookies):
    print("测试添加设备...")
    response = requests.post(f"{BASE_URL}/api/add_device",
                           json={"type": "router", "name": "测试路由器", "x": 100, "y": 100},
                           cookies=cookies)
    if response.status_code == 200:
        print("✓ 添加设备成功")
        return response.json()['device']['id']
    else:
        print("✗ 添加设备失败")
        return None

def test_configure_interface(cookies, device_id):
    print("测试配置接口...")
    response = requests.post(f"{BASE_URL}/api/configure_interface",
                           json={
                               "device_id": device_id,
                               "interface": "eth0",
                               "ip_address": "***********",
                               "subnet_mask": "24"
                           },
                           cookies=cookies)
    if response.status_code == 200:
        print("✓ 配置接口成功")
        return True
    else:
        print("✗ 配置接口失败")
        return False

def test_execute_command(cookies, device_id):
    print("测试执行命令...")
    response = requests.post(f"{BASE_URL}/api/execute_command",
                           json={
                               "device_id": device_id,
                               "command": "ip a"
                           },
                           cookies=cookies)
    if response.status_code == 200:
        print("✓ 执行命令成功")
        print(f"  输出: {response.json()['output']}")
        return True
    else:
        print("✗ 执行命令失败")
        return False

def test_templates(cookies):
    print("测试网络模板...")
    response = requests.get(f"{BASE_URL}/api/templates", cookies=cookies)
    if response.status_code == 200:
        print("✓ 获取模板列表成功")
        templates = response.json()['templates']
        if templates:
            template_name = templates[0]
            apply_response = requests.post(f"{BASE_URL}/api/template/{template_name}/apply",
                                         cookies=cookies)
            if apply_response.status_code == 200:
                print(f"✓ 应用模板 {template_name} 成功")
                return True
        return True
    else:
        print("✗ 获取模板列表失败")
        return False

def test_scenarios(cookies):
    print("测试练习场景...")
    response = requests.get(f"{BASE_URL}/api/scenarios", cookies=cookies)
    if response.status_code == 200:
        print("✓ 获取场景列表成功")
        scenarios = response.json()
        if scenarios:
            scenario_level = list(scenarios.keys())[0]
            check_response = requests.get(f"{BASE_URL}/api/scenario/{scenario_level}/check",
                                        cookies=cookies)
            if check_response.status_code == 200:
                print(f"✓ 检查场景 {scenario_level} 成功")
                return True
        return True
    else:
        print("✗ 获取场景列表失败")
        return False

def test_validation(cookies):
    print("测试网络验证...")
    response = requests.get(f"{BASE_URL}/api/validate", cookies=cookies)
    if response.status_code == 200:
        print("✓ 网络验证成功")
        result = response.json()
        print(f"  网络有效性: {result['valid']}")
        if result['issues']:
            print(f"  问题: {result['issues']}")
        if result['warnings']:
            print(f"  警告: {result['warnings']}")
        return True
    else:
        print("✗ 网络验证失败")
        return False

def test_connect_devices(cookies, device1_id, device2_id):
    print("测试设备连接...")
    response = requests.post(f"{BASE_URL}/api/connect_devices",
                           json={
                               "device1_id": device1_id,
                               "device2_id": device2_id,
                               "interface1": "port0",
                               "interface2": "port0"
                           },
                           cookies=cookies)
    if response.status_code == 200:
        print("✓ 设备连接成功")
        return True
    else:
        print("✗ 设备连接失败")
        return False

def test_disconnect_devices(cookies, device1_id, device2_id):
    print("测试设备断开...")
    response = requests.post(f"{BASE_URL}/api/disconnect_devices",
                           json={
                               "device1_id": device1_id,
                               "device2_id": device2_id,
                               "interface1": "port0",
                               "interface2": "port0"
                           },
                           cookies=cookies)
    if response.status_code == 200:
        print("✓ 设备断开成功")
        return True
    else:
        print("✗ 设备断开失败")
        return False

def test_teacher_dashboard():
    print("测试教师端...")
    response = requests.get(f"{BASE_URL}/api/teacher/scores")
    if response.status_code == 200:
        print("✓ 获取学生分数成功")
        scores = response.json()
        print(f"  当前学生数: {len(scores)}")
        return True
    else:
        print("✗ 获取学生分数失败")
        return False

def main():
    print("网络模拟器系统测试")
    print("=" * 40)
    
    cookies = test_login()
    if not cookies:
        return
    
    device1_id = test_add_device(cookies)
    device2_id = None
    if device1_id:
        test_configure_interface(cookies, device1_id)
        test_execute_command(cookies, device1_id)

        device2_id = test_add_device(cookies)
        if device2_id:
            if test_connect_devices(cookies, device1_id, device2_id):
                test_disconnect_devices(cookies, device1_id, device2_id)
    
    test_templates(cookies)
    test_scenarios(cookies)
    test_validation(cookies)
    test_teacher_dashboard()
    
    print("=" * 40)
    print("测试完成！")

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保服务器正在运行在 http://localhost:8080")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
