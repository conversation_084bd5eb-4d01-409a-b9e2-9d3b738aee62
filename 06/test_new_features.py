#!/usr/bin/env python3

import requests
import json

BASE_URL = "http://localhost:8080"

def test_dhcp_functionality():
    print("测试DHCP功能...")
    
    session = requests.Session()
    login_response = session.post(f"{BASE_URL}/login", json={"username": "dhcp_test"})
    if login_response.status_code != 200:
        print("✗ 登录失败")
        return
    
    # 添加设备
    device_response = session.post(f"{BASE_URL}/api/add_device", 
                                 json={"type": "client", "name": "DHCP客户端", "x": 100, "y": 100})
    if device_response.status_code != 200:
        print("✗ 添加设备失败")
        return
    
    device_id = device_response.json()['device']['id']
    
    # 配置DHCP接口
    config_response = session.post(f"{BASE_URL}/api/configure_interface",
                                 json={
                                     "device_id": device_id,
                                     "interface": "port0",
                                     "ip_address": "DHCP",
                                     "subnet_mask": "DHCP",
                                     "dhcp": True
                                 })
    if config_response.status_code == 200:
        result = config_response.json()
        if result['success']:
            print(f"✓ DHCP配置成功")
            print(f"  分配的IP: {result.get('ip_address', 'N/A')}")
            print(f"  子网掩码: {result.get('subnet_mask', 'N/A')}")
        else:
            print("✗ DHCP配置失败")
    else:
        print("✗ DHCP配置请求失败")

def test_enhanced_ping():
    print("\n测试增强的ping功能...")
    
    session = requests.Session()
    login_response = session.post(f"{BASE_URL}/login", json={"username": "ping_test"})
    if login_response.status_code != 200:
        print("✗ 登录失败")
        return
    
    # 添加设备
    device_response = session.post(f"{BASE_URL}/api/add_device", 
                                 json={"type": "router", "name": "测试路由器", "x": 100, "y": 100})
    if device_response.status_code != 200:
        print("✗ 添加设备失败")
        return
    
    device_id = device_response.json()['device']['id']
    
    # 配置接口
    config_response = session.post(f"{BASE_URL}/api/configure_interface",
                                 json={
                                     "device_id": device_id,
                                     "interface": "port0",
                                     "ip_address": "***********",
                                     "subnet_mask": "24",
                                     "dhcp": False
                                 })
    if config_response.status_code != 200:
        print("✗ 配置接口失败")
        return
    
    # 测试ping本地地址
    print("  测试ping本地地址 (127.0.0.1):")
    ping_response = session.post(f"{BASE_URL}/api/execute_command",
                               json={
                                   "device_id": device_id,
                                   "command": "ping 127.0.0.1"
                               })
    if ping_response.status_code == 200:
        output = ping_response.json()['output']
        print(f"    {output[:100]}...")  # 只显示前100个字符
        if "0.1" in output or "1." in output:  # 检查是否有本地地址的低延迟
            print("    ✓ 本地地址ping延迟正常 (低延迟)")
        else:
            print("    ⚠ 本地地址ping延迟可能不正确")
    
    # 测试ping远程地址
    print("  测试ping远程地址 (8.8.8.8):")
    ping_response = session.post(f"{BASE_URL}/api/execute_command",
                               json={
                                   "device_id": device_id,
                                   "command": "ping 8.8.8.8"
                               })
    if ping_response.status_code == 200:
        output = ping_response.json()['output']
        print(f"    {output[:100]}...")  # 只显示前100个字符
        if "4 packets transmitted, 4 received" in output:
            print("    ✓ 远程地址ping返回4个结果")
        else:
            print("    ⚠ 远程地址ping结果格式可能不正确")
    
    # 测试ping自己的接口地址
    print("  测试ping自己的接口地址 (***********):")
    ping_response = session.post(f"{BASE_URL}/api/execute_command",
                               json={
                                   "device_id": device_id,
                                   "command": "ping ***********"
                               })
    if ping_response.status_code == 200:
        output = ping_response.json()['output']
        print(f"    {output[:100]}...")  # 只显示前100个字符
        if "0.1" in output or "1." in output:  # 检查是否有本地地址的低延迟
            print("    ✓ 自己接口地址ping延迟正常 (低延迟)")
        else:
            print("    ⚠ 自己接口地址ping延迟可能不正确")

def main():
    print("新功能测试")
    print("=" * 40)
    
    test_dhcp_functionality()
    test_enhanced_ping()
    
    print("=" * 40)
    print("测试完成！")

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保服务器正在运行在 http://localhost:8080")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
