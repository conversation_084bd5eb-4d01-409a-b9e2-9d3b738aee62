<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接线测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            width: 800px;
            height: 600px;
            border: 1px solid #ccc;
            position: relative;
            background-color: #f9f9f9;
        }
        
        .test-device {
            position: absolute;
            width: 80px;
            height: 60px;
            background-color: #ffeb3b;
            border: 2px solid #333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .test-port {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: #007bff;
            border-radius: 50%;
            border: 2px solid #fff;
            cursor: crosshair;
            z-index: 10;
        }
        
        .test-port:hover {
            background-color: #ff5722;
            transform: scale(1.3);
        }
        
        .test-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: auto;
            z-index: 1;
        }
        
        .test-line {
            stroke: #333333;
            stroke-width: 2;
            fill: none;
            cursor: pointer;
        }
        
        .test-line:hover {
            stroke: #ff5722;
            stroke-width: 3;
        }
    </style>
</head>
<body>
    <h1>连接线渲染测试</h1>
    <p>这个页面用于测试SVG连接线的渲染效果</p>
    
    <div class="test-container" id="testContainer">
        <svg class="test-svg" id="testSvg">
            <!-- 连接线将在这里渲染 -->
        </svg>
        
        <!-- 设备1 -->
        <div class="test-device" style="left: 100px; top: 100px;">
            路由器1
            <div class="test-port" style="right: -6px; top: 26px;" data-device="1" data-port="right"></div>
        </div>
        
        <!-- 设备2 -->
        <div class="test-device" style="left: 300px; top: 200px;">
            路由器2
            <div class="test-port" style="left: -6px; top: 26px;" data-device="2" data-port="left"></div>
        </div>
        
        <!-- 设备3 -->
        <div class="test-device" style="left: 500px; top: 150px;">
            客户端1
            <div class="test-port" style="left: -6px; top: 26px;" data-device="3" data-port="left"></div>
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <button onclick="drawTestConnections()">绘制测试连接线</button>
        <button onclick="clearTestConnections()">清除连接线</button>
    </div>
    
    <script>
        function drawTestConnections() {
            const svg = document.getElementById('testSvg');
            const container = document.getElementById('testContainer');
            
            // 清除现有连接线
            svg.innerHTML = '';
            
            // 获取端口位置
            const port1 = document.querySelector('[data-device="1"][data-port="right"]');
            const port2 = document.querySelector('[data-device="2"][data-port="left"]');
            const port3 = document.querySelector('[data-device="3"][data-port="left"]');
            
            const containerRect = container.getBoundingClientRect();
            
            function getPortCenter(port) {
                const rect = port.getBoundingClientRect();
                return {
                    x: rect.left - containerRect.left + 4,
                    y: rect.top - containerRect.top + 4
                };
            }
            
            const pos1 = getPortCenter(port1);
            const pos2 = getPortCenter(port2);
            const pos3 = getPortCenter(port3);
            
            // 创建连接线1: 路由器1 -> 路由器2
            const line1 = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line1.setAttribute('x1', pos1.x);
            line1.setAttribute('y1', pos1.y);
            line1.setAttribute('x2', pos2.x);
            line1.setAttribute('y2', pos2.y);
            line1.className = 'test-line';
            line1.addEventListener('click', () => alert('点击了连接线1'));
            svg.appendChild(line1);
            
            // 创建连接线2: 路由器2 -> 客户端1
            const line2 = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line2.setAttribute('x1', pos2.x);
            line2.setAttribute('y1', pos2.y);
            line2.setAttribute('x2', pos3.x);
            line2.setAttribute('y2', pos3.y);
            line2.className = 'test-line';
            line2.addEventListener('click', () => alert('点击了连接线2'));
            svg.appendChild(line2);
            
            console.log('测试连接线已绘制');
            console.log('连接线1:', pos1, '->', pos2);
            console.log('连接线2:', pos2, '->', pos3);
        }
        
        function clearTestConnections() {
            const svg = document.getElementById('testSvg');
            svg.innerHTML = '';
            console.log('连接线已清除');
        }
    </script>
</body>
</html>
